import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTAyOTUzMywiZXhwIjoyMDU2NjA1NTMzfQ.2m5CgPGaWNdtxczMbEyRKx2kkdvLbJFHw3eKE2wGNp4'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkDbState() {
  try {
    console.log('🔍 Checking database state...')
    
    // Check profiles table
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(10)
    
    console.log('Profiles query error:', profilesError)
    console.log('Profiles result:', profiles)
    console.log('')

    // Check team_members table
    const { data: teamMembers, error: teamMembersError } = await supabase
      .from('team_members')
      .select('*')
      .limit(10)
    
    console.log('Team members query error:', teamMembersError)
    console.log('Team members result:', teamMembers)
    console.log('')

    // Check teams table
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .limit(10)
    
    console.log('Teams query error:', teamsError)
    console.log('Teams result:', teams)
    console.log('')

    // Try with RPC to list tables
    const { data: tables, error: tablesError } = await supabase
      .rpc('get_tables_list')
    
    console.log('Tables RPC error:', tablesError)
    console.log('Tables result:', tables)

  } catch (error) {
    console.error('❌ Error checking database state:', error)
  }
}

checkDbState().catch(console.error)