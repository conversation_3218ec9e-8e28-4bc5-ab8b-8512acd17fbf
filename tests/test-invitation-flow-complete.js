import { createClient } from '@supabase/supabase-js'
import puppeteer from 'puppeteer'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

const APP_URL = 'http://localhost:8080'

async function testInvitationFlowComplete(userEmail, userPassword) {
  let browser
  let page
  
  try {
    console.log('🚀 Starting comprehensive invitation flow test...')
    
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: false, 
      defaultViewport: { width: 1280, height: 720 },
      slowMo: 500 // Slow down actions for visibility
    })
    page = await browser.newPage()
    
    // Step 1: Navigate to the app
    console.log('\n1. Navigating to the application...')
    await page.goto(APP_URL, { waitUntil: 'networkidle0' })
    console.log('✅ Application loaded')
    
    // Step 2: Try to login with existing credentials
    console.log('\n2. Attempting to login...')
    
    // Wait for login form to appear
    await page.waitForSelector('input[type="email"]', { timeout: 10000 })
    
    // Fill in credentials
    await page.type('input[type="email"]', userEmail)
    await page.type('input[type="password"]', userPassword)
    
    // Click login button
    await page.click('button[type="submit"]')
    
    // Wait for navigation or error
    await page.waitForTimeout(3000)
    
    // Check if we're logged in by looking for dashboard elements
    const currentUrl = page.url()
    console.log('Current URL after login attempt:', currentUrl)
    
    if (currentUrl.includes('dashboard') || currentUrl.includes('teams')) {
      console.log('✅ Successfully logged in')
      
      // Step 3: Check profile information
      console.log('\n3. Checking profile information...')
      
      // Look for profile elements
      const profileElements = await page.$$('[data-testid*="profile"], .profile, [class*="profile"]')
      console.log('Profile elements found:', profileElements.length)
      
      // Check if user name is displayed
      const userNameElements = await page.$$eval('*', elements => 
        elements.filter(el => el.textContent && el.textContent.match(/[A-Z][a-z]+ [A-Z][a-z]+/))
          .map(el => el.textContent.trim())
          .slice(0, 5)
      )
      console.log('Potential user name elements:', userNameElements)
      
      // Step 4: Check team data
      console.log('\n4. Checking team data access...')
      
      // Navigate to teams page if not already there
      if (!currentUrl.includes('teams')) {
        try {
          await page.click('a[href*="teams"], button[aria-label*="team"], [data-testid*="team"]')
          await page.waitForTimeout(2000)
        } catch (error) {
          console.log('Could not find teams navigation, trying to navigate directly')
          await page.goto(`${APP_URL}/#/teams`, { waitUntil: 'networkidle0' })
        }
      }
      
      // Look for team elements
      const teamElements = await page.$$('[data-testid*="team"], .team, [class*="team"]')
      console.log('Team-related elements found:', teamElements.length)
      
      // Check for "no teams" or team data
      const pageText = await page.evaluate(() => document.body.textContent)
      const hasTeamData = pageText.includes('team') || pageText.includes('Team')
      const hasNoDataMessage = pageText.includes('No teams') || pageText.includes('no data') || pageText.includes('empty')
      
      console.log('Has team-related text:', hasTeamData)
      console.log('Has no data message:', hasNoDataMessage)
      
      // Step 5: Check properties/maintenance data
      console.log('\n5. Checking properties and maintenance data...')
      
      // Try to navigate to properties
      try {
        await page.click('a[href*="properties"], a[href*="dashboard"]')
        await page.waitForTimeout(2000)
        
        const propertiesText = await page.evaluate(() => document.body.textContent)
        const hasPropertiesData = propertiesText.includes('property') || propertiesText.includes('Property')
        console.log('Has properties data:', hasPropertiesData)
        
      } catch (error) {
        console.log('Could not navigate to properties page')
      }
      
      // Step 6: Database verification
      console.log('\n6. Verifying database state...')
      
      // Get user profile from database
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', userEmail)
        .single()
      
      if (profileError) {
        console.error('❌ Error fetching user profile:', profileError)
      } else {
        console.log('✅ User profile in database:', {
          id: profiles.id,
          email: profiles.email,
          first_name: profiles.first_name,
          last_name: profiles.last_name,
          role: profiles.role
        })
      }
      
      // Get user teams
      const { data: userTeams, error: teamsError } = await supabase
        .rpc('get_user_teams', { user_id: profiles?.id })
      
      if (teamsError) {
        console.error('❌ Error fetching user teams:', teamsError)
      } else {
        console.log('✅ User teams in database:', userTeams?.length || 0, 'teams')
        userTeams?.forEach(team => console.log(`  - ${team.team_name} (${team.user_role})`))
      }
      
      // Get team memberships
      const { data: memberships, error: membershipError } = await supabase
        .from('team_members')
        .select('*, teams!inner(name)')
        .eq('user_id', profiles?.id)
      
      if (membershipError) {
        console.error('❌ Error fetching team memberships:', membershipError)
      } else {
        console.log('✅ Team memberships in database:', memberships?.length || 0, 'memberships')
        memberships?.forEach(m => console.log(`  - Team: ${m.teams.name}, Role: ${m.role}`))
      }
      
    } else {
      console.log('❌ Login failed or redirected to unexpected page')
      
      // Check for error messages
      const errorMessages = await page.$$eval('[class*="error"], [class*="alert"], .toast', 
        elements => elements.map(el => el.textContent.trim())
      )
      console.log('Error messages:', errorMessages)
    }
    
    // Step 7: Test invitation creation (if user has permissions)
    console.log('\n7. Testing invitation creation...')
    
    try {
      // Look for invite button or team management
      const inviteButtons = await page.$$('button:has-text("invite"), button:has-text("Invite"), a:has-text("invite")')
      if (inviteButtons.length > 0) {
        console.log('✅ Invite functionality available')
      } else {
        console.log('ℹ️ No invite buttons found (may not have permissions)')
      }
    } catch (error) {
      console.log('Could not test invitation creation')
    }
    
    console.log('\n✅ Comprehensive invitation flow test completed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    if (page) await page.close()
    if (browser) await browser.close()
  }
}

// Export for manual testing
export { testInvitationFlowComplete }

// If run directly, prompt for credentials
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🔐 Please provide your login credentials for testing:')
  
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  })
  
  readline.question('Email: ', (email) => {
    readline.question('Password: ', (password) => {
      readline.close()
      testInvitationFlowComplete(email, password)
    })
  })
}