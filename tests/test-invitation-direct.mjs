import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testInvitationDirect() {
  console.log('🧪 Testing Direct Invitation Access')
  console.log('===================================')
  console.log('')
  
  const invitationToken = '00a1de99-c0f6-46f5-b4f7-302f00079fff'
  
  try {
    console.log('🔍 Testing different query approaches...')
    console.log('')
    
    // Test 1: Direct query with select *
    console.log('Test 1: Direct query with select *')
    const { data: test1, error: error1 } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('token', invitationToken)
      .maybeSingle()
    
    console.log('Result:', test1 ? 'SUCCESS' : 'FAILED')
    if (error1) {
      console.log('Error:', error1)
    } else {
      console.log('Data found:', !!test1)
    }
    console.log('')
    
    // Test 2: Query with specific fields
    console.log('Test 2: Query with specific fields')
    const { data: test2, error: error2 } = await supabase
      .from('team_invitations')
      .select('id, email, role, status, team_id, expires_at')
      .eq('token', invitationToken)
      .maybeSingle()
    
    console.log('Result:', test2 ? 'SUCCESS' : 'FAILED')
    if (error2) {
      console.log('Error:', error2)
    } else {
      console.log('Data found:', !!test2)
    }
    console.log('')
    
    // Test 3: Query without maybeSingle
    console.log('Test 3: Query without maybeSingle')
    const { data: test3, error: error3 } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('token', invitationToken)
    
    console.log('Result:', test3?.length > 0 ? 'SUCCESS' : 'FAILED')
    if (error3) {
      console.log('Error:', error3)
    } else {
      console.log('Data found:', test3?.length || 0, 'records')
    }
    console.log('')
    
    // Test 4: Test with RPC function if it exists
    console.log('Test 4: Test with RPC function')
    try {
      const { data: test4, error: error4 } = await supabase.rpc('get_invitation_details', {
        p_token: invitationToken
      })
      
      console.log('Result:', test4 ? 'SUCCESS' : 'FAILED')
      if (error4) {
        console.log('Error:', error4)
      } else {
        console.log('Data found:', !!test4)
      }
    } catch (rpcError) {
      console.log('RPC function not available or error:', rpcError.message)
    }
    console.log('')
    
    // Test 5: Check auth status
    console.log('Test 5: Check auth status')
    const { data: authData, error: authError } = await supabase.auth.getSession()
    console.log('Auth session:', authData?.session ? 'LOGGED IN' : 'ANONYMOUS')
    if (authError) {
      console.log('Auth error:', authError)
    }
    console.log('')
    
    // Test 6: Try to use the edge function directly
    console.log('Test 6: Test edge function')
    try {
      const { data: test6, error: error6 } = await supabase.functions.invoke('get-invitation-details', {
        body: { token: invitationToken }
      })
      
      console.log('Result:', test6 ? 'SUCCESS' : 'FAILED')
      if (error6) {
        console.log('Error:', error6)
      } else {
        console.log('Data:', test6)
      }
    } catch (funcError) {
      console.log('Edge function error:', funcError.message)
    }
    console.log('')
    
    console.log('🎯 SUMMARY:')
    console.log('===========')
    if (test1 || test2 || test3?.length > 0) {
      console.log('✅ Invitation data is accessible via direct queries')
      console.log('❌ The issue is likely in the frontend application logic')
      console.log('')
      console.log('🔧 RECOMMENDED FIXES:')
      console.log('1. Check the InvitationPage.tsx component for JWT handling')
      console.log('2. Verify the Supabase client configuration')
      console.log('3. Check if there are any authentication interceptors')
      console.log('4. Look for any custom JWT validation logic')
    } else {
      console.log('❌ Invitation data is not accessible')
      console.log('🔧 This indicates a database or RLS policy issue')
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

testInvitationDirect().catch(console.error)
