import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function debugCurrentInvitation() {
  console.log('🔍 DEBUGGING CURRENT INVITATION ATTEMPT')
  console.log('======================================')
  console.log('')
  
  // Check the token from the screenshot
  const token = 'e94b760b-12f0-4a82-a849-4f3f527f7bc1'
  
  console.log('Checking token:', token)
  console.log('')
  
  // Step 1: Check if invitation exists and its status
  console.log('🔍 Step 1: Checking invitation status')
  try {
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('token', token)
      .maybeSingle()
    
    if (error) {
      console.log('❌ Error checking invitation:', error.message)
      return
    }
    
    if (!invitation) {
      console.log('❌ Invitation not found')
      return
    }
    
    console.log('✅ Invitation found:')
    console.log('   ID:', invitation.id)
    console.log('   Email:', invitation.email)
    console.log('   Status:', invitation.status)
    console.log('   Team ID:', invitation.team_id)
    console.log('   Role:', invitation.role)
    console.log('   Created:', invitation.created_at)
    console.log('   Expires:', invitation.expires_at)
    console.log('')
    
    if (invitation.status === 'accepted') {
      console.log('⚠️  INVITATION ALREADY ACCEPTED!')
      console.log('This explains the error. The invitation was already used.')
      console.log('')
      
      // Check if user exists
      console.log('🔍 Checking if user exists for this email...')
      try {
        const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
          email: invitation.email,
          password: 'TestPassword123!' // The password from our test
        })
        
        if (loginError) {
          console.log('❌ User login failed:', loginError.message)
        } else {
          console.log('✅ User exists and can login!')
          console.log('   User ID:', loginData.user?.id)
          await supabase.auth.signOut()
        }
      } catch (err) {
        console.log('❌ Login test error:', err.message)
      }
      
      console.log('')
      console.log('🔧 SOLUTION: Create a new invitation for a different email')
      return
    }
    
    // Step 2: Test the edge function with the exact data from the form
    console.log('🔍 Step 2: Testing edge function with form data')
    
    const formData = {
      token: token,
      email: '<EMAIL>', // From the screenshot
      password: '••••••••', // We'll use a test password
      first_name: 'Andrew',
      last_name: 'Arnotta',
      role: 'service_provider'
    }
    
    // Use a real password for testing
    const testData = {
      ...formData,
      password: 'TestPassword123!'
    }
    
    console.log('Testing with data:', JSON.stringify(testData, null, 2))
    console.log('')
    
    try {
      const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
      })
      
      console.log('Response status:', response.status)
      console.log('Response status text:', response.statusText)
      
      const responseText = await response.text()
      console.log('Response body:', responseText)
      
      try {
        const responseJson = JSON.parse(responseText)
        console.log('')
        console.log('📊 DETAILED ERROR ANALYSIS:')
        console.log('===========================')
        
        if (responseJson.error) {
          console.log('Error message:', responseJson.error)
          
          if (responseJson.error.includes('already been registered')) {
            console.log('')
            console.log('💡 CAUSE: User with email already exists')
            console.log('SOLUTION: Use a different email or handle existing user')
          } else if (responseJson.error.includes('not found')) {
            console.log('')
            console.log('💡 CAUSE: Invitation not found')
            console.log('SOLUTION: Check token is correct')
          } else if (responseJson.error.includes('permission')) {
            console.log('')
            console.log('💡 CAUSE: Permission denied')
            console.log('SOLUTION: Check RLS policies')
          }
        }
      } catch (parseError) {
        console.log('Could not parse response as JSON')
      }
      
    } catch (err) {
      console.log('❌ Request failed:', err.message)
    }
    
  } catch (err) {
    console.error('❌ Unexpected error:', err.message)
  }
}

async function createNewInvitationForTesting() {
  console.log('')
  console.log('🔧 CREATING NEW INVITATION FOR TESTING')
  console.log('======================================')
  
  // Generate a completely new email
  const uniqueId = Math.random().toString(36).substring(2, 8)
  const newEmail = `andyarnott+fresh${uniqueId}@gmail.com`
  const newToken = crypto.randomUUID()
  
  console.log('Creating invitation for:', newEmail)
  console.log('Token:', newToken)
  
  try {
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .insert({
        token: newToken,
        team_id: '80145fbc-b0b5-421f-acff-5c20a1e573e8',
        email: newEmail,
        role: 'service_provider',
        status: 'pending',
        invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.log('❌ Error creating invitation:', error.message)
      return
    }
    
    console.log('✅ New invitation created!')
    console.log('')
    console.log('🔗 NEW INVITATION URL:')
    console.log('======================')
    const newUrl = `https://www.stayfu.com/#/invite?token=${newToken}&team_id=80145fbc-b0b5-421f-acff-5c20a1e573e8`
    console.log(newUrl)
    console.log('')
    console.log('Use this fresh invitation to test!')
    
  } catch (err) {
    console.error('❌ Error creating new invitation:', err.message)
  }
}

async function runDebug() {
  await debugCurrentInvitation()
  await createNewInvitationForTesting()
}

runDebug().catch(console.error)
