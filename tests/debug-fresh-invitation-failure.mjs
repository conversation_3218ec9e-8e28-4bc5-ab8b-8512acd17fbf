import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

async function debugFreshInvitationFailure() {
  console.log('🚨 DEBUGGING FRESH INVITATION FAILURE')
  console.log('====================================')
  console.log('')
  
  // The token from the fresh invitation
  const token = '26985a69-2514-4b26-be8d-c84164310f77'
  
  console.log('Debugging token:', token)
  console.log('')
  
  // Test data that matches what's in the form
  const testData = {
    token: token,
    email: 'and<PERSON><PERSON><PERSON>+<EMAIL>',
    password: 'TestPassword123!',
    first_name: 'Molly',
    last_name: '<PERSON><PERSON><PERSON>',
    role: 'service_provider'
  }
  
  console.log('Test data:', JSON.stringify(testData, null, 2))
  console.log('')
  
  // Step 1: Check invitation exists and is pending
  console.log('🔍 Step 1: Checking invitation status')
  try {
    const response1 = await fetch(`${supabaseUrl}/functions/v1/get-invitation-details`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token: token })
    })
    
    console.log('get-invitation-details status:', response1.status)
    const inviteResponse = await response1.text()
    console.log('get-invitation-details response:', inviteResponse)
    
    if (response1.status !== 200) {
      console.log('❌ get-invitation-details failed!')
      return
    }
    
    const inviteData = JSON.parse(inviteResponse)
    if (!inviteData.success) {
      console.log('❌ get-invitation-details returned error:', inviteData.error)
      return
    }
    
    console.log('✅ Invitation details retrieved successfully')
    console.log('Invitation status:', inviteData.invitation.status)
    console.log('')
    
  } catch (err) {
    console.log('❌ Error checking invitation:', err.message)
    return
  }
  
  // Step 2: Test accept-invitation-direct with detailed error logging
  console.log('🔍 Step 2: Testing accept-invitation-direct with full error details')
  try {
    const response2 = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    })
    
    console.log('accept-invitation-direct status:', response2.status)
    console.log('accept-invitation-direct status text:', response2.statusText)
    
    // Get all response headers
    console.log('Response headers:')
    for (const [key, value] of response2.headers.entries()) {
      console.log(`  ${key}: ${value}`)
    }
    
    const responseText = await response2.text()
    console.log('Response body (raw):', responseText)
    console.log('')
    
    if (response2.status !== 200) {
      console.log('🚨 EDGE FUNCTION FAILED!')
      console.log('Status:', response2.status)
      console.log('Status Text:', response2.statusText)
      
      try {
        const errorData = JSON.parse(responseText)
        console.log('Error details:', JSON.stringify(errorData, null, 2))
        
        if (errorData.error) {
          console.log('')
          console.log('🔍 ERROR ANALYSIS:')
          console.log('==================')
          console.log('Error message:', errorData.error)
          
          // Analyze specific error types
          if (errorData.error.includes('already been registered')) {
            console.log('💡 ISSUE: User already exists')
            console.log('CAUSE: Email address already has an account')
            console.log('SOLUTION: Use a completely different email')
          } else if (errorData.error.includes('not found')) {
            console.log('💡 ISSUE: Invitation not found')
            console.log('CAUSE: Token might be invalid or expired')
          } else if (errorData.error.includes('permission')) {
            console.log('💡 ISSUE: Permission denied')
            console.log('CAUSE: RLS policy or authentication issue')
          } else if (errorData.error.includes('constraint')) {
            console.log('💡 ISSUE: Database constraint violation')
            console.log('CAUSE: Duplicate data or foreign key issue')
          } else {
            console.log('💡 ISSUE: Unknown error')
            console.log('CAUSE: Need to check edge function logs')
          }
        }
      } catch (parseError) {
        console.log('Could not parse error response as JSON')
        console.log('Raw error response:', responseText)
      }
      
      return
    }
    
    console.log('✅ accept-invitation-direct succeeded!')
    const successData = JSON.parse(responseText)
    console.log('Success data:', JSON.stringify(successData, null, 2))
    
  } catch (err) {
    console.log('❌ Request failed with exception:', err.message)
    console.log('Stack trace:', err.stack)
  }
}

async function checkEdgeFunctionLogs() {
  console.log('')
  console.log('📋 EDGE FUNCTION DEBUGGING TIPS:')
  console.log('================================')
  console.log('1. Check function logs at: https://supabase.com/dashboard/project/pwaeknalhosfwuxkpaet/functions')
  console.log('2. Look for console.log outputs from the edge function')
  console.log('3. Check for any deployment issues')
  console.log('4. Verify environment variables are set correctly')
  console.log('')
  console.log('If the function is failing silently, there might be:')
  console.log('- Missing environment variables (SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)')
  console.log('- RLS policy blocking the operations')
  console.log('- Database connection issues')
  console.log('- Code syntax errors in the edge function')
}

async function runDebug() {
  await debugFreshInvitationFailure()
  await checkEdgeFunctionLogs()
}

runDebug().catch(console.error)
