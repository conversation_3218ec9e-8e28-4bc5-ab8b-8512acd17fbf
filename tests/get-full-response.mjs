import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

async function getFullResponse() {
  const token = '26985a69-2514-4b26-be8d-c84164310f77'
  
  const testData = {
    token: token,
    email: '<EMAIL>',
    password: 'TestPassword123!',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON><PERSON>',
    role: 'service_provider'
  }
  
  console.log('🔍 Testing accept-invitation-direct...')
  
  try {
    const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    })
    
    console.log('Status:', response.status)
    console.log('Status Text:', response.statusText)
    
    const responseText = await response.text()
    console.log('Full Response Body:')
    console.log(responseText)
    console.log('')
    
    if (response.status === 200) {
      console.log('✅ SUCCESS! The edge function is working!')
      
      try {
        const data = JSON.parse(responseText)
        console.log('Parsed response:')
        console.log(JSON.stringify(data, null, 2))
        
        if (data.success) {
          console.log('')
          console.log('🎉 INVITATION ACCEPTED SUCCESSFULLY!')
          console.log('User ID:', data.user?.id)
          console.log('Email:', data.user?.email)
          console.log('')
          console.log('🤔 BUT WHY IS THE FRONTEND STILL SHOWING AN ERROR?')
          console.log('This suggests the issue is in the frontend JavaScript, not the backend!')
        }
      } catch (parseError) {
        console.log('Could not parse response as JSON')
      }
    } else {
      console.log('❌ Edge function failed')
    }
    
  } catch (err) {
    console.log('❌ Request failed:', err.message)
  }
}

getFullResponse().catch(console.error)
