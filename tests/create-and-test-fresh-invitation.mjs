import { createClient } from '@supabase/supabase-js'
import { v4 as uuidv4 } from 'uuid'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function createFreshInvitation() {
  console.log('🔧 CREATING FRESH INVITATION')
  console.log('============================')
  console.log('')
  
  // Generate unique email and token
  const uniqueId = Math.random().toString(36).substring(2, 8)
  const testEmail = `andyarnott+test${uniqueId}@gmail.com`
  const token = uuidv4()
  
  console.log('Creating invitation for:', testEmail)
  console.log('Token:', token)
  console.log('')
  
  try {
    // Create the invitation
    const { data: invitation, error: inviteError } = await supabase
      .from('team_invitations')
      .insert({
        token: token,
        team_id: '80145fbc-b0b5-421f-acff-5c20a1e573e8', // Idaho Properties team
        email: testEmail,
        role: 'service_provider',
        status: 'pending',
        invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b', // Admin user
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (inviteError) {
      console.log('❌ Error creating invitation:', inviteError.message)
      return null
    }
    
    console.log('✅ Invitation created successfully!')
    console.log('Invitation ID:', invitation.id)
    console.log('')
    
    return {
      token: token,
      email: testEmail,
      invitation: invitation
    }
    
  } catch (err) {
    console.error('❌ Error creating invitation:', err.message)
    return null
  }
}

async function testInvitationFlow(invitationData) {
  console.log('🧪 TESTING COMPLETE INVITATION FLOW')
  console.log('===================================')
  console.log('')
  
  const testData = {
    token: invitationData.token,
    email: invitationData.email,
    password: 'TestPassword123!',
    first_name: 'Test',
    last_name: 'User',
    role: 'service_provider'
  }
  
  console.log('Test data:', JSON.stringify(testData, null, 2))
  console.log('')
  
  // Step 1: Test get-invitation-details
  console.log('🔍 Step 1: Testing get-invitation-details')
  try {
    const { data, error } = await supabase.functions.invoke('get-invitation-details', {
      body: { token: testData.token }
    })
    
    if (error) {
      console.log('❌ get-invitation-details error:', error)
      return false
    }
    
    if (!data?.success) {
      console.log('❌ get-invitation-details failed:', data?.error)
      return false
    }
    
    console.log('✅ get-invitation-details successful!')
    console.log('Invitation details:', data.invitation)
    console.log('')
  } catch (err) {
    console.log('❌ get-invitation-details exception:', err.message)
    return false
  }
  
  // Step 2: Test accept-invitation-direct
  console.log('🔍 Step 2: Testing accept-invitation-direct')
  try {
    const { data, error } = await supabase.functions.invoke('accept-invitation-direct', {
      body: testData
    })
    
    console.log('')
    console.log('📊 ACCEPT INVITATION RESULT:')
    console.log('============================')
    
    if (error) {
      console.log('❌ accept-invitation-direct error:')
      console.log('   Error object:', error)
      console.log('   Error message:', error.message)
      console.log('   Error status:', error.status)
      return false
    }
    
    if (!data?.success) {
      console.log('❌ accept-invitation-direct failed:')
      console.log('   Response data:', data)
      return false
    }
    
    console.log('✅ accept-invitation-direct successful!')
    console.log('   User ID:', data.user?.id)
    console.log('   Team ID:', data.team_id)
    console.log('   Is New User:', data.isNewUser)
    console.log('   Message:', data.message)
    console.log('')
    
    // Step 3: Test login with the new user
    console.log('🔍 Step 3: Testing login with new user')
    try {
      const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
        email: testData.email,
        password: testData.password
      })
      
      if (loginError) {
        console.log('❌ Login failed:', loginError.message)
        return false
      } else {
        console.log('✅ Login successful!')
        console.log('   User ID:', loginData.user?.id)
        console.log('   Email:', loginData.user?.email)
        
        // Check team membership
        const { data: teamMemberships, error: teamError } = await supabase
          .from('team_members')
          .select('*')
          .eq('user_id', loginData.user.id)
        
        if (teamError) {
          console.log('❌ Error checking team membership:', teamError.message)
        } else {
          console.log('✅ Team memberships:', teamMemberships.length)
          teamMemberships.forEach(membership => {
            console.log('   Team ID:', membership.team_id)
            console.log('   Status:', membership.status)
          })
        }
        
        // Sign out
        await supabase.auth.signOut()
        console.log('   Signed out successfully')
      }
    } catch (loginErr) {
      console.log('❌ Login exception:', loginErr.message)
      return false
    }
    
    console.log('')
    console.log('🎉 COMPLETE INVITATION FLOW SUCCESSFUL!')
    console.log('All steps passed - the invitation system is working!')
    
    return true
    
  } catch (err) {
    console.log('❌ accept-invitation-direct exception:', err.message)
    return false
  }
}

async function generateInvitationURL(invitationData) {
  console.log('')
  console.log('🔗 FRESH INVITATION URL:')
  console.log('========================')
  
  const baseUrl = 'https://www.stayfu.com'
  const invitationUrl = `${baseUrl}/#/invite?token=${invitationData.token}&team_id=80145fbc-b0b5-421f-acff-5c20a1e573e8`
  
  console.log('Use this URL for testing:')
  console.log(invitationUrl)
  console.log('')
  console.log('This invitation is for:')
  console.log('- Email:', invitationData.email)
  console.log('- Role: service_provider')
  console.log('- Team: Idaho Properties')
  console.log('- Status: pending')
}

async function runCompleteTest() {
  try {
    // Create fresh invitation
    const invitationData = await createFreshInvitation()
    if (!invitationData) {
      console.log('❌ Failed to create invitation')
      return
    }
    
    // Test the complete flow
    const success = await testInvitationFlow(invitationData)
    if (!success) {
      console.log('❌ Invitation flow test failed')
      return
    }
    
    // Generate URL for manual testing
    await generateInvitationURL(invitationData)
    
  } catch (error) {
    console.error('❌ Unexpected error during test:', error)
  }
}

runCompleteTest().catch(console.error)
