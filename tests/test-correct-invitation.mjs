import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Test with the CORRECT, EXISTING invitation token
const correctInvitation = {
  token: '7f371193-f52d-4e43-8eef-69a7b84919a0',
  email: '<EMAIL>',
  password: 'TestPassword123!',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON><PERSON>',
  role: 'service_provider'
}

async function testCorrectInvitation() {
  console.log('🔍 TESTING WITH CORRECT INVITATION TOKEN')
  console.log('=======================================')
  console.log('')
  console.log('Using invitation data:')
  console.log('Token:', correctInvitation.token)
  console.log('Email:', correctInvitation.email)
  console.log('')
  
  // Step 1: Test get-invitation-details
  console.log('🔍 Step 1: Testing get-invitation-details')
  try {
    const { data, error } = await supabase.functions.invoke('get-invitation-details', {
      body: { token: correctInvitation.token }
    })
    
    if (error) {
      console.log('❌ get-invitation-details error:', error)
      return
    }
    
    if (!data?.success) {
      console.log('❌ get-invitation-details failed:', data?.error)
      return
    }
    
    console.log('✅ get-invitation-details successful!')
    console.log('Invitation details:', data.invitation)
    console.log('')
  } catch (err) {
    console.log('❌ get-invitation-details exception:', err.message)
    return
  }
  
  // Step 2: Test accept-invitation-direct
  console.log('🔍 Step 2: Testing accept-invitation-direct')
  try {
    const { data, error } = await supabase.functions.invoke('accept-invitation-direct', {
      body: correctInvitation
    })
    
    console.log('')
    console.log('📊 ACCEPT INVITATION RESULT:')
    console.log('============================')
    
    if (error) {
      console.log('❌ accept-invitation-direct error:')
      console.log('   Error object:', error)
      console.log('   Error message:', error.message)
      console.log('   Error status:', error.status)
      
      if (error.context) {
        console.log('   Error context:', error.context)
      }
      
      return
    }
    
    if (!data?.success) {
      console.log('❌ accept-invitation-direct failed:')
      console.log('   Response data:', data)
      return
    }
    
    console.log('✅ accept-invitation-direct successful!')
    console.log('   User ID:', data.user?.id)
    console.log('   Team ID:', data.team_id)
    console.log('   Message:', data.message)
    console.log('')
    
    // Step 3: Test login with the new user
    console.log('🔍 Step 3: Testing login with new user')
    try {
      const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
        email: correctInvitation.email,
        password: correctInvitation.password
      })
      
      if (loginError) {
        console.log('❌ Login failed:', loginError.message)
      } else {
        console.log('✅ Login successful!')
        console.log('   User ID:', loginData.user?.id)
        console.log('   Email:', loginData.user?.email)
        
        // Sign out
        await supabase.auth.signOut()
        console.log('   Signed out successfully')
      }
    } catch (loginErr) {
      console.log('❌ Login exception:', loginErr.message)
    }
    
    console.log('')
    console.log('🎉 INVITATION SYSTEM IS WORKING!')
    console.log('The issue was using an incorrect/non-existent token.')
    
  } catch (err) {
    console.log('❌ accept-invitation-direct exception:', err.message)
  }
}

async function generateCorrectInvitationURL() {
  console.log('')
  console.log('🔗 CORRECT INVITATION URL:')
  console.log('==========================')
  
  const baseUrl = 'https://www.stayfu.com'
  const correctUrl = `${baseUrl}/#/invite?token=${correctInvitation.token}&team_id=80145fbc-b0b5-421f-acff-5c20a1e573e8`
  
  console.log('Use this URL for testing:')
  console.log(correctUrl)
  console.log('')
  console.log('This invitation is for:')
  console.log('- Email:', correctInvitation.email)
  console.log('- Role: service_provider')
  console.log('- Team: Idaho Properties')
  console.log('- Status: pending')
}

async function runTest() {
  await testCorrectInvitation()
  await generateCorrectInvitationURL()
}

runTest().catch(console.error)
