import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function checkExistingInvitations() {
  console.log('🔍 CHECKING EXISTING INVITATIONS')
  console.log('================================')
  console.log('')
  
  try {
    // Get all invitations
    const { data: allInvitations, error } = await supabase
      .from('team_invitations')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)
    
    if (error) {
      console.log('❌ Error fetching invitations:', error.message)
      return
    }
    
    console.log(`Found ${allInvitations.length} invitations:`)
    console.log('')
    
    allInvitations.forEach((invitation, index) => {
      console.log(`${index + 1}. Invitation ID: ${invitation.id}`)
      console.log(`   Token: ${invitation.token}`)
      console.log(`   Email: ${invitation.email}`)
      console.log(`   Team ID: ${invitation.team_id}`)
      console.log(`   Role: ${invitation.role}`)
      console.log(`   Status: ${invitation.status}`)
      console.log(`   Created: ${invitation.created_at}`)
      console.log('')
    })
    
    // Check for the specific token we're looking for
    const targetToken = '7871-a5-f524-ac4b-8eef-8e99e5e67a0'
    const foundInvitation = allInvitations.find(inv => inv.token === targetToken)
    
    if (foundInvitation) {
      console.log('✅ Found the target invitation!')
    } else {
      console.log('❌ Target invitation not found in database')
      console.log(`   Looking for token: ${targetToken}`)
      
      // Check if there are any invitations for the email
      const emailInvitations = allInvitations.filter(inv => inv.email === '<EMAIL>')
      if (emailInvitations.length > 0) {
        console.log('')
        console.log('📧 Found invitations for the same email:')
        emailInvitations.forEach(inv => {
          console.log(`   Token: ${inv.token}`)
          console.log(`   Status: ${inv.status}`)
          console.log(`   Created: ${inv.created_at}`)
        })
      }
    }
    
    // Also check for any recent invitations
    console.log('')
    console.log('📅 Most recent invitations:')
    const recentInvitations = allInvitations.slice(0, 3)
    recentInvitations.forEach(inv => {
      console.log(`   Token: ${inv.token}`)
      console.log(`   Email: ${inv.email}`)
      console.log(`   Status: ${inv.status}`)
      console.log(`   Created: ${inv.created_at}`)
      console.log('')
    })
    
  } catch (err) {
    console.error('❌ Unexpected error:', err.message)
  }
}

checkExistingInvitations().catch(console.error)
