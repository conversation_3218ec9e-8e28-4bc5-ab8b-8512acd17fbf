import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function checkInvitationStatus() {
  console.log('🔍 CHECKING INVITATION STATUS')
  console.log('============================')
  console.log('')
  
  const token = '7f371193-f52d-4e43-8eef-69a7b84919a0'
  
  try {
    // Check the invitation status
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('token', token)
      .maybeSingle()
    
    if (error) {
      console.log('❌ Error checking invitation:', error.message)
      return
    }
    
    if (!invitation) {
      console.log('❌ Invitation not found')
      return
    }
    
    console.log('📋 Invitation Details:')
    console.log('=====================')
    console.log('ID:', invitation.id)
    console.log('Token:', invitation.token)
    console.log('Email:', invitation.email)
    console.log('Team ID:', invitation.team_id)
    console.log('Role:', invitation.role)
    console.log('Status:', invitation.status)
    console.log('Created:', invitation.created_at)
    console.log('Accepted:', invitation.accepted_at)
    console.log('Updated:', invitation.updated_at)
    console.log('')
    
    if (invitation.status === 'accepted') {
      console.log('⚠️  INVITATION ALREADY ACCEPTED!')
      console.log('This is why the edge function is failing.')
      console.log('')
      
      // Check if user exists
      console.log('🔍 Checking if user exists...')
      try {
        const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
          email: invitation.email,
          password: 'TestPassword123!'
        })
        
        if (loginError) {
          console.log('❌ User login failed:', loginError.message)
          console.log('User might not exist or password is wrong')
        } else {
          console.log('✅ User exists and can login!')
          console.log('User ID:', loginData.user?.id)
          
          // Sign out
          await supabase.auth.signOut()
          console.log('Signed out')
        }
      } catch (err) {
        console.log('❌ Login test error:', err.message)
      }
      
      console.log('')
      console.log('🔧 SOLUTION OPTIONS:')
      console.log('====================')
      console.log('1. Create a NEW invitation for a different email')
      console.log('2. Reset this invitation status to "pending"')
      console.log('3. Use the existing user credentials to login')
      
    } else if (invitation.status === 'pending') {
      console.log('✅ Invitation is still pending - should work')
      
      // Test the edge function
      console.log('')
      console.log('🔍 Testing edge function with this invitation...')
      
      const testData = {
        token: invitation.token,
        email: invitation.email,
        password: 'TestPassword123!',
        first_name: 'Molly',
        last_name: 'Arnott',
        role: 'service_provider'
      }
      
      try {
        const { data, error } = await supabase.functions.invoke('accept-invitation-direct', {
          body: testData
        })
        
        if (error) {
          console.log('❌ Edge function error:', error)
        } else if (!data?.success) {
          console.log('❌ Edge function failed:', data?.error)
        } else {
          console.log('✅ Edge function successful!')
          console.log('User ID:', data.user?.id)
        }
      } catch (err) {
        console.log('❌ Edge function exception:', err.message)
      }
    }
    
  } catch (err) {
    console.error('❌ Unexpected error:', err.message)
  }
}

checkInvitationStatus().catch(console.error)
