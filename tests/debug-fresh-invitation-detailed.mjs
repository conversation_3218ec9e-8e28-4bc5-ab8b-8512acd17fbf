import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function debugFreshInvitationDetailed() {
  console.log('🔍 DEBUGGING FRESH INVITATION WITH DETAILED LOGGING')
  console.log('==================================================')
  console.log('')
  
  // Use the fresh invitation from the previous test
  const token = '5e562e48-8fd7-484a-8909-95e797056a00'
  const email = '<EMAIL>'
  
  console.log('Testing token:', token)
  console.log('Testing email:', email)
  console.log('')
  
  // Step 1: Verify invitation exists and is pending
  console.log('🔍 Step 1: Verifying invitation status')
  try {
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('token', token)
      .maybeSingle()
    
    if (error) {
      console.log('❌ Error checking invitation:', error.message)
      return
    }
    
    if (!invitation) {
      console.log('❌ Invitation not found')
      return
    }
    
    console.log('✅ Invitation found:')
    console.log('   Status:', invitation.status)
    console.log('   Email:', invitation.email)
    console.log('   Team ID:', invitation.team_id)
    console.log('   Role:', invitation.role)
    console.log('')
    
    if (invitation.status !== 'pending') {
      console.log('⚠️  Invitation is not pending, creating a new one...')
      await createBrandNewInvitation()
      return
    }
    
    // Step 2: Test the edge function with exact data
    console.log('🔍 Step 2: Testing edge function with detailed logging')
    
    const testData = {
      token: token,
      email: email,
      password: 'TestPassword123!',
      first_name: 'Test',
      last_name: 'User',
      role: 'service_provider'
    }
    
    console.log('Request data:', JSON.stringify(testData, null, 2))
    console.log('')
    console.log('Making request to edge function...')
    
    const startTime = Date.now()
    
    try {
      const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'StayFu-Test/1.0'
        },
        body: JSON.stringify(testData)
      })
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      console.log('Response received in', duration, 'ms')
      console.log('Response status:', response.status)
      console.log('Response status text:', response.statusText)
      console.log('')
      
      // Log all response headers
      console.log('Response headers:')
      for (const [key, value] of response.headers.entries()) {
        console.log(`  ${key}: ${value}`)
      }
      console.log('')
      
      const responseText = await response.text()
      console.log('Response body (raw):')
      console.log(responseText)
      console.log('')
      
      if (response.status === 200) {
        console.log('🎉 SUCCESS!')
        try {
          const data = JSON.parse(responseText)
          console.log('Parsed response:', JSON.stringify(data, null, 2))
        } catch (parseError) {
          console.log('Could not parse response as JSON')
        }
      } else {
        console.log('🚨 EDGE FUNCTION FAILED!')
        console.log('Status:', response.status)
        
        // Try to parse error details
        try {
          const errorData = JSON.parse(responseText)
          console.log('Error details:', JSON.stringify(errorData, null, 2))
          
          // Analyze the specific error
          if (errorData.error) {
            console.log('')
            console.log('🔍 ERROR ANALYSIS:')
            console.log('==================')
            console.log('Error message:', errorData.error)
            
            if (errorData.error.includes('already been registered')) {
              console.log('💡 ISSUE: User already exists')
              console.log('SOLUTION: The edge function should handle this case')
            } else if (errorData.error.includes('not found')) {
              console.log('💡 ISSUE: Invitation not found')
            } else if (errorData.error.includes('permission')) {
              console.log('💡 ISSUE: Permission denied')
            } else if (errorData.error.includes('constraint')) {
              console.log('💡 ISSUE: Database constraint violation')
            } else if (errorData.error.includes('expired')) {
              console.log('💡 ISSUE: Invitation expired')
            } else {
              console.log('💡 ISSUE: Unknown error - need to check edge function logs')
            }
          }
        } catch (parseError) {
          console.log('Could not parse error response as JSON')
        }
      }
      
    } catch (fetchError) {
      console.log('❌ Request failed with exception:', fetchError.message)
      console.log('Stack trace:', fetchError.stack)
    }
    
  } catch (err) {
    console.error('❌ Unexpected error:', err.message)
  }
}

async function createBrandNewInvitation() {
  console.log('')
  console.log('🔧 CREATING BRAND NEW INVITATION')
  console.log('=================================')
  
  const uniqueId = Math.random().toString(36).substring(2, 8)
  const newEmail = `andyarnott+debug${uniqueId}@gmail.com`
  const newToken = crypto.randomUUID()
  
  console.log('Creating invitation for:', newEmail)
  console.log('Token:', newToken)
  
  try {
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .insert({
        token: newToken,
        team_id: '80145fbc-b0b5-421f-acff-5c20a1e573e8',
        email: newEmail,
        role: 'service_provider',
        status: 'pending',
        invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.log('❌ Error creating invitation:', error.message)
      return
    }
    
    console.log('✅ Brand new invitation created!')
    console.log('')
    console.log('🔗 BRAND NEW INVITATION URL:')
    console.log('============================')
    const newUrl = `https://www.stayfu.com/#/invite?token=${newToken}&team_id=80145fbc-b0b5-421f-acff-5c20a1e573e8`
    console.log(newUrl)
    console.log('')
    console.log('📧 Email for testing:', newEmail)
    console.log('🔑 Token:', newToken)
    console.log('')
    console.log('🧪 Now testing this brand new invitation...')
    console.log('')
    
    // Test the brand new invitation
    const testData = {
      token: newToken,
      email: newEmail,
      password: 'TestPassword123!',
      first_name: 'Debug',
      last_name: 'User',
      role: 'service_provider'
    }
    
    console.log('Testing with data:', JSON.stringify(testData, null, 2))
    
    try {
      const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
      })
      
      console.log('Response status:', response.status)
      const responseText = await response.text()
      console.log('Response body:', responseText)
      
      if (response.status === 200) {
        console.log('🎉 BRAND NEW INVITATION WORKS!')
        const data = JSON.parse(responseText)
        console.log('Success data:', JSON.stringify(data, null, 2))
      } else {
        console.log('🚨 BRAND NEW INVITATION ALSO FAILS!')
        try {
          const errorData = JSON.parse(responseText)
          console.log('Error details:', JSON.stringify(errorData, null, 2))
        } catch (parseError) {
          console.log('Could not parse error response')
        }
      }
      
    } catch (err) {
      console.log('❌ Request failed:', err.message)
    }
    
  } catch (err) {
    console.error('❌ Error creating new invitation:', err.message)
  }
}

debugFreshInvitationDetailed().catch(console.error)
