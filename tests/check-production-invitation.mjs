import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function checkProductionInvitation() {
  console.log('🔍 Checking Production Invitation Token')
  console.log('======================================')
  console.log('')
  
  const invitationToken = '00a1de99-c0f6-46f5-b4f7-302f00079fff'
  
  try {
    console.log(`🎯 Checking token: ${invitationToken}`)
    console.log('')
    
    // Check if the invitation exists
    const { data: invitation, error: invitationError } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('token', invitationToken)
      .maybeSingle()
    
    if (invitationError) {
      console.error('❌ Error fetching invitation:', invitationError)
      return
    }
    
    if (!invitation) {
      console.log('❌ Invitation not found in production database')
      console.log('')
      console.log('🔧 POSSIBLE SOLUTIONS:')
      console.log('======================')
      console.log('1. The invitation token may have expired or been used')
      console.log('2. The token may be incorrect')
      console.log('3. The invitation may have been deleted')
      console.log('')
      console.log('Let me check for recent invitations...')
      
      // Check for recent invitations
      const { data: recentInvitations, error: recentError } = await supabase
        .from('team_invitations')
        .select('token, email, role, status, created_at, expires_at')
        .order('created_at', { ascending: false })
        .limit(5)
      
      if (recentError) {
        console.error('❌ Error fetching recent invitations:', recentError)
      } else {
        console.log('📋 Recent invitations:')
        recentInvitations?.forEach((inv, index) => {
          console.log(`${index + 1}. Token: ${inv.token}`)
          console.log(`   Email: ${inv.email}`)
          console.log(`   Role: ${inv.role}`)
          console.log(`   Status: ${inv.status}`)
          console.log(`   Created: ${new Date(inv.created_at).toLocaleString()}`)
          console.log(`   Expires: ${new Date(inv.expires_at).toLocaleString()}`)
          console.log('')
        })
      }
      return
    }
    
    console.log('✅ Invitation found!')
    console.log('')
    console.log('📋 INVITATION DETAILS:')
    console.log('======================')
    console.log(`🔗 Token: ${invitation.token}`)
    console.log(`📧 Email: ${invitation.email}`)
    console.log(`👤 Role: ${invitation.role}`)
    console.log(`📊 Status: ${invitation.status}`)
    console.log(`🏢 Team ID: ${invitation.team_id}`)
    console.log(`👥 Invited by: ${invitation.invited_by}`)
    console.log(`📅 Created: ${new Date(invitation.created_at).toLocaleString()}`)
    console.log(`⏰ Expires: ${new Date(invitation.expires_at).toLocaleString()}`)
    console.log('')
    
    // Check if invitation has expired
    const now = new Date()
    const expiresAt = new Date(invitation.expires_at)
    const isExpired = now > expiresAt
    
    if (isExpired) {
      console.log('⚠️  INVITATION HAS EXPIRED!')
      console.log(`   Expired ${Math.floor((now - expiresAt) / (1000 * 60 * 60 * 24))} days ago`)
      console.log('')
    } else {
      console.log('✅ Invitation is still valid')
      console.log(`   Expires in ${Math.floor((expiresAt - now) / (1000 * 60 * 60 * 24))} days`)
      console.log('')
    }
    
    // Check if invitation has been used
    if (invitation.status === 'accepted') {
      console.log('⚠️  INVITATION HAS ALREADY BEEN ACCEPTED!')
      console.log('')
    } else if (invitation.status === 'pending') {
      console.log('✅ Invitation is pending and ready to be accepted')
      console.log('')
    }
    
    // Get team details
    const { data: team, error: teamError } = await supabase
      .from('teams')
      .select('name, owner_id')
      .eq('id', invitation.team_id)
      .single()
    
    if (teamError) {
      console.error('❌ Error fetching team details:', teamError)
    } else {
      console.log('🏢 TEAM DETAILS:')
      console.log('================')
      console.log(`📋 Team Name: ${team.name}`)
      console.log(`👤 Owner ID: ${team.owner_id}`)
      console.log('')
    }
    
    // Test the invitation URL
    console.log('🔗 INVITATION URLS:')
    console.log('===================')
    console.log(`🌐 Production: https://www.stayfu.com/#/invite?token=${invitation.token}`)
    console.log(`🏠 Local Dev: http://localhost:8080/#/invite?token=${invitation.token}`)
    console.log('')
    
    if (!isExpired && invitation.status === 'pending') {
      console.log('🎯 NEXT STEPS:')
      console.log('==============')
      console.log('1. The invitation is valid and ready to be used')
      console.log('2. Switch the app to production mode to test the invitation')
      console.log('3. Or create a new invitation in the local environment')
      console.log('')
      console.log('To switch to production mode:')
      console.log('1. Stop the dev server')
      console.log('2. Temporarily rename .env.development to .env.development.bak')
      console.log('3. Restart the dev server')
      console.log('4. The app will use production Supabase')
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

checkProductionInvitation().catch(console.error)
