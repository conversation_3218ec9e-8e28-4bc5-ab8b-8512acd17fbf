import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function checkUserExists() {
  console.log('🔍 CHECKING IF USER EXISTS')
  console.log('==========================')
  console.log('')
  
  const email = '<EMAIL>'
  const password = 'TestPassword123!'
  
  console.log('Testing email:', email)
  console.log('')
  
  // Try to login with the user
  try {
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: email,
      password: password
    })
    
    if (loginError) {
      console.log('❌ Login failed:', loginError.message)
      
      if (loginError.message.includes('Invalid login credentials')) {
        console.log('💡 User exists but password is wrong, or user doesn\'t exist')
      }
    } else {
      console.log('✅ User exists and can login!')
      console.log('User ID:', loginData.user?.id)
      console.log('Email:', loginData.user?.email)
      console.log('Email confirmed:', loginData.user?.email_confirmed_at ? 'Yes' : 'No')
      
      // Check user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', loginData.user.id)
        .maybeSingle()
      
      if (profileError) {
        console.log('❌ Error fetching profile:', profileError.message)
      } else if (profile) {
        console.log('✅ User profile exists:')
        console.log('   Name:', profile.first_name, profile.last_name)
        console.log('   Role:', profile.role)
        console.log('   Email:', profile.email)
      } else {
        console.log('❌ User profile not found')
      }
      
      // Check team membership
      const { data: teamMemberships, error: teamError } = await supabase
        .from('team_members')
        .select('*')
        .eq('user_id', loginData.user.id)
      
      if (teamError) {
        console.log('❌ Error fetching team memberships:', teamError.message)
      } else {
        console.log('✅ Team memberships:', teamMemberships.length)
        teamMemberships.forEach(membership => {
          console.log('   Team ID:', membership.team_id)
          console.log('   Role:', membership.role)
          console.log('   Status:', membership.status)
        })
      }
      
      // Sign out
      await supabase.auth.signOut()
      console.log('Signed out')
      
      console.log('')
      console.log('🔧 SOLUTION:')
      console.log('============')
      console.log('The user already exists and can login!')
      console.log('The invitation should be marked as "accepted".')
      console.log('Update the invitation status and redirect user to login.')
    }
    
  } catch (err) {
    console.error('❌ Login test error:', err.message)
  }
}

async function fixInvitationStatus() {
  console.log('')
  console.log('🔧 FIXING INVITATION STATUS')
  console.log('===========================')
  
  const token = '7f371193-f52d-4e43-8eef-69a7b84919a0'
  
  try {
    // Update invitation status to accepted
    const { error: updateError } = await supabase
      .from('team_invitations')
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('token', token)
    
    if (updateError) {
      console.log('❌ Error updating invitation:', updateError.message)
    } else {
      console.log('✅ Invitation status updated to "accepted"')
    }
  } catch (err) {
    console.error('❌ Update error:', err.message)
  }
}

async function runCheck() {
  await checkUserExists()
  await fixInvitationStatus()
  
  console.log('')
  console.log('🎉 NEXT STEPS:')
  console.log('==============')
  console.log('1. The user can now login with:')
  console.log('   Email: <EMAIL>')
  console.log('   Password: TestPassword123!')
  console.log('')
  console.log('2. Update the edge function to handle existing users')
  console.log('3. Test with a fresh invitation for a new email')
}

runCheck().catch(console.error)
