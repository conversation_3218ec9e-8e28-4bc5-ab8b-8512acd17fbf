import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testUserLogin() {
  console.log('🔐 Testing User Login After Invitation')
  console.log('======================================')
  console.log('')
  
  const credentials = {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  }
  
  try {
    console.log('🚀 Attempting to sign in...')
    console.log('Email:', credentials.email)
    console.log('')
    
    const { data, error } = await supabase.auth.signInWithPassword(credentials)
    
    if (error) {
      console.log('❌ Login failed:')
      console.log('Error:', error.message)
      console.log('Error code:', error.status)
      console.log('')
      console.log('This suggests the user was not created successfully.')
    } else {
      console.log('✅ Login successful!')
      console.log('')
      console.log('👤 USER DETAILS:')
      console.log('================')
      console.log('User ID:', data.user.id)
      console.log('Email:', data.user.email)
      console.log('Email confirmed:', data.user.email_confirmed_at ? 'Yes' : 'No')
      console.log('Created at:', data.user.created_at)
      console.log('User metadata:', data.user.user_metadata)
      console.log('')
      
      // Check team membership
      console.log('🏢 CHECKING TEAM MEMBERSHIP:')
      console.log('============================')
      
      const { data: teamMembers, error: teamError } = await supabase
        .from('team_members')
        .select('*')
        .eq('user_id', data.user.id)
      
      if (teamError) {
        console.log('❌ Error checking team membership:', teamError.message)
      } else {
        console.log('Team memberships found:', teamMembers?.length || 0)
        teamMembers?.forEach((membership, index) => {
          console.log(`${index + 1}. Team ID: ${membership.team_id}`)
          console.log(`   Status: ${membership.status}`)
          console.log(`   Added by: ${membership.added_by}`)
          console.log(`   Created: ${membership.created_at}`)
        })
      }
      
      console.log('')
      console.log('🎯 CONCLUSION:')
      console.log('==============')
      if (teamMembers?.length > 0) {
        console.log('✅ User was successfully created and added to team!')
        console.log('✅ The invitation process worked correctly!')
        console.log('')
        console.log('The frontend error might be due to:')
        console.log('1. The user already exists (duplicate registration attempt)')
        console.log('2. The invitation status was not updated properly')
        console.log('3. A race condition in the frontend code')
      } else {
        console.log('⚠️  User was created but not added to team')
        console.log('This indicates a partial failure in the invitation process')
      }
      
      // Sign out
      await supabase.auth.signOut()
      console.log('')
      console.log('🔓 Signed out successfully')
    }
    
  } catch (unexpectedError) {
    console.error('❌ Unexpected error:', unexpectedError)
  }
}

testUserLogin().catch(console.error)
