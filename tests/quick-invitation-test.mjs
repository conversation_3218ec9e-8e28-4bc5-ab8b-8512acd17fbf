import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function quickTest() {
  console.log('🔍 Quick invitation flow test')
  console.log('============================')
  
  // Test login
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'Newsig1!!!'
  })
  
  if (authError) {
    console.error('❌ Login failed:', authError.message)
    return
  }
  
  console.log('✅ Login successful')
  console.log(`User: ${authData.user.email} (${authData.user.id})`)
  
  // Get profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', authData.user.id)
    .single()
  
  if (profile) {
    console.log(`✅ Profile: ${profile.first_name} ${profile.last_name} (${profile.role})`)
  }
  
  // Get teams
  const { data: teams } = await supabase
    .from('teams')
    .select('*')
  
  console.log(`✅ Teams accessible: ${teams?.length || 0}`)
  
  if (teams && teams.length > 0) {
    // Create invitation
    const invitationToken = crypto.randomUUID()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)
    
    const { data: invitation, error: inviteError } = await supabase
      .from('team_invitations')
      .insert({
        team_id: teams[0].id,
        email: '<EMAIL>',
        role: 'service_provider',
        invited_by: authData.user.id,
        token: invitationToken,
        expires_at: expiresAt.toISOString(),
        status: 'pending'
      })
      .select()
      .single()
    
    if (!inviteError) {
      console.log('✅ Invitation created!')
      console.log(`🔗 http://localhost:8080/#/invite?token=${invitationToken}`)
      
      // Test the invitation link by trying to fetch invitation details
      const { data: inviteDetails, error: detailsError } = await supabase
        .from('team_invitations')
        .select('*, teams!inner(name)')
        .eq('token', invitationToken)
        .single()
      
      if (!detailsError) {
        console.log(`✅ Invitation details: ${inviteDetails.email} → ${inviteDetails.teams.name} (${inviteDetails.role})`)
      }
    }
  }
  
  await supabase.auth.signOut()
  console.log('\n🎯 Next steps:')
  console.log('1. Open the invitation link above')
  console.log('2. <NAME_EMAIL>')
  console.log('3. Check if you can see team data after registration')
}

quickTest().catch(console.error)