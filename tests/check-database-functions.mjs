import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function checkDatabaseFunctions() {
  console.log('🔍 Checking Database Functions and Current State')
  console.log('===============================================')
  
  try {
    // Check if we can connect and query basic data
    console.log('\n📋 Basic connectivity test:')
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('email, role')
      .limit(5)
    
    if (profilesError) {
      console.error('❌ Cannot access profiles:', profilesError)
      return
    }
    
    console.log('✅ Database connection working')
    console.log('Sample profiles:')
    profiles?.forEach(p => console.log(`  - ${p.email}: ${p.role}`))
    
    // <NAME_EMAIL> specifically
    console.log('\n👤 Checking specific user:')
    const { data: testUser, error: testUserError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', '<EMAIL>')
      .single()
    
    if (testUserError) {
      console.log('❌ User not found or error:', testUserError.message)
    } else {
      console.log('✅ Found user:', testUser)
    }
    
    // Check maintenance tasks in general
    console.log('\n🔧 Checking maintenance tasks:')
    const { data: allTasks, error: tasksError } = await supabase
      .from('maintenance_tasks')
      .select('id, title, user_id, provider_id, assigned_to, property_id, team_id')
      .limit(10)
    
    if (tasksError) {
      console.error('❌ Cannot access maintenance tasks:', tasksError)
    } else {
      console.log(`Found ${allTasks?.length || 0} maintenance tasks (showing first 10)`)
      allTasks?.forEach(task => {
        console.log(`  - "${task.title}" | User: ${task.user_id} | Provider: ${task.provider_id} | Assigned: ${task.assigned_to}`)
      })
    }
    
    // Check team memberships
    console.log('\n👥 Checking team memberships:')
    const { data: teamMembers, error: teamError } = await supabase
      .from('team_members')
      .select(`
        user_id,
        team_id,
        role,
        profiles!inner(email),
        teams!inner(name)
      `)
      .limit(10)
    
    if (teamError) {
      console.error('❌ Cannot access team members:', teamError)
    } else {
      console.log(`Found ${teamMembers?.length || 0} team memberships`)
      teamMembers?.forEach(tm => {
        console.log(`  - ${tm.profiles.email} in "${tm.teams.name}" as ${tm.role}`)
      })
    }
    
    // Check team properties
    console.log('\n🏠 Checking team properties:')
    const { data: teamProps, error: teamPropsError } = await supabase
      .from('team_properties')
      .select(`
        property_id,
        team_id,
        properties!inner(name),
        teams!inner(name)
      `)
      .limit(10)
    
    if (teamPropsError) {
      console.error('❌ Cannot access team properties:', teamPropsError)
    } else {
      console.log(`Found ${teamProps?.length || 0} team property associations`)
      teamProps?.forEach(tp => {
        console.log(`  - Property "${tp.properties.name}" assigned to team "${tp.teams.name}"`)
      })
    }
    
    // Try to call the RPC function with a test user ID
    if (testUser) {
      console.log('\n🔧 Testing RPC function with test user:')
      const { data: rpcTasks, error: rpcError } = await supabase.rpc('get_maintenance_tasks_for_user', {
        p_user_id: testUser.id
      })
      
      if (rpcError) {
        console.error('❌ RPC function error:', rpcError)
        console.log('This suggests the migration with the RPC function was not applied')
      } else {
        console.log(`✅ RPC function works, returned ${rpcTasks?.length || 0} tasks`)
        rpcTasks?.forEach(task => {
          console.log(`  - "${task.title}" | Property: ${task.property_id} | Team: ${task.team_id}`)
        })
      }
    }
    
  } catch (error) {
    console.error('❌ Check error:', error)
  }
}

checkDatabaseFunctions().catch(console.error)