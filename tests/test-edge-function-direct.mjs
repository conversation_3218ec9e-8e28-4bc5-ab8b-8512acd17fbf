import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testEdgeFunctionDirect() {
  console.log('🧪 Testing accept-invitation-direct Edge Function')
  console.log('================================================')
  console.log('')
  
  const testData = {
    token: '00a1de99-c0f6-46f5-b4f7-302f00079fff',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    first_name: 'Test',
    last_name: 'ServiceProvider',
    role: 'service_provider'
  }
  
  console.log('📋 Test Data:')
  console.log('=============')
  console.log('Token:', testData.token)
  console.log('Email:', testData.email)
  console.log('First Name:', testData.first_name)
  console.log('Last Name:', testData.last_name)
  console.log('Role:', testData.role)
  console.log('')
  
  try {
    console.log('🚀 Calling accept-invitation-direct edge function...')
    
    const { data, error } = await supabase.functions.invoke('accept-invitation-direct', {
      body: testData
    })
    
    console.log('')
    console.log('📊 RESPONSE:')
    console.log('============')
    
    if (error) {
      console.log('❌ Error occurred:')
      console.log('Error object:', error)
      console.log('Error message:', error.message)
      console.log('Error details:', error.details)
      console.log('Error context:', error.context)
      
      // Try to get more details from the error
      if (error.context && error.context.body) {
        try {
          const errorBody = JSON.parse(error.context.body)
          console.log('Error body parsed:', errorBody)
        } catch (parseError) {
          console.log('Error body (raw):', error.context.body)
        }
      }
    } else {
      console.log('✅ Success!')
      console.log('Response data:', data)
    }
    
    console.log('')
    console.log('🔍 DEBUGGING STEPS:')
    console.log('===================')
    
    if (error) {
      console.log('1. Check if the invitation token is still valid')
      console.log('2. Verify the edge function is deployed correctly')
      console.log('3. Check edge function logs in Supabase dashboard')
      console.log('4. Verify all required parameters are provided')
      console.log('5. Check if the email already exists in the system')
      
      // Let's check if the email already exists
      console.log('')
      console.log('🔍 Checking if email already exists...')
      
      try {
        const { data: existingUser, error: userError } = await supabase.auth.signInWithPassword({
          email: testData.email,
          password: testData.password
        })
        
        if (existingUser?.user) {
          console.log('⚠️  User already exists with this email!')
          console.log('User ID:', existingUser.user.id)
          console.log('User email:', existingUser.user.email)
          console.log('This might be why registration is failing.')
          
          // Sign out the user
          await supabase.auth.signOut()
        } else if (userError) {
          console.log('✅ User does not exist (login failed as expected)')
          console.log('Login error:', userError.message)
        }
      } catch (loginError) {
        console.log('✅ User does not exist (login failed as expected)')
        console.log('Login error:', loginError.message)
      }
    }
    
  } catch (unexpectedError) {
    console.error('❌ Unexpected error:', unexpectedError)
  }
}

testEdgeFunctionDirect().catch(console.error)
