import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function debugSupabaseClientVsFetch() {
  console.log('🔍 DEBUGGING SUPABASE CLIENT VS DIRECT FETCH')
  console.log('============================================')
  console.log('')
  
  // Create a test invitation
  const uniqueId = Math.random().toString(36).substring(2, 8)
  const testEmail = `andyarnott+debug${uniqueId}@gmail.com`
  const newToken = crypto.randomUUID()
  
  console.log('Creating test invitation for:', testEmail)
  console.log('Token:', newToken)
  
  try {
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .insert({
        token: newToken,
        team_id: '80145fbc-b0b5-421f-acff-5c20a1e573e8',
        email: testEmail,
        role: 'service_provider',
        status: 'pending',
        invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.log('❌ Error creating invitation:', error.message)
      return
    }
    
    console.log('✅ Test invitation created!')
    console.log('')
    
    const testData = {
      token: newToken,
      email: testEmail,
      password: 'TestPassword123!',
      first_name: 'Debug',
      last_name: 'User',
      role: 'service_provider'
    }
    
    // Test 1: Direct fetch (this works)
    console.log('🔍 Test 1: Direct fetch call')
    try {
      const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
      })
      
      console.log('Direct fetch status:', response.status)
      const responseText = await response.text()
      console.log('Direct fetch response:', responseText.substring(0, 200) + '...')
      
      if (response.status === 200) {
        console.log('✅ Direct fetch: SUCCESS')
      } else {
        console.log('❌ Direct fetch: FAILED')
      }
    } catch (err) {
      console.log('❌ Direct fetch error:', err.message)
    }
    
    console.log('')
    
    // Create another invitation for the second test
    const uniqueId2 = Math.random().toString(36).substring(2, 8)
    const testEmail2 = `andyarnott+debug2${uniqueId2}@gmail.com`
    const newToken2 = crypto.randomUUID()
    
    const { data: invitation2, error: error2 } = await supabase
      .from('team_invitations')
      .insert({
        token: newToken2,
        team_id: '80145fbc-b0b5-421f-acff-5c20a1e573e8',
        email: testEmail2,
        role: 'service_provider',
        status: 'pending',
        invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error2) {
      console.log('❌ Error creating second invitation:', error2.message)
      return
    }
    
    const testData2 = {
      token: newToken2,
      email: testEmail2,
      password: 'TestPassword123!',
      first_name: 'Debug2',
      last_name: 'User',
      role: 'service_provider'
    }
    
    // Test 2: Supabase client call (this fails)
    console.log('🔍 Test 2: Supabase client call')
    try {
      const result = await supabase.functions.invoke('accept-invitation-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData2)
      })
      
      console.log('Supabase client error:', result.error)
      console.log('Supabase client data:', result.data)
      
      if (result.error) {
        console.log('❌ Supabase client: FAILED')
        console.log('Error details:', result.error.message)
        console.log('Error context status:', result.error.context?.status)
        console.log('Error context statusText:', result.error.context?.statusText)
        
        // Try to get the response body from the error context
        if (result.error.context) {
          try {
            const errorBody = await result.error.context.text()
            console.log('Error response body:', errorBody)
          } catch (bodyError) {
            console.log('Could not read error response body')
          }
        }
      } else {
        console.log('✅ Supabase client: SUCCESS')
      }
    } catch (err) {
      console.log('❌ Supabase client exception:', err.message)
    }
    
    console.log('')
    console.log('🔍 ANALYSIS:')
    console.log('============')
    console.log('Direct fetch works, but Supabase client fails.')
    console.log('This suggests there might be a difference in how the')
    console.log('Supabase client sends the request vs direct fetch.')
    console.log('')
    console.log('Possible issues:')
    console.log('1. Different headers being sent')
    console.log('2. Different request format')
    console.log('3. Authentication differences')
    console.log('4. Body serialization differences')
    
  } catch (err) {
    console.error('❌ Error in debug test:', err.message)
  }
}

debugSupabaseClientVsFetch().catch(console.error)
