import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

async function debugEdgeFunctionError() {
  console.log('🔍 DEBUGGING EDGE FUNCTION ERROR')
  console.log('================================')
  console.log('')
  
  const testData = {
    token: '7f371193-f52d-4e43-8eef-69a7b84919a0',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON><PERSON>',
    role: 'service_provider'
  }
  
  console.log('Request data:', JSON.stringify(testData, null, 2))
  console.log('')
  
  try {
    // Make direct fetch request to get full error details
    const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    })
    
    console.log('Response status:', response.status)
    console.log('Response status text:', response.statusText)
    console.log('')
    
    // Get the response body
    const responseText = await response.text()
    console.log('Response body (raw):', responseText)
    console.log('')
    
    // Try to parse as JSON
    try {
      const responseJson = JSON.parse(responseText)
      console.log('Response body (parsed):')
      console.log(JSON.stringify(responseJson, null, 2))
      
      if (responseJson.error) {
        console.log('')
        console.log('🚨 ERROR DETAILS:')
        console.log('================')
        console.log('Error message:', responseJson.error)
        
        // Analyze common error patterns
        if (responseJson.error.includes('already exists')) {
          console.log('')
          console.log('💡 LIKELY CAUSE: User with this email already exists')
          console.log('SOLUTION: Use a different email or check if user already exists')
        } else if (responseJson.error.includes('permission')) {
          console.log('')
          console.log('💡 LIKELY CAUSE: Permission/RLS policy issue')
          console.log('SOLUTION: Check RLS policies and service role permissions')
        } else if (responseJson.error.includes('constraint')) {
          console.log('')
          console.log('💡 LIKELY CAUSE: Database constraint violation')
          console.log('SOLUTION: Check for duplicate data or foreign key issues')
        }
      }
    } catch (parseError) {
      console.log('❌ Could not parse response as JSON:', parseError.message)
    }
    
  } catch (err) {
    console.error('❌ Request failed:', err.message)
  }
}

debugEdgeFunctionError().catch(console.error)
