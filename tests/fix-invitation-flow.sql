-- Fix Team Invitation Flow Issues
-- This script addresses the core issues preventing proper team invitation flow

-- 1. Add missing columns to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS first_name text;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS last_name text;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS role text DEFAULT 'property_manager';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_super_admin boolean DEFAULT false;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS phone text;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS timezone text DEFAULT 'UTC';

-- 2. Update existing profiles with missing data
UPDATE profiles 
SET first_name = COALESCE(first_name, split_part(full_name, ' ', 1), 'User'),
    last_name = COALESCE(last_name, split_part(full_name, ' ', 2), 'Name'),
    role = COALESCE(role, 'property_manager')
WHERE first_name IS NULL OR last_name IS NULL OR role IS NULL;

-- 3. Add RLS policies for teams table
DROP POLICY IF EXISTS "Users can view teams they belong to" ON teams;
DROP POLICY IF EXISTS "Users can manage teams they own" ON teams;

CREATE POLICY "Users can view teams they belong to" ON teams
    FOR SELECT USING (
        owner_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM team_members 
            WHERE team_id = teams.id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage teams they own" ON teams
    FOR ALL USING (owner_id = auth.uid());

-- 4. Add RLS policies for profiles table if missing
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view team member profiles" ON profiles;

CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view team member profiles" ON profiles
    FOR SELECT USING (
        auth.uid() = id OR 
        EXISTS (
            SELECT 1 FROM team_members tm1, team_members tm2 
            WHERE tm1.team_id = tm2.team_id 
            AND tm1.user_id = auth.uid() 
            AND tm2.user_id = profiles.id
        )
    );

-- 5. Add RLS policies for team_members table
DROP POLICY IF EXISTS "Users can view team memberships they belong to" ON team_members;
DROP POLICY IF EXISTS "Team owners can manage team memberships" ON team_members;

CREATE POLICY "Users can view team memberships they belong to" ON team_members
    FOR SELECT USING (
        user_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = team_members.team_id 
            AND teams.owner_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM team_members tm 
            WHERE tm.team_id = team_members.team_id 
            AND tm.user_id = auth.uid()
        )
    );

CREATE POLICY "Team owners can manage team memberships" ON team_members
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = team_members.team_id 
            AND teams.owner_id = auth.uid()
        )
    );

-- 6. Update team_members with proper roles from invitations
UPDATE team_members 
SET role = ti.role
FROM team_invitations ti
WHERE team_members.team_id = ti.team_id 
  AND team_members.user_id IN (
    SELECT p.id FROM profiles p WHERE p.email = ti.email
  )
  AND team_members.role IS NULL
  AND ti.status = 'accepted';

-- 7. Create essential RPC functions
CREATE OR REPLACE FUNCTION get_user_teams(user_id uuid)
RETURNS TABLE(
    team_id uuid,
    team_name text,
    team_description text,
    user_role text,
    is_owner boolean,
    member_count bigint,
    created_at timestamptz
) 
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT 
        t.id as team_id,
        t.name as team_name,
        t.description as team_description,
        COALESCE(tm.role, 'owner') as user_role,
        (t.owner_id = user_id) as is_owner,
        (SELECT COUNT(*) FROM team_members WHERE team_id = t.id) as member_count,
        t.created_at
    FROM teams t
    LEFT JOIN team_members tm ON t.id = tm.team_id AND tm.user_id = user_id
    WHERE t.owner_id = user_id OR tm.user_id = user_id;
$$;

CREATE OR REPLACE FUNCTION get_team_members(team_id uuid)
RETURNS TABLE(
    user_id uuid,
    email text,
    first_name text,
    last_name text,
    role text,
    joined_at timestamptz
) 
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT 
        p.id as user_id,
        p.email,
        p.first_name,
        p.last_name,
        COALESCE(tm.role, 'owner') as role,
        COALESCE(tm.created_at, t.created_at) as joined_at
    FROM profiles p
    LEFT JOIN team_members tm ON p.id = tm.user_id AND tm.team_id = team_id
    LEFT JOIN teams t ON t.id = team_id
    WHERE tm.user_id = p.id OR (t.owner_id = p.id AND t.id = team_id);
$$;

-- 8. Create invitation acceptance function
CREATE OR REPLACE FUNCTION process_invitation_acceptance(
    p_token text,
    p_user_id uuid
)
RETURNS TABLE(
    success boolean,
    error text,
    team_id uuid,
    team_name text,
    role text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_invitation team_invitations%ROWTYPE;
    v_team teams%ROWTYPE;
    v_profile profiles%ROWTYPE;
BEGIN
    -- Get the invitation
    SELECT * INTO v_invitation
    FROM team_invitations
    WHERE token = p_token AND status = 'pending';
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Invalid or expired invitation token'::text, NULL::uuid, ''::text, ''::text;
        RETURN;
    END IF;
    
    -- Check if invitation has expired
    IF v_invitation.expires_at < now() THEN
        RETURN QUERY SELECT false, 'Invitation has expired'::text, NULL::uuid, ''::text, ''::text;
        RETURN;
    END IF;
    
    -- Get the team
    SELECT * INTO v_team
    FROM teams
    WHERE id = v_invitation.team_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Team not found'::text, NULL::uuid, ''::text, ''::text;
        RETURN;
    END IF;
    
    -- Get the user profile
    SELECT * INTO v_profile
    FROM profiles
    WHERE id = p_user_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'User profile not found'::text, NULL::uuid, ''::text, ''::text;
        RETURN;
    END IF;
    
    -- Check if user is already a member
    IF EXISTS (
        SELECT 1 FROM team_members 
        WHERE team_id = v_invitation.team_id AND user_id = p_user_id
    ) THEN
        -- Update the invitation as accepted
        UPDATE team_invitations
        SET status = 'accepted', accepted_at = now(), updated_at = now()
        WHERE id = v_invitation.id;
        
        RETURN QUERY SELECT true, ''::text, v_team.id, v_team.name, v_invitation.role;
        RETURN;
    END IF;
    
    -- Add user to team
    INSERT INTO team_members (team_id, user_id, role, invited_by, invited_at, accepted_at, created_at)
    VALUES (v_invitation.team_id, p_user_id, v_invitation.role, v_invitation.invited_by, v_invitation.created_at, now(), now());
    
    -- Mark invitation as accepted
    UPDATE team_invitations
    SET status = 'accepted', accepted_at = now(), updated_at = now()
    WHERE id = v_invitation.id;
    
    -- If user is a service provider, ensure they have a service_providers entry
    IF v_invitation.role = 'service_provider' THEN
        INSERT INTO service_providers (user_id, name, email, phone, specialties, created_at, updated_at)
        VALUES (p_user_id, 
                COALESCE(v_profile.first_name || ' ' || v_profile.last_name, v_profile.email), 
                v_profile.email, 
                v_profile.phone, 
                '[]'::jsonb, 
                now(), 
                now())
        ON CONFLICT (user_id) DO NOTHING;
    END IF;
    
    RETURN QUERY SELECT true, ''::text, v_team.id, v_team.name, v_invitation.role;
    
EXCEPTION WHEN OTHERS THEN
    RETURN QUERY SELECT false, SQLERRM::text, NULL::uuid, ''::text, ''::text;
END;
$$;

-- 9. Create legacy compatibility function
CREATE OR REPLACE FUNCTION accept_invitation_and_add_member(
    p_token text,
    p_user_id uuid
)
RETURNS TABLE(
    success boolean,
    error text,
    team_id uuid,
    team_name text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result_row RECORD;
BEGIN
    -- Use the new function
    SELECT * INTO result_row
    FROM process_invitation_acceptance(p_token, p_user_id);
    
    RETURN QUERY SELECT 
        result_row.success,
        result_row.error,
        result_row.team_id,
        result_row.team_name;
END;
$$;

-- 10. Create trigger for new user profile creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, email, created_at, updated_at)
    VALUES (
        NEW.id,
        NEW.email,
        now(),
        now()
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        updated_at = now();
    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    -- If insertion fails, just return NEW
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- 11. Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_user_teams(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_team_members(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION process_invitation_acceptance(text, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION accept_invitation_and_add_member(text, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION handle_new_user() TO authenticated;