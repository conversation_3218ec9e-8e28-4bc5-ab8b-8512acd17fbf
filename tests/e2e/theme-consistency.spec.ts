import { test, expect } from '@playwright/test';

// Test credentials
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'Newsig1!!!';

async function login(page: any) {
  await page.goto('/');
  
  // <PERSON>le redirect to login if not authenticated
  if (page.url().includes('/login')) {
    await page.fill('input[type="email"]', TEST_EMAIL);
    await page.fill('input[type="password"]', TEST_PASSWORD);
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  }
}

async function toggleDarkMode(page: any) {
  // Look for theme toggle button - adjust selector as needed
  const themeToggle = page.locator('[data-testid="theme-toggle"], [aria-label*="theme"], button:has-text("Theme")').first();
  if (await themeToggle.isVisible()) {
    await themeToggle.click();
    await page.waitForTimeout(500); // Wait for theme transition
  } else {
    // If no toggle found, manually add dark class
    await page.evaluate(() => {
      document.documentElement.classList.toggle('dark');
    });
    await page.waitForTimeout(500);
  }
}

test.describe('Theme Consistency Tests', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
  });

  test('Dashboard - Light and Dark Mode Consistency', async ({ page }) => {
    // Test light mode
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Ensure light mode
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/dashboard-light.png',
      fullPage: true 
    });
    
    // Switch to dark mode
    await toggleDarkMode(page);
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/dashboard-dark.png',
      fullPage: true 
    });
    
    // Verify text visibility in dark mode
    const textElements = await page.locator('h1, h2, h3, h4, h5, h6, p, span').all();
    for (const element of textElements.slice(0, 10)) { // Check first 10 elements
      const isVisible = await element.isVisible();
      const color = await element.evaluate(el => getComputedStyle(el).color);
      expect(isVisible).toBe(true);
      expect(color).not.toBe('rgb(0, 0, 0)'); // Should not be black in dark mode
    }
  });

  test('Mobile Dashboard - Responsive Design', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Light mode mobile
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/dashboard-mobile-light.png',
      fullPage: true 
    });
    
    // Dark mode mobile
    await toggleDarkMode(page);
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/dashboard-mobile-dark.png',
      fullPage: true 
    });
    
    // Check mobile touch targets
    const buttons = await page.locator('button, a[role="button"]').all();
    for (const button of buttons.slice(0, 5)) {
      const boundingBox = await button.boundingBox();
      if (boundingBox) {
        expect(boundingBox.height).toBeGreaterThanOrEqual(44); // Minimum touch target
        expect(boundingBox.width).toBeGreaterThanOrEqual(44);
      }
    }
  });

  test('Properties Page - Theme Consistency', async ({ page }) => {
    await page.goto('/properties');
    await page.waitForLoadState('networkidle');
    
    // Light mode
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/properties-light.png',
      fullPage: true 
    });
    
    // Dark mode
    await toggleDarkMode(page);
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/properties-dark.png',
      fullPage: true 
    });
  });

  test('Inventory Page - Theme Consistency', async ({ page }) => {
    await page.goto('/inventory');
    await page.waitForLoadState('networkidle');
    
    // Light mode
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/inventory-light.png',
      fullPage: true 
    });
    
    // Dark mode
    await toggleDarkMode(page);
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/inventory-dark.png',
      fullPage: true 
    });
  });

  test('Maintenance Page - Theme Consistency', async ({ page }) => {
    await page.goto('/maintenance');
    await page.waitForLoadState('networkidle');
    
    // Light mode
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/maintenance-light.png',
      fullPage: true 
    });
    
    // Dark mode
    await toggleDarkMode(page);
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/maintenance-dark.png',
      fullPage: true 
    });
  });

  test('Forms and Dialogs - Theme Consistency', async ({ page }) => {
    await page.goto('/properties');
    await page.waitForLoadState('networkidle');
    
    // Try to open add property dialog
    const addButton = page.locator('button:has-text("Add Property"), button:has-text("Add"), [data-testid="add-property"]').first();
    if (await addButton.isVisible()) {
      await addButton.click();
      await page.waitForTimeout(1000);
      
      // Light mode dialog
      await page.evaluate(() => {
        document.documentElement.classList.remove('dark');
      });
      await page.waitForTimeout(500);
      
      await page.screenshot({ 
        path: 'tests/screenshots/dialog-light.png' 
      });
      
      // Dark mode dialog
      await toggleDarkMode(page);
      await page.waitForTimeout(500);
      
      await page.screenshot({ 
        path: 'tests/screenshots/dialog-dark.png' 
      });
    }
  });

  test('Navigation and Sidebar - Theme Consistency', async ({ page }) => {
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Desktop view
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Light mode
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/navigation-desktop-light.png' 
    });
    
    // Dark mode
    await toggleDarkMode(page);
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/navigation-desktop-dark.png' 
    });
    
    // Mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Light mode mobile
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/navigation-mobile-light.png' 
    });
    
    // Dark mode mobile
    await toggleDarkMode(page);
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/navigation-mobile-dark.png' 
    });
  });
});