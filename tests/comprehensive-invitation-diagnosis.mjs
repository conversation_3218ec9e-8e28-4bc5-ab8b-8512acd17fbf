import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Test data from the current invitation
const testInvitation = {
  token: '7871-a5-f524-ac4b-8eef-8e99e5e67a0',
  email: '<EMAIL>',
  password: 'TestPassword123!',
  first_name: 'Andrew2',
  last_name: 'A<PERSON>tt2',
  role: 'service_provider'
}

console.log('🔍 COMPREHENSIVE SERVICE PROVIDER INVITATION DIAGNOSIS')
console.log('=====================================================')
console.log('')

// Phase 1: Environment & Deployment Verification
async function phase1_EnvironmentVerification() {
  console.log('📋 PHASE 1: ENVIRONMENT & DEPLOYMENT VERIFICATION')
  console.log('================================================')
  console.log('')
  
  // Test 1.1: Basic Supabase connectivity
  console.log('🔍 Test 1.1: Basic Supabase Connectivity')
  try {
    const { data, error } = await supabase.from('profiles').select('count').limit(1)
    if (error) {
      console.log('❌ Supabase connection failed:', error.message)
      return false
    }
    console.log('✅ Supabase connection successful')
  } catch (err) {
    console.log('❌ Supabase connection error:', err.message)
    return false
  }
  
  // Test 1.2: Edge function accessibility
  console.log('')
  console.log('🔍 Test 1.2: Edge Function Accessibility')
  
  // Test get-invitation-details function
  try {
    const response = await fetch(`${supabaseUrl}/functions/v1/get-invitation-details`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token: 'test-token' })
    })
    
    console.log('get-invitation-details response status:', response.status)
    if (response.status === 404) {
      console.log('❌ get-invitation-details function not found or not deployed')
      return false
    }
    console.log('✅ get-invitation-details function is accessible')
  } catch (err) {
    console.log('❌ get-invitation-details function error:', err.message)
    return false
  }
  
  // Test accept-invitation-direct function
  try {
    const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token: 'test-token' })
    })
    
    console.log('accept-invitation-direct response status:', response.status)
    if (response.status === 404) {
      console.log('❌ accept-invitation-direct function not found or not deployed')
      return false
    }
    console.log('✅ accept-invitation-direct function is accessible')
  } catch (err) {
    console.log('❌ accept-invitation-direct function error:', err.message)
    return false
  }
  
  console.log('')
  console.log('✅ Phase 1 Complete: Environment verification passed')
  return true
}

// Phase 2: Database & Permissions Testing
async function phase2_DatabaseTesting() {
  console.log('📋 PHASE 2: DATABASE & PERMISSIONS TESTING')
  console.log('==========================================')
  console.log('')
  
  // Test 2.1: Check if invitation exists
  console.log('🔍 Test 2.1: Invitation Existence Check')
  try {
    const { data, error } = await supabase
      .from('team_invitations')
      .select('id, team_id, email, role, status')
      .eq('token', testInvitation.token)
      .maybeSingle()
    
    if (error) {
      console.log('❌ Error checking invitation:', error.message)
      return false
    }
    
    if (!data) {
      console.log('❌ Invitation not found with token:', testInvitation.token)
      return false
    }
    
    console.log('✅ Invitation found:', data)
    console.log('   Team ID:', data.team_id)
    console.log('   Email:', data.email)
    console.log('   Role:', data.role)
    console.log('   Status:', data.status)
  } catch (err) {
    console.log('❌ Invitation check error:', err.message)
    return false
  }
  
  // Test 2.2: Check RPC functions exist
  console.log('')
  console.log('🔍 Test 2.2: RPC Functions Check')
  
  try {
    // Test create_profile_safely RPC
    const { data, error } = await supabase.rpc('create_profile_safely', {
      p_user_id: '00000000-0000-0000-0000-000000000000',
      p_first_name: 'Test',
      p_last_name: 'User',
      p_role: 'service_provider',
      p_email: '<EMAIL>'
    })
    
    // We expect this to fail with a specific error, not "function not found"
    if (error && error.message.includes('function') && error.message.includes('does not exist')) {
      console.log('❌ create_profile_safely RPC function does not exist')
      return false
    }
    console.log('✅ create_profile_safely RPC function exists')
  } catch (err) {
    console.log('❌ RPC function test error:', err.message)
  }
  
  try {
    // Test accept_invitation_and_add_member RPC
    const { data, error } = await supabase.rpc('accept_invitation_and_add_member', {
      p_token: 'test-token',
      p_user_id: '00000000-0000-0000-0000-000000000000'
    })
    
    // We expect this to fail with a specific error, not "function not found"
    if (error && error.message.includes('function') && error.message.includes('does not exist')) {
      console.log('❌ accept_invitation_and_add_member RPC function does not exist')
      return false
    }
    console.log('✅ accept_invitation_and_add_member RPC function exists')
  } catch (err) {
    console.log('❌ RPC function test error:', err.message)
  }
  
  console.log('')
  console.log('✅ Phase 2 Complete: Database testing passed')
  return true
}

// Phase 3: Edge Function Testing
async function phase3_EdgeFunctionTesting() {
  console.log('📋 PHASE 3: EDGE FUNCTION TESTING')
  console.log('=================================')
  console.log('')
  
  // Test 3.1: Test get-invitation-details with real token
  console.log('🔍 Test 3.1: get-invitation-details with real token')
  try {
    const { data, error } = await supabase.functions.invoke('get-invitation-details', {
      body: { token: testInvitation.token }
    })
    
    if (error) {
      console.log('❌ get-invitation-details error:', error)
      return false
    }
    
    if (!data?.success) {
      console.log('❌ get-invitation-details failed:', data?.error)
      return false
    }
    
    console.log('✅ get-invitation-details successful')
    console.log('   Invitation data:', data.invitation)
  } catch (err) {
    console.log('❌ get-invitation-details exception:', err.message)
    return false
  }
  
  // Test 3.2: Test accept-invitation-direct with real data
  console.log('')
  console.log('🔍 Test 3.2: accept-invitation-direct with real data')
  console.log('Request data:', testInvitation)
  
  try {
    const { data, error } = await supabase.functions.invoke('accept-invitation-direct', {
      body: testInvitation
    })
    
    console.log('')
    console.log('📊 ACCEPT INVITATION RESULT:')
    console.log('============================')
    
    if (error) {
      console.log('❌ accept-invitation-direct error:')
      console.log('   Error object:', error)
      console.log('   Error message:', error.message)
      console.log('   Error status:', error.status)
      
      // Try to get more details from the error
      if (error.context) {
        console.log('   Error context:', error.context)
      }
      
      return false
    }
    
    if (!data?.success) {
      console.log('❌ accept-invitation-direct failed:')
      console.log('   Response data:', data)
      return false
    }
    
    console.log('✅ accept-invitation-direct successful!')
    console.log('   User ID:', data.user?.id)
    console.log('   Team ID:', data.team_id)
    console.log('   Message:', data.message)
    
  } catch (err) {
    console.log('❌ accept-invitation-direct exception:', err.message)
    return false
  }
  
  console.log('')
  console.log('✅ Phase 3 Complete: Edge function testing passed')
  return true
}

// Run all phases
async function runComprehensiveDiagnosis() {
  try {
    console.log('Starting comprehensive diagnosis...')
    console.log('')
    
    const phase1Success = await phase1_EnvironmentVerification()
    if (!phase1Success) {
      console.log('')
      console.log('🛑 DIAGNOSIS STOPPED: Phase 1 failed')
      console.log('Fix environment and deployment issues before proceeding.')
      return
    }
    
    console.log('')
    const phase2Success = await phase2_DatabaseTesting()
    if (!phase2Success) {
      console.log('')
      console.log('🛑 DIAGNOSIS STOPPED: Phase 2 failed')
      console.log('Fix database and permissions issues before proceeding.')
      return
    }
    
    console.log('')
    const phase3Success = await phase3_EdgeFunctionTesting()
    if (!phase3Success) {
      console.log('')
      console.log('🛑 DIAGNOSIS STOPPED: Phase 3 failed')
      console.log('Edge function issues identified. Check function logs for details.')
      return
    }
    
    console.log('')
    console.log('🎉 ALL PHASES PASSED!')
    console.log('The invitation system should be working now.')
    
  } catch (error) {
    console.error('❌ Unexpected error during diagnosis:', error)
  }
}

runComprehensiveDiagnosis().catch(console.error)
