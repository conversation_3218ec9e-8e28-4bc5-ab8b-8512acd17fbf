import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function debugCurrentInvitation() {
  console.log('🔍 Debugging Current Invitation Issue')
  console.log('====================================')
  console.log('')
  
  const currentToken = '7871-a5-f524-ac4b-8eef-8e99e5e67a0'
  const testData = {
    token: currentToken,
    email: '<EMAIL>',
    password: 'TestPassword123!',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON><PERSON>',
    role: 'service_provider'
  }
  
  console.log('📋 Current Invitation Data:')
  console.log('===========================')
  console.log('Token:', testData.token)
  console.log('Email:', testData.email)
  console.log('First Name:', testData.first_name)
  console.log('Last Name:', testData.last_name)
  console.log('Role:', testData.role)
  console.log('')
  
  try {
    // Step 1: Check if invitation exists and is valid
    console.log('🔍 Step 1: Checking invitation details...')
    const { data: inviteDetails, error: inviteError } = await supabase.functions.invoke('get-invitation-details', {
      body: { token: currentToken }
    })
    
    if (inviteError) {
      console.log('❌ Error getting invitation details:', inviteError)
      return
    }
    
    if (!inviteDetails?.success) {
      console.log('❌ Invitation details failed:', inviteDetails?.error)
      return
    }
    
    console.log('✅ Invitation details retrieved successfully')
    console.log('Invitation data:', inviteDetails.invitation)
    console.log('')
    
    // Step 2: Check if user already exists
    console.log('🔍 Step 2: Checking if user already exists...')
    try {
      const { data: existingUser, error: loginError } = await supabase.auth.signInWithPassword({
        email: testData.email,
        password: testData.password
      })
      
      if (existingUser?.user) {
        console.log('⚠️  User already exists!')
        console.log('User ID:', existingUser.user.id)
        console.log('This might be why registration is failing.')
        
        // Sign out
        await supabase.auth.signOut()
        console.log('Signed out existing user')
        console.log('')
      }
    } catch (loginErr) {
      console.log('✅ User does not exist (login failed as expected)')
      console.log('')
    }
    
    // Step 3: Test the accept-invitation-direct function
    console.log('🔍 Step 3: Testing accept-invitation-direct function...')
    
    const { data: acceptResult, error: acceptError } = await supabase.functions.invoke('accept-invitation-direct', {
      body: testData
    })
    
    console.log('')
    console.log('📊 ACCEPT INVITATION RESULT:')
    console.log('============================')
    
    if (acceptError) {
      console.log('❌ Accept invitation error:')
      console.log('Error object:', acceptError)
      console.log('Error message:', acceptError.message)
      console.log('Error status:', acceptError.status)
      
      // Try to get more details from the error
      if (acceptError.context && acceptError.context.body) {
        try {
          const errorBody = JSON.parse(acceptError.context.body)
          console.log('Error body parsed:', errorBody)
        } catch (parseError) {
          console.log('Error body (raw):', acceptError.context.body)
        }
      }
      
      console.log('')
      console.log('🔧 DEBUGGING STEPS:')
      console.log('===================')
      console.log('1. Check edge function logs in Supabase dashboard')
      console.log('2. Verify the accept-invitation-direct function is deployed')
      console.log('3. Check if there are any database constraint violations')
      console.log('4. Verify RLS policies allow the operations')
      
    } else {
      console.log('✅ Accept invitation succeeded!')
      console.log('Response data:', acceptResult)
      
      if (acceptResult?.success) {
        console.log('')
        console.log('🎉 SUCCESS! User created and added to team:')
        console.log('User ID:', acceptResult.user?.id)
        console.log('Team ID:', acceptResult.team_id)
        console.log('Message:', acceptResult.message)
      } else {
        console.log('⚠️  Function returned success=false:')
        console.log('Error:', acceptResult?.error)
      }
    }
    
    // Step 4: If there was an error, try the manual fix approach
    if (acceptError || !acceptResult?.success) {
      console.log('')
      console.log('🔧 Step 4: Attempting manual user creation...')
      
      // Try to create user manually first
      try {
        const { data: manualUser, error: manualError } = await supabase.auth.signUp({
          email: testData.email,
          password: testData.password,
          options: {
            data: {
              first_name: testData.first_name,
              last_name: testData.last_name,
              role: testData.role
            }
          }
        })
        
        if (manualError) {
          console.log('❌ Manual user creation failed:', manualError.message)
        } else {
          console.log('✅ Manual user creation succeeded!')
          console.log('User ID:', manualUser.user?.id)
          
          if (manualUser.user?.id) {
            // Try to add to team using RPC
            console.log('🔧 Attempting to add user to team via RPC...')
            
            const { data: rpcResult, error: rpcError } = await supabase.rpc('accept_invitation_and_add_member', {
              p_token: currentToken,
              p_user_id: manualUser.user.id
            })
            
            if (rpcError) {
              console.log('❌ RPC failed:', rpcError.message)
            } else {
              console.log('✅ RPC succeeded!')
              console.log('RPC result:', rpcResult)
            }
          }
        }
      } catch (manualErr) {
        console.log('❌ Manual creation error:', manualErr.message)
      }
    }
    
  } catch (unexpectedError) {
    console.error('❌ Unexpected error:', unexpectedError)
  }
}

debugCurrentInvitation().catch(console.error)
