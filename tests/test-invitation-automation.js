import puppeteer from 'puppeteer'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)
const APP_URL = 'http://localhost:8080'

async function testInvitationFlow() {
  let browser
  
  try {
    console.log('🚀 Starting automated invitation flow test...')
    
    // Test 1: Login with your credentials
    console.log('\n1. Testing login with your credentials...')
    
    browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 1280, height: 720 },
      slowMo: 500,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })
    
    const page = await browser.newPage()
    
    // Navigate to app
    await page.goto(APP_URL, { waitUntil: 'networkidle0' })
    
    // Wait for and fill login form
    await page.waitForSelector('input[type="email"]', { timeout: 10000 })
    await page.type('input[type="email"]', '<EMAIL>')
    await page.type('input[type="password"]', 'Newsig1!!!')
    
    // Submit login
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    const loginUrl = page.url()
    console.log('URL after login:', loginUrl)
    
    if (loginUrl.includes('dashboard') || loginUrl.includes('teams') || loginUrl.includes('properties')) {
      console.log('✅ Login successful!')
      
      // Check what's visible on the page
      const pageText = await page.evaluate(() => document.body.textContent)
      console.log('Page contains profile info:', 
        pageText.includes('Andrew') || pageText.includes('Arnott'))
      console.log('Page contains team info:', 
        pageText.toLowerCase().includes('team'))
      console.log('Page contains property info:', 
        pageText.toLowerCase().includes('property'))
      
      // Take a screenshot for debugging
      await page.screenshot({ path: 'login-success.png', fullPage: true })
      console.log('📸 Screenshot saved as login-success.png')
      
    } else {
      console.log('❌ Login failed or unexpected redirect')
      await page.screenshot({ path: 'login-failed.png', fullPage: true })
      console.log('📸 Screenshot saved as login-failed.png')
    }
    
    // Test 2: Test invitation link
    console.log('\n2. Testing invitation link...')
    const invitationToken = 'dcef0847-d237-4cd8-9fa4-51ad3093e8bc'
    const invitationUrl = `${APP_URL}/#/invite?token=${invitationToken}`
    
    // Sign out first
    try {
      await page.click('button[aria-label*="sign out"], button[aria-label*="logout"], a[href*="logout"]')
      await page.waitForTimeout(2000)
    } catch (e) {
      console.log('Could not find sign out button, navigating to invitation directly')
    }
    
    // Navigate to invitation URL
    await page.goto(invitationUrl, { waitUntil: 'networkidle0' })
    await page.waitForTimeout(3000)
    
    const invitePageText = await page.evaluate(() => document.body.textContent)
    console.log('Invitation page contains expected text:', 
      invitePageText.includes('invitation') || invitePageText.includes('invite'))
    
    await page.screenshot({ path: 'invitation-page.png', fullPage: true })
    console.log('📸 Screenshot saved as invitation-page.png')
    
    // Test 3: Try to register with the test email
    console.log('\n3. Testing registration with test email...')
    
    // Look for register/signup button or tab
    try {
      const registerTab = await page.$('button:has-text("Register"), button:has-text("Sign up"), [role="tab"]:has-text("Register")')
      if (registerTab) {
        await registerTab.click()
        await page.waitForTimeout(1000)
      }
    } catch (e) {
      console.log('Could not find register tab, looking for registration form')
    }
    
    // Try to fill registration form
    try {
      // Look for registration fields
      const emailField = await page.$('input[type="email"]')
      const passwordField = await page.$('input[type="password"]')
      const firstNameField = await page.$('input[placeholder*="first"], input[name*="first"]')
      const lastNameField = await page.$('input[placeholder*="last"], input[name*="last"]')
      
      if (emailField && passwordField) {
        console.log('Found registration form, filling with test data...')
        
        await emailField.click({ clickCount: 3 }) // Select all
        await emailField.type('<EMAIL>')
        
        if (firstNameField) {
          await firstNameField.type('Test')
        }
        if (lastNameField) {
          await lastNameField.type('User')
        }
        
        await passwordField.type('testpassword123')
        
        // Try to submit
        const submitButton = await page.$('button[type="submit"], button:has-text("Register"), button:has-text("Sign up")')
        if (submitButton) {
          await submitButton.click()
          await page.waitForTimeout(3000)
          
          console.log('Registration form submitted')
          await page.screenshot({ path: 'registration-attempt.png', fullPage: true })
          console.log('📸 Screenshot saved as registration-attempt.png')
        }
      }
    } catch (e) {
      console.log('Error with registration form:', e.message)
    }
    
    console.log('\n✅ Automated test completed!')
    console.log('\n📋 Manual testing steps:')
    console.log('1. Check login-success.png to see what you see after login')
    console.log('2. Check invitation-page.png to see the invitation page')
    console.log('3. Use this invitation link manually:')
    console.log(`   ${invitationUrl}`)
    console.log('4. Register with: <EMAIL>')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    if (browser) {
      await browser.close()
    }
  }
}

testInvitationFlow()