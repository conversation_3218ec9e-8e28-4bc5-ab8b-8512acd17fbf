import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testCompleteInvitationFlowFinal() {
  console.log('🎯 FINAL TEST: COMPLETE INVITATION FLOW')
  console.log('======================================')
  console.log('')
  
  // Create a completely fresh invitation
  const uniqueId = Math.random().toString(36).substring(2, 8)
  const testEmail = `andyarnott+final${uniqueId}@gmail.com`
  const newToken = crypto.randomUUID()
  
  console.log('Creating final test invitation for:', testEmail)
  console.log('Token:', newToken)
  console.log('')
  
  try {
    // Step 1: Create the invitation
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .insert({
        token: newToken,
        team_id: '80145fbc-b0b5-421f-acff-5c20a1e573e8',
        email: testEmail,
        role: 'service_provider',
        status: 'pending',
        invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.log('❌ Error creating invitation:', error.message)
      return
    }
    
    console.log('✅ Final test invitation created!')
    console.log('')
    
    // Step 2: Test the edge function directly (backend)
    console.log('🔍 Step 2: Testing backend (edge function)')
    
    const testData = {
      token: newToken,
      email: testEmail,
      password: 'TestPassword123!',
      first_name: 'Final',
      last_name: 'Test',
      role: 'service_provider'
    }
    
    const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    })
    
    console.log('Backend response status:', response.status)
    
    if (response.status === 200) {
      const data = await response.json()
      console.log('✅ Backend working perfectly!')
      console.log('   User ID:', data.user?.id)
      console.log('   Team ID:', data.team_id)
      console.log('   Is New User:', data.isNewUser)
      console.log('   Message:', data.message)
      console.log('')
      
      // Step 3: Test the frontend API function
      console.log('🔍 Step 3: Testing frontend API function')
      
      // Create another invitation for frontend test
      const frontendUniqueId = Math.random().toString(36).substring(2, 8)
      const frontendEmail = `andyarnott+frontend${frontendUniqueId}@gmail.com`
      const frontendToken = crypto.randomUUID()
      
      const { data: frontendInvitation, error: frontendError } = await supabase
        .from('team_invitations')
        .insert({
          token: frontendToken,
          team_id: '80145fbc-b0b5-421f-acff-5c20a1e573e8',
          email: frontendEmail,
          role: 'service_provider',
          status: 'pending',
          invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      
      if (frontendError) {
        console.log('❌ Error creating frontend test invitation:', frontendError.message)
        return
      }
      
      console.log('Frontend test invitation created for:', frontendEmail)
      console.log('')
      
      // Test the frontend function by calling the edge function the same way the frontend does
      const frontendResponse = await supabase.functions.invoke('accept-invitation-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token: frontendToken,
          email: frontendEmail,
          password: 'TestPassword123!',
          first_name: 'Frontend',
          last_name: 'Test',
          role: 'service_provider'
        })
      })
      
      console.log('Frontend API response:')
      console.log('   Error:', frontendResponse.error)
      console.log('   Data:', frontendResponse.data)
      console.log('')
      
      if (frontendResponse.error) {
        console.log('❌ Frontend API failed:', frontendResponse.error.message)
      } else if (frontendResponse.data && frontendResponse.data.success) {
        console.log('✅ Frontend API working perfectly!')
        console.log('   User ID:', frontendResponse.data.user?.id)
        console.log('   Team ID:', frontendResponse.data.team_id)
        console.log('   Is New User:', frontendResponse.data.isNewUser)
        console.log('')
        
        console.log('🎉 COMPLETE SUCCESS!')
        console.log('===================')
        console.log('✅ Backend edge function: WORKING')
        console.log('✅ Frontend API function: WORKING')
        console.log('✅ Database operations: WORKING')
        console.log('✅ User creation: WORKING')
        console.log('✅ Team assignment: WORKING')
        console.log('')
        console.log('🔗 WORKING INVITATION URL FOR FINAL TEST:')
        console.log('==========================================')
        const finalUrl = `https://www.stayfu.com/#/invite?token=${frontendToken}&team_id=80145fbc-b0b5-421f-acff-5c20a1e573e8`
        console.log(finalUrl)
        console.log('')
        console.log('📧 Email for testing:', frontendEmail)
        console.log('🔑 Token:', frontendToken)
        console.log('')
        console.log('🎯 WHAT TO EXPECT NOW:')
        console.log('======================')
        console.log('1. Visit the invitation URL above')
        console.log('2. See invitation details displayed correctly')
        console.log('3. Click "Create Account" button')
        console.log('4. Fill in the registration form (email pre-filled)')
        console.log('5. Click "Create Account & Accept Invitation"')
        console.log('6. SUCCESS! User created and added to team')
        console.log('7. Automatic redirect to teams page')
        console.log('')
        console.log('🚀 THE INVITATION FLOW IS NOW COMPLETELY FIXED!')
        
      } else {
        console.log('❌ Frontend API returned unsuccessful response:', frontendResponse.data)
      }
      
    } else {
      console.log('❌ Backend failed:', response.status)
      const errorText = await response.text()
      console.log('Error response:', errorText)
    }
    
  } catch (err) {
    console.error('❌ Error in final test:', err.message)
  }
}

testCompleteInvitationFlowFinal().catch(console.error)
