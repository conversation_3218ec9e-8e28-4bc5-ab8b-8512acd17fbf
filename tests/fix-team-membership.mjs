import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function fixTeamMembership() {
  console.log('🔧 Fixing Team Membership')
  console.log('=========================')
  console.log('')
  
  const userData = {
    userId: '32356846-1a7c-4367-baa2-d841baed0508',
    teamId: '80145fbc-b0b5-421f-acff-5c20a1e573e8',
    invitationToken: '00a1de99-c0f6-46f5-b4f7-302f00079fff',
    role: 'service_provider'
  }
  
  console.log('📋 User Data:')
  console.log('=============')
  console.log('User ID:', userData.userId)
  console.log('Team ID:', userData.teamId)
  console.log('Role:', userData.role)
  console.log('Invitation Token:', userData.invitationToken)
  console.log('')
  
  try {
    // Method 1: Try using the RPC function that should handle team membership
    console.log('🚀 Method 1: Using accept_invitation_and_add_member RPC')
    
    const { data: rpcResult, error: rpcError } = await supabase.rpc('accept_invitation_and_add_member', {
      p_token: userData.invitationToken,
      p_user_id: userData.userId
    })
    
    if (rpcError) {
      console.log('❌ RPC method failed:', rpcError.message)
      console.log('RPC error details:', rpcError)
    } else {
      console.log('✅ RPC method succeeded!')
      console.log('RPC result:', rpcResult)
    }
    
    console.log('')
    
    // Method 2: Try direct team membership insertion
    console.log('🚀 Method 2: Direct team membership insertion')
    
    const { data: insertResult, error: insertError } = await supabase
      .from('team_members')
      .upsert({
        team_id: userData.teamId,
        user_id: userData.userId,
        added_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b', // The inviter from the invitation
        status: 'active'
      })
      .select()
    
    if (insertError) {
      console.log('❌ Direct insertion failed:', insertError.message)
      console.log('Insert error details:', insertError)
    } else {
      console.log('✅ Direct insertion succeeded!')
      console.log('Insert result:', insertResult)
    }
    
    console.log('')
    
    // Method 3: Update invitation status
    console.log('🚀 Method 3: Update invitation status')
    
    const { data: updateResult, error: updateError } = await supabase
      .from('team_invitations')
      .update({
        status: 'accepted',
        updated_at: new Date().toISOString()
      })
      .eq('token', userData.invitationToken)
      .select()
    
    if (updateError) {
      console.log('❌ Invitation update failed:', updateError.message)
      console.log('Update error details:', updateError)
    } else {
      console.log('✅ Invitation update succeeded!')
      console.log('Update result:', updateResult)
    }
    
    console.log('')
    
    // Verify the results
    console.log('🔍 Verification: Checking team membership')
    
    const { data: teamMembers, error: verifyError } = await supabase
      .from('team_members')
      .select('*')
      .eq('user_id', userData.userId)
    
    if (verifyError) {
      console.log('❌ Verification failed:', verifyError.message)
    } else {
      console.log('✅ Verification results:')
      console.log('Team memberships found:', teamMembers?.length || 0)
      teamMembers?.forEach((membership, index) => {
        console.log(`${index + 1}. Team ID: ${membership.team_id}`)
        console.log(`   Status: ${membership.status}`)
        console.log(`   Added by: ${membership.added_by}`)
        console.log(`   Created: ${membership.created_at}`)
      })
    }
    
    console.log('')
    console.log('🔍 Verification: Checking invitation status')
    
    const { data: invitationStatus, error: inviteVerifyError } = await supabase
      .from('team_invitations')
      .select('status, updated_at')
      .eq('token', userData.invitationToken)
      .single()
    
    if (inviteVerifyError) {
      console.log('❌ Invitation verification failed:', inviteVerifyError.message)
    } else {
      console.log('✅ Invitation status:', invitationStatus.status)
      console.log('Last updated:', invitationStatus.updated_at)
    }
    
  } catch (unexpectedError) {
    console.error('❌ Unexpected error:', unexpectedError)
  }
}

fixTeamMembership().catch(console.error)
