import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

async function checkResponseBody() {
  console.log('🔍 CHECKING RESPONSE BODY FROM EDGE FUNCTION')
  console.log('============================================')
  
  // Create a completely new invitation to test
  const uniqueId = Math.random().toString(36).substring(2, 8)
  const testEmail = `andyarnott+check${uniqueId}@gmail.com`
  const newToken = crypto.randomUUID()
  
  console.log('Creating fresh invitation for:', testEmail)
  console.log('Token:', newToken)
  
  const supabase = createClient(supabaseUrl, supabaseAnonKey)
  
  try {
    // Create the invitation
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .insert({
        token: newToken,
        team_id: '80145fbc-b0b5-421f-acff-5c20a1e573e8',
        email: testEmail,
        role: 'service_provider',
        status: 'pending',
        invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.log('❌ Error creating invitation:', error.message)
      return
    }
    
    console.log('✅ Fresh invitation created!')
    console.log('')
    
    // Test the edge function
    const testData = {
      token: newToken,
      email: testEmail,
      password: 'TestPassword123!',
      first_name: 'Check',
      last_name: 'User',
      role: 'service_provider'
    }
    
    console.log('Testing with data:', JSON.stringify(testData, null, 2))
    console.log('')
    
    const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    })
    
    console.log('Response status:', response.status)
    console.log('Response status text:', response.statusText)
    console.log('')
    
    const responseText = await response.text()
    console.log('Response body (full):')
    console.log(responseText)
    console.log('')
    
    if (response.status === 200) {
      console.log('🎉 SUCCESS! The edge function is working!')
      try {
        const data = JSON.parse(responseText)
        console.log('Parsed response:')
        console.log(JSON.stringify(data, null, 2))
        console.log('')
        console.log('✅ User ID:', data.user?.id)
        console.log('✅ Email:', data.user?.email)
        console.log('✅ Team ID:', data.team_id)
        console.log('✅ Is New User:', data.isNewUser)
        console.log('✅ Message:', data.message)
        console.log('')
        console.log('🔗 WORKING INVITATION URL:')
        console.log('==========================')
        const workingUrl = `https://www.stayfu.com/#/invite?token=${newToken}&team_id=80145fbc-b0b5-421f-acff-5c20a1e573e8`
        console.log(workingUrl)
        console.log('')
        console.log('📧 Email for testing:', testEmail)
        console.log('')
        console.log('🚨 THE BACKEND IS WORKING! THE ISSUE MUST BE IN THE FRONTEND!')
        
      } catch (parseError) {
        console.log('Could not parse response as JSON:', parseError.message)
      }
    } else {
      console.log('🚨 Edge function failed!')
      try {
        const errorData = JSON.parse(responseText)
        console.log('Error details:', JSON.stringify(errorData, null, 2))
      } catch (parseError) {
        console.log('Could not parse error response')
      }
    }
    
  } catch (err) {
    console.error('❌ Error:', err.message)
  }
}

checkResponseBody().catch(console.error)
