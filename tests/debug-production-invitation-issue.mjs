import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function debugProductionInvitationIssue() {
  console.log('🔍 DEBUGGING PRODUCTION INVITATION ISSUE')
  console.log('========================================')
  console.log('')
  
  // The token from the screenshot
  const token = '00f323be-9b30-4abb-b174-698c281cb671'
  
  console.log('Debugging token:', token)
  console.log('')
  
  // Step 1: Check if invitation exists and its current status
  console.log('🔍 Step 1: Checking invitation in production database')
  try {
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('token', token)
      .maybeSingle()
    
    if (error) {
      console.log('❌ Error checking invitation:', error.message)
      console.log('Error details:', error)
      return
    }
    
    if (!invitation) {
      console.log('❌ Invitation not found in production database')
      console.log('This means the invitation was not properly created or was deleted')
      return
    }
    
    console.log('✅ Invitation found in production:')
    console.log('   ID:', invitation.id)
    console.log('   Email:', invitation.email)
    console.log('   Status:', invitation.status)
    console.log('   Team ID:', invitation.team_id)
    console.log('   Role:', invitation.role)
    console.log('   Created:', invitation.created_at)
    console.log('   Expires:', invitation.expires_at)
    console.log('   Accepted:', invitation.accepted_at)
    console.log('')
    
    if (invitation.status === 'accepted') {
      console.log('⚠️  INVITATION ALREADY ACCEPTED!')
      console.log('This explains the error. Need to create a new invitation.')
      console.log('')
      
      // Create a new invitation for testing
      await createNewProductionInvitation()
      return
    }
    
    // Step 2: Test the exact data from the form
    console.log('🔍 Step 2: Testing with exact form data from screenshot')
    
    const formData = {
      token: token,
      email: '<EMAIL>', // From the screenshot
      password: 'TestPassword123!', // Test password
      first_name: 'Andrewl', // From screenshot
      last_name: 'Arnotta', // From screenshot
      role: 'service_provider'
    }
    
    console.log('Testing with form data:', JSON.stringify(formData, null, 2))
    console.log('')
    
    try {
      const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })
      
      console.log('Response status:', response.status)
      console.log('Response status text:', response.statusText)
      
      // Get all response headers
      console.log('Response headers:')
      for (const [key, value] of response.headers.entries()) {
        console.log(`  ${key}: ${value}`)
      }
      
      const responseText = await response.text()
      console.log('Response body (raw):', responseText)
      console.log('')
      
      if (response.status !== 200) {
        console.log('🚨 EDGE FUNCTION FAILED!')
        console.log('Status:', response.status)
        console.log('Status Text:', response.statusText)
        
        try {
          const errorData = JSON.parse(responseText)
          console.log('Error details:', JSON.stringify(errorData, null, 2))
          
          if (errorData.error) {
            console.log('')
            console.log('🔍 DETAILED ERROR ANALYSIS:')
            console.log('===========================')
            console.log('Error message:', errorData.error)
            
            // Check for specific error patterns
            if (errorData.error.includes('already been registered')) {
              console.log('💡 ISSUE: User already exists with this email')
              console.log('SOLUTION: Use a different email or handle existing user flow')
            } else if (errorData.error.includes('not found')) {
              console.log('💡 ISSUE: Invitation not found')
              console.log('CAUSE: Token might be invalid or invitation was deleted')
            } else if (errorData.error.includes('permission')) {
              console.log('💡 ISSUE: Permission denied')
              console.log('CAUSE: RLS policy blocking the operation')
            } else if (errorData.error.includes('constraint')) {
              console.log('💡 ISSUE: Database constraint violation')
              console.log('CAUSE: Duplicate data or foreign key issue')
            } else if (errorData.error.includes('expired')) {
              console.log('💡 ISSUE: Invitation expired')
              console.log('CAUSE: Invitation past expiration date')
            } else {
              console.log('💡 ISSUE: Unknown error')
              console.log('CAUSE: Need to check edge function logs')
            }
          }
        } catch (parseError) {
          console.log('Could not parse error response as JSON')
          console.log('Raw error response:', responseText)
        }
        
        return
      }
      
      console.log('✅ Edge function succeeded!')
      const successData = JSON.parse(responseText)
      console.log('Success data:', JSON.stringify(successData, null, 2))
      
    } catch (err) {
      console.log('❌ Request failed with exception:', err.message)
      console.log('Stack trace:', err.stack)
    }
    
  } catch (err) {
    console.error('❌ Unexpected error:', err.message)
  }
}

async function createNewProductionInvitation() {
  console.log('')
  console.log('🔧 CREATING NEW PRODUCTION INVITATION')
  console.log('=====================================')
  
  // Generate a completely new email and token
  const uniqueId = Math.random().toString(36).substring(2, 8)
  const newEmail = `andyarnott+prod${uniqueId}@gmail.com`
  const newToken = crypto.randomUUID()
  
  console.log('Creating invitation for:', newEmail)
  console.log('Token:', newToken)
  
  try {
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .insert({
        token: newToken,
        team_id: '80145fbc-b0b5-421f-acff-5c20a1e573e8',
        email: newEmail,
        role: 'service_provider',
        status: 'pending',
        invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.log('❌ Error creating invitation:', error.message)
      console.log('Error details:', error)
      return
    }
    
    console.log('✅ New production invitation created!')
    console.log('')
    console.log('🔗 NEW PRODUCTION INVITATION URL:')
    console.log('==================================')
    const newUrl = `https://www.stayfu.com/#/invite?token=${newToken}&team_id=80145fbc-b0b5-421f-acff-5c20a1e573e8`
    console.log(newUrl)
    console.log('')
    console.log('📧 Email for testing:', newEmail)
    console.log('🔑 Token:', newToken)
    console.log('')
    console.log('Use this fresh invitation to test!')
    
  } catch (err) {
    console.error('❌ Error creating new invitation:', err.message)
  }
}

async function runDebug() {
  await debugProductionInvitationIssue()
}

runDebug().catch(console.error)
