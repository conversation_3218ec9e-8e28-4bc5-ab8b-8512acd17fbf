import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function debugSpecificUser() {
  console.log('🔍 Debugging User: <EMAIL>')
  console.log('===============================================')
  
  try {
    // Sign in as the service provider
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123' // You may need to update this
    })
    
    if (authError) {
      console.error('❌ Failed to sign in:', authError.message)
      console.log('🔧 Please provide the correct password for this account')
      return
    }
    
    console.log('✅ Signed in successfully')
    const userId = authData.user.id
    console.log('👤 User ID:', userId)
    
    // Check user profile
    console.log('\n📋 User Profile:')
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (profileError) {
      console.error('❌ Profile error:', profileError)
    } else {
      console.log('Profile:', profile)
    }
    
    // Check team memberships
    console.log('\n👥 Team Memberships:')
    const { data: teamMemberships, error: teamError } = await supabase
      .from('team_members')
      .select(`
        team_id,
        role,
        teams!inner(name, owner_id)
      `)
      .eq('user_id', userId)
    
    if (teamError) {
      console.error('❌ Team membership error:', teamError)
    } else {
      console.log('Team memberships:', teamMemberships)
      teamMemberships?.forEach(tm => {
        console.log(`  - Team: "${tm.teams.name}" | Role: ${tm.role} | Team ID: ${tm.team_id}`)
      })
    }
    
    // Check team properties for each team
    console.log('\n🏠 Team Properties:')
    const teamIds = teamMemberships?.map(tm => tm.team_id) || []
    
    if (teamIds.length > 0) {
      const { data: teamProperties, error: propError } = await supabase
        .from('team_properties')
        .select(`
          property_id,
          team_id,
          properties!inner(name, user_id),
          teams!inner(name)
        `)
        .in('team_id', teamIds)
      
      if (propError) {
        console.error('❌ Team properties error:', propError)
      } else {
        console.log(`Found ${teamProperties?.length || 0} properties for user's teams`)
        teamProperties?.forEach(tp => {
          console.log(`  - Property: "${tp.properties.name}" | Team: "${tp.teams.name}" | Property ID: ${tp.property_id}`)
        })
      }
    } else {
      console.log('No team memberships found - should have no access to properties')
    }
    
    // Check what maintenance tasks the user can see via RPC
    console.log('\n🔧 Maintenance Tasks (via RPC):')
    const { data: rpcTasks, error: rpcError } = await supabase.rpc('get_maintenance_tasks_for_user', {
      p_user_id: userId
    })
    
    if (rpcError) {
      console.error('❌ RPC error:', rpcError)
    } else {
      console.log(`RPC returned ${rpcTasks?.length || 0} tasks`)
      rpcTasks?.forEach(task => {
        console.log(`  - Task: "${task.title}"`)
        console.log(`    Property ID: ${task.property_id || 'NULL'}`)
        console.log(`    Team ID: ${task.team_id || 'NULL'}`)
        console.log(`    User ID: ${task.user_id}`)
        console.log(`    Provider ID: ${task.provider_id || 'NULL'}`)
        console.log(`    Assigned To: ${task.assigned_to || 'NULL'}`)
        console.log(`    Created: ${task.created_at}`)
        console.log('')
      })
    }
    
    // Check what maintenance tasks the user can see via direct query
    console.log('\n🔧 Maintenance Tasks (via direct query):')
    const { data: directTasks, error: directError } = await supabase
      .from('maintenance_tasks')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (directError) {
      console.error('❌ Direct query error:', directError)
    } else {
      console.log(`Direct query returned ${directTasks?.length || 0} tasks`)
      directTasks?.forEach(task => {
        console.log(`  - Task: "${task.title}"`)
        console.log(`    Property ID: ${task.property_id || 'NULL'}`)
        console.log(`    Team ID: ${task.team_id || 'NULL'}`)
        console.log(`    User ID: ${task.user_id}`)
        console.log(`    Provider ID: ${task.provider_id || 'NULL'}`)
        console.log(`    Assigned To: ${task.assigned_to || 'NULL'}`)
        console.log('')
      })
    }
    
    // Check if there are any tasks assigned to this user
    console.log('\n🎯 Tasks Specifically Assigned to User:')
    const assignedTasks = directTasks?.filter(task => 
      task.user_id === userId || 
      task.provider_id === userId || 
      task.assigned_to === userId
    ) || []
    
    console.log(`Found ${assignedTasks.length} tasks assigned to this user`)
    assignedTasks.forEach(task => {
      console.log(`  - "${task.title}" assigned via: ${
        task.user_id === userId ? 'user_id' : 
        task.provider_id === userId ? 'provider_id' : 
        task.assigned_to === userId ? 'assigned_to' : 'unknown'
      }`)
    })
    
    await supabase.auth.signOut()
    
  } catch (error) {
    console.error('❌ Debug error:', error)
  }
}

debugSpecificUser().catch(console.error)