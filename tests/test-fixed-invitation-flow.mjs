import { createClient } from '@supabase/supabase-js'

// Production Supabase configuration
const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

async function testFixedInvitationFlow() {
  console.log('🧪 TESTING FIXED INVITATION FLOW')
  console.log('=================================')
  console.log('')
  
  // Create a completely fresh invitation
  const uniqueId = Math.random().toString(36).substring(2, 8)
  const testEmail = `andyarnott+fixed${uniqueId}@gmail.com`
  const newToken = crypto.randomUUID()
  
  console.log('Creating fresh invitation for:', testEmail)
  console.log('Token:', newToken)
  
  const supabase = createClient(supabaseUrl, supabaseAnonKey)
  
  try {
    // Step 1: Create the invitation
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .insert({
        token: newToken,
        team_id: '80145fbc-b0b5-421f-acff-5c20a1e573e8',
        email: testEmail,
        role: 'service_provider',
        status: 'pending',
        invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.log('❌ Error creating invitation:', error.message)
      return
    }
    
    console.log('✅ Fresh invitation created!')
    console.log('')
    
    // Step 2: Test the fixed flow using the registerUserForInvitation function
    console.log('🔍 Testing registerUserForInvitation function (the new flow)')
    
    const testData = {
      token: newToken,
      email: testEmail,
      password: 'TestPassword123!',
      first_name: 'Test',
      last_name: 'User',
      role: 'service_provider'
    }
    
    console.log('Test data:', JSON.stringify(testData, null, 2))
    console.log('')
    
    try {
      const response = await fetch(`${supabaseUrl}/functions/v1/accept-invitation-direct`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
      })
      
      console.log('Response status:', response.status)
      console.log('Response status text:', response.statusText)
      
      const responseText = await response.text()
      console.log('Response body:', responseText)
      
      if (response.status === 200) {
        const data = JSON.parse(responseText)
        console.log('')
        console.log('🎉 SUCCESS! Fixed invitation flow works!')
        console.log('User ID:', data.user?.id)
        console.log('Email:', data.user?.email)
        console.log('Team ID:', data.team_id)
        console.log('Message:', data.message)
        console.log('')
        console.log('✅ The invitation page will now work correctly!')
        console.log('✅ Users can register and accept invitations in one step!')
        
      } else {
        console.log('❌ Edge function failed')
        try {
          const errorData = JSON.parse(responseText)
          console.log('Error details:', errorData)
        } catch (parseError) {
          console.log('Could not parse error response')
        }
      }
      
    } catch (err) {
      console.log('❌ Request failed:', err.message)
    }
    
    console.log('')
    console.log('🔗 UPDATED INVITATION URL TO TEST:')
    console.log('==================================')
    const invitationUrl = `https://www.stayfu.com/#/invite?token=${newToken}&team_id=80145fbc-b0b5-421f-acff-5c20a1e573e8`
    console.log(invitationUrl)
    console.log('')
    console.log('📋 WHAT TO EXPECT:')
    console.log('==================')
    console.log('1. Visit the invitation URL')
    console.log('2. Click "Create Account" (no redirect to separate registration page)')
    console.log('3. Fill in the registration form that appears on the same page')
    console.log('4. Click "Create Account & Accept Invitation"')
    console.log('5. Success! User is created and added to team in one step')
    
  } catch (err) {
    console.error('❌ Error in test:', err.message)
  }
}

testFixedInvitationFlow().catch(console.error)
