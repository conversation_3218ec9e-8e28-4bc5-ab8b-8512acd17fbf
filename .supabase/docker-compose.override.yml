version: "3.8"
services:
  studio:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  kong:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  storage:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  db:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  inbucket:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  auth:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  rest:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  realtime:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  meta:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  functions:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  imgproxy:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  vector:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  analytics:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
  logflare:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
