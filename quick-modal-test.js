const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testPropertyModal() {
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: null,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Create screenshots directory
    const screenshotsDir = './modal-test-screenshots';
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir);
    }

    console.log('Testing property modal fixes...');

    // Login
    await page.goto('http://localhost:8081/#/login', { waitUntil: 'networkidle0' });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', 'Newsig1!!!');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Go to properties page
    await page.goto('http://localhost:8081/#/properties', { waitUntil: 'networkidle0' });
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Click on first property card
    const propertyCards = await page.$$('.cursor-pointer');
    if (propertyCards.length > 0) {
      await propertyCards[0].click();
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Test light mode
      await page.evaluate(() => {
        document.documentElement.classList.remove('dark');
      });
      await new Promise(resolve => setTimeout(resolve, 1000));
      await page.screenshot({ 
        path: path.join(screenshotsDir, 'property-modal-light-fixed.png'),
        fullPage: true 
      });
      console.log('Light mode modal screenshot taken');
      
      // Test dark mode
      await page.evaluate(() => {
        document.documentElement.classList.add('dark');
      });
      await new Promise(resolve => setTimeout(resolve, 1000));
      await page.screenshot({ 
        path: path.join(screenshotsDir, 'property-modal-dark-fixed.png'),
        fullPage: true 
      });
      console.log('Dark mode modal screenshot taken');
      
      // Close modal
      await page.keyboard.press('Escape');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('Property modal testing completed!');
    
  } catch (error) {
    console.error('Error during modal testing:', error);
  } finally {
    await browser.close();
  }
}

testPropertyModal();