

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "public";


ALTER SCHEMA "public" OWNER TO "pg_database_owner";


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE TYPE "public"."permission_type" AS ENUM (
    'manage_properties',
    'submit_damage_reports',
    'manage_inventory',
    'view_inventory',
    'manage_staff',
    'manage_service_providers',
    'view_reports',
    'edit_reports',
    'admin_dashboard_access',
    'impersonate_users',
    'edit_user_data',
    'add_users',
    'delete_users',
    'manage_subscriptions',
    'admin',
    'manage_purchase_orders',
    'view_purchase_orders',
    'manage_damage_reports',
    'view_damage_reports',
    'manage_maintenance',
    'view_maintenance',
    'manage_team',
    'view_team',
    'create_maintenance_task'
);


ALTER TYPE "public"."permission_type" OWNER TO "postgres";


CREATE TYPE "public"."po_status" AS ENUM (
    'pending',
    'ordered',
    'delivered',
    'archived'
);


ALTER TYPE "public"."po_status" OWNER TO "postgres";


CREATE TYPE "public"."user_role" AS ENUM (
    'super_admin',
    'admin',
    'property_manager',
    'staff',
    'service_provider'
);


ALTER TYPE "public"."user_role" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."accept_invitation_and_add_member"("p_token" "text", "p_user_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_team_id UUID;
    v_team_name TEXT;
    v_email TEXT;
    v_status TEXT;
    v_invited_by UUID;
    v_role TEXT;
    v_result JSONB;
    v_user_email TEXT;
    v_user_exists BOOLEAN;
BEGIN
    -- Get the invitation details
    BEGIN
        SELECT team_id, email, status, invited_by, role, 
               (SELECT name FROM teams WHERE id = team_invitations.team_id) as team_name
        INTO v_team_id, v_email, v_status, v_invited_by, v_role, v_team_name
        FROM team_invitations
        WHERE token = p_token;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error getting invitation details: %', SQLERRM;
        RETURN jsonb_build_object('success', false, 'error', 'Failed to get invitation details: ' || SQLERRM);
    END;
    
    -- Check if invitation exists
    IF v_team_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Invitation not found');
    END IF;
    
    -- Check if the user exists in the profiles table
    BEGIN
        SELECT EXISTS(SELECT 1 FROM profiles WHERE id = p_user_id) INTO v_user_exists;
        
        IF NOT v_user_exists THEN
            RAISE NOTICE 'User % does not exist in profiles table, creating profile', p_user_id;
            
            -- Get the user's email from auth.users
            BEGIN
                SELECT email INTO v_user_email FROM auth.users WHERE id = p_user_id;
                
                IF v_user_email IS NULL THEN
                    -- If we can't get the email from auth.users, use the invitation email
                    v_user_email := v_email;
                END IF;
                
                -- Create a profile for the user
                INSERT INTO profiles (id, email, role, is_super_admin, created_at, updated_at)
                VALUES (p_user_id, v_user_email, COALESCE(v_role, 'service_provider'), false, NOW(), NOW());
                
                RAISE NOTICE 'Created profile for user % with email %', p_user_id, v_user_email;
            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Error creating profile for user %: %', p_user_id, SQLERRM;
                -- Continue anyway, as the profile might be created by a trigger
            END;
        ELSE
            -- Get the user's email from profiles
            SELECT email INTO v_user_email FROM profiles WHERE id = p_user_id;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error checking if user exists: %', SQLERRM;
        -- Continue anyway, as the profile might be created by a trigger
    END;
    
    -- Log the emails for debugging
    RAISE NOTICE 'Invitation email: %, User email: %', v_email, v_user_email;
    
    -- If invitation is already accepted, just add the user to the team
    IF v_status = 'accepted' THEN
        -- Add the user to the team using the add_user_to_team function
        DECLARE
            team_result JSONB;
        BEGIN
            team_result := add_user_to_team(p_user_id, v_team_id, v_invited_by);
            
            -- If the user is a service provider, add default permissions
            BEGIN
                IF EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND role = 'service_provider') THEN
                    PERFORM add_service_provider_default_permissions(p_user_id, v_team_id);
                END IF;
            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Error adding service provider permissions: %', SQLERRM;
            END;
            
            RETURN jsonb_build_object(
                'success', true, 
                'team_id', v_team_id, 
                'team_name', v_team_name
            );
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error adding user to team: %', SQLERRM;
            RETURN jsonb_build_object('success', false, 'error', 'Failed to add user to team: ' || SQLERRM);
        END;
    END IF;
    
    -- If invitation is not pending, return error
    IF v_status != 'pending' THEN
        RETURN jsonb_build_object('success', false, 'error', 'Invitation is not pending or accepted');
    END IF;
    
    -- Update the invitation status
    BEGIN
        UPDATE team_invitations
        SET status = 'accepted', updated_at = NOW()
        WHERE token = p_token;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error updating invitation status: %', SQLERRM;
        RETURN jsonb_build_object('success', false, 'error', 'Failed to update invitation status: ' || SQLERRM);
    END;
    
    -- Update the user's profile with the invited email if it's different
    -- This helps with email mismatch issues
    IF v_user_email IS DISTINCT FROM v_email THEN
        RAISE NOTICE 'Updating user profile email from % to %', v_user_email, v_email;
        
        -- Only update if the user doesn't already have an email
        IF v_user_email IS NULL OR v_user_email = '' THEN
            BEGIN
                UPDATE profiles
                SET email = v_email, updated_at = NOW()
                WHERE id = p_user_id;
            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Error updating user profile email: %', SQLERRM;
            END;
        END IF;
    END IF;
    
    -- Add the user to the team using the add_user_to_team function
    DECLARE
        team_result JSONB;
    BEGIN
        team_result := add_user_to_team(p_user_id, v_team_id, v_invited_by);
        
        -- If the user is a service provider, add default permissions
        BEGIN
            IF v_role = 'service_provider' OR EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND role = 'service_provider') THEN
                PERFORM add_service_provider_default_permissions(p_user_id, v_team_id);
            END IF;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error adding service provider permissions: %', SQLERRM;
        END;
        
        RETURN jsonb_build_object(
            'success', true, 
            'team_id', v_team_id, 
            'team_name', v_team_name
        );
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error adding user to team: %', SQLERRM;
        RETURN jsonb_build_object('success', false, 'error', 'Failed to add user to team: ' || SQLERRM);
    END;
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Unexpected error in accept_invitation_and_add_member: %', SQLERRM;
    RETURN jsonb_build_object('success', false, 'error', 'Unexpected error: ' || SQLERRM);
END;
$$;


ALTER FUNCTION "public"."accept_invitation_and_add_member"("p_token" "text", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."accept_invitation_direct"("p_token" "text", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_password" "text", "p_role" "text" DEFAULT 'service_provider'::"text") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_invitation_data RECORD;
  v_user_id uuid;
  v_team_id uuid;
  v_user_role user_role;
  v_result jsonb;
  v_role_text text;
BEGIN
  -- Get the invitation details
  SELECT * INTO v_invitation_data
  FROM team_invitations
  WHERE token = p_token;
  
  IF v_invitation_data IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'Invalid invitation token');
  END IF;
  
  -- Check if the invitation is already accepted
  IF v_invitation_data.status = 'accepted' THEN
    RETURN jsonb_build_object('success', false, 'error', 'Invitation already accepted');
  END IF;
  
  -- Check if the invitation is expired
  IF v_invitation_data.expires_at IS NOT NULL AND v_invitation_data.expires_at < NOW() THEN
    RETURN jsonb_build_object('success', false, 'error', 'Invitation expired');
  END IF;
  
  -- Check if the email matches the invitation
  IF v_invitation_data.email IS NOT NULL AND v_invitation_data.email != p_email THEN
    RETURN jsonb_build_object('success', false, 'error', 'Email does not match invitation');
  END IF;
  
  -- Set the team ID
  v_team_id := v_invitation_data.team_id;
  
  -- Determine the effective role with proper type handling
  BEGIN
    -- Set a default role text
    v_role_text := 'service_provider';
    
    -- If a role is provided in the parameters, use that
    IF p_role IS NOT NULL AND p_role != '' THEN
      v_role_text := p_role;
    -- Otherwise, if the invitation has a role, use that
    ELSIF v_invitation_data.role IS NOT NULL THEN
      v_role_text := v_invitation_data.role::text;
    END IF;
    
    -- Validate that the role is a valid user_role enum value
    IF v_role_text NOT IN ('admin', 'property_manager', 'service_provider', 'staff', 'super_admin') THEN
      v_role_text := 'service_provider';
    END IF;
    
    -- Cast to user_role type
    v_user_role := v_role_text::user_role;
  EXCEPTION WHEN OTHERS THEN
    -- If any error occurs, default to service_provider
    v_user_role := 'service_provider'::user_role;
  END;
  
  -- Check if the user already exists by email
  SELECT id INTO v_user_id
  FROM auth.users
  WHERE email = p_email;
  
  IF v_user_id IS NULL THEN
    -- Create a new user
    BEGIN
      v_user_id := extensions.uuid_generate_v4();
      
      -- Insert into auth.users
      INSERT INTO auth.users (
        id,
        email,
        encrypted_password,
        email_confirmed_at,
        raw_app_meta_data,
        raw_user_meta_data,
        created_at,
        updated_at
      ) VALUES (
        v_user_id,
        p_email,
        crypt(p_password, gen_salt('bf')),
        NOW(),
        jsonb_build_object('provider', 'email', 'providers', ARRAY['email']),
        jsonb_build_object(
          'first_name', p_first_name,
          'last_name', p_last_name,
          'role', v_role_text
        ),
        NOW(),
        NOW()
      );
      
      -- Create profile - FIXED: Using 'id' as the primary key, not 'user_id'
      INSERT INTO public.profiles (
        id,
        email,
        first_name,
        last_name,
        role,
        created_at,
        updated_at
      ) VALUES (
        v_user_id,
        p_email,
        p_first_name,
        p_last_name,
        v_user_role,
        NOW(),
        NOW()
      );
      
    EXCEPTION WHEN OTHERS THEN
      RETURN jsonb_build_object('success', false, 'error', 'Failed to create user: ' || SQLERRM);
    END;
  ELSE
    -- Check if profile exists
    IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = v_user_id) THEN
      -- Create profile for existing user - FIXED: Using 'id' as the primary key, not 'user_id'
      INSERT INTO public.profiles (
        id,
        email,
        first_name,
        last_name,
        role,
        created_at,
        updated_at
      ) VALUES (
        v_user_id,
        p_email,
        p_first_name,
        p_last_name,
        v_user_role,
        NOW(),
        NOW()
      );
    END IF;
  END IF;
  
  -- Get the user's role
  SELECT role INTO v_user_role
  FROM public.profiles
  WHERE id = v_user_id;
  
  -- Add the user to the team
  v_result := public.add_user_to_team(v_user_id, v_team_id, v_invitation_data.invited_by);
  
  IF (v_result->>'success')::boolean = false THEN
    RETURN v_result;
  END IF;
  
  -- If the user is a service provider, add default permissions
  IF v_user_role = 'service_provider' THEN
    PERFORM public.add_service_provider_default_permissions(v_user_id, v_team_id);
  END IF;
  
  -- Update the invitation status
  UPDATE team_invitations
  SET status = 'accepted', accepted_at = NOW()
  WHERE token = p_token;
  
  -- Return success
  RETURN jsonb_build_object(
    'success', true,
    'user_id', v_user_id,
    'team_id', v_team_id
  );
  
EXCEPTION WHEN OTHERS THEN
  RETURN jsonb_build_object('success', false, 'error', 'Unexpected error: ' || SQLERRM);
END;
$$;


ALTER FUNCTION "public"."accept_invitation_direct"("p_token" "text", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_password" "text", "p_role" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."accept_team_invitation"("invitation_token" "text", "accepting_user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  invitation RECORD;
  team_member_id UUID;
BEGIN
  -- Get the invitation
  SELECT * INTO invitation FROM public.team_invitations 
  WHERE token = invitation_token AND status = 'pending' AND expires_at > now();
  
  -- If no invitation found or expired
  IF invitation IS NULL THEN
    RAISE EXCEPTION 'Invalid or expired invitation.';
  END IF;
  
  -- Check if user already in team
  IF EXISTS (
    SELECT 1 FROM public.team_members 
    WHERE team_id = invitation.team_id AND user_id = accepting_user_id
  ) THEN
    -- Update invitation status
    UPDATE public.team_invitations SET status = 'accepted' WHERE token = invitation_token;
    RETURN TRUE;
  END IF;
  
  -- Create team member
  INSERT INTO public.team_members(team_id, user_id, added_by, status)
  VALUES (invitation.team_id, accepting_user_id, invitation.invited_by, 'active')
  RETURNING id INTO team_member_id;
  
  -- Update invitation status
  UPDATE public.team_invitations SET status = 'accepted' WHERE token = invitation_token;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;


ALTER FUNCTION "public"."accept_team_invitation"("invitation_token" "text", "accepting_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."accept_team_invitation_safe"("invitation_token" "text", "accepting_user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
  declare
    v_team_id uuid;
    v_email text;
    v_role text;
    v_invitation record;
  begin
    -- Get and validate invitation with a timeout
    set local statement_timeout = '5s';
    
    select * into v_invitation
    from team_invitations
    where token = invitation_token
    and status = 'pending'
    and expires_at > now()
    limit 1;

    if not found then
      return false;
    end if;

    v_team_id := v_invitation.team_id;
    v_email := v_invitation.email;
    v_role := v_invitation.role;

    update team_invitations
    set 
      status = 'accepted',
      updated_at = now()
    where token = invitation_token;

    insert into team_members (team_id, user_id, added_by, status)
    values (v_team_id, accepting_user_id, v_invitation.invited_by, 'active');

    update profiles
    set 
      email = coalesce(email, v_email),
      role = v_role::user_role,
      updated_at = now()
    where id = accepting_user_id
    and (email is null or email = v_email);

    return true;
  exception when others then
    raise notice 'Error in accept_team_invitation: %', SQLERRM;
    return false;
  end;
  $$;


ALTER FUNCTION "public"."accept_team_invitation_safe"("invitation_token" "text", "accepting_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_property_to_team_direct"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  -- Check if user owns the team
  WITH team_check AS (
    SELECT EXISTS (
      SELECT 1 FROM teams
      WHERE id = p_team_id AND owner_id = p_user_id
    ) AS is_owner
  ),
  -- Check if user owns the property
  property_check AS (
    SELECT EXISTS (
      SELECT 1 FROM properties
      WHERE id = p_property_id AND user_id = p_user_id
    ) AS owns_property
  ),
  -- Check if property is already in team
  exists_check AS (
    SELECT EXISTS (
      SELECT 1 FROM team_properties
      WHERE property_id = p_property_id AND team_id = p_team_id
    ) AS already_exists
  ),
  -- Insert if all checks pass and property is not already in team
  insert_result AS (
    INSERT INTO team_properties (team_id, property_id)
    SELECT p_team_id, p_property_id
    FROM team_check, property_check, exists_check
    WHERE team_check.is_owner 
      AND property_check.owns_property 
      AND NOT exists_check.already_exists
    RETURNING true AS inserted
  ),
  -- Update inventory items
  update_result AS (
    UPDATE inventory_items
    SET team_id = p_team_id
    WHERE property_id = p_property_id
    AND team_id IS NULL
    RETURNING true AS updated
  )
  -- Return true if inserted or already exists
  SELECT COALESCE(
    (SELECT inserted FROM insert_result),
    (SELECT already_exists FROM exists_check),
    false
  );
$$;


ALTER FUNCTION "public"."add_property_to_team_direct"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_property_to_team_safe"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_is_owner boolean;
  v_owns_property boolean;
  v_already_exists boolean;
BEGIN
  -- Check if user owns the team
  SELECT EXISTS (
    SELECT 1 FROM teams
    WHERE id = p_team_id AND owner_id = p_user_id
  ) INTO v_is_owner;
  
  IF NOT v_is_owner THEN
    RAISE EXCEPTION 'User does not own this team';
  END IF;
  
  -- Check if user owns the property
  SELECT EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = p_user_id
  ) INTO v_owns_property;
  
  IF NOT v_owns_property THEN
    RAISE EXCEPTION 'User does not own this property';
  END IF;
  
  -- Check if property is already in team
  SELECT EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id AND team_id = p_team_id
  ) INTO v_already_exists;
  
  IF v_already_exists THEN
    RETURN true; -- Already exists, consider it a success
  END IF;
  
  -- Add property to team
  INSERT INTO team_properties (team_id, property_id)
  VALUES (p_team_id, p_property_id);
  
  -- Update inventory items for this property to include the team_id
  UPDATE inventory_items
  SET team_id = p_team_id
  WHERE property_id = p_property_id
  AND team_id IS NULL;
  
  RETURN true;
EXCEPTION
  WHEN others THEN
    RAISE;
END;
$$;


ALTER FUNCTION "public"."add_property_to_team_safe"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_service_provider_default_permissions"("p_user_id" "uuid", "p_team_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- First, remove any existing permissions that service providers should not have
    DELETE FROM user_permissions
    WHERE user_id = p_user_id AND team_id = p_team_id AND permission IN (
        'manage_properties',
        'manage_staff',
        'manage_service_providers',
        'manage_team',
        'view_team',
        'admin_dashboard_access',
        'impersonate_users',
        'edit_user_data',
        'add_users',
        'delete_users',
        'manage_subscriptions',
        'admin'
    );
    
    -- Add default permissions for service providers
    INSERT INTO user_permissions (user_id, team_id, permission, enabled, created_at, updated_at)
    VALUES
        (p_user_id, p_team_id, 'view_maintenance', TRUE, NOW(), NOW()),
        (p_user_id, p_team_id, 'manage_maintenance', TRUE, NOW(), NOW()),
        (p_user_id, p_team_id, 'submit_damage_reports', TRUE, NOW(), NOW()),
        (p_user_id, p_team_id, 'view_damage_reports', TRUE, NOW(), NOW()),
        (p_user_id, p_team_id, 'manage_damage_reports', TRUE, NOW(), NOW()),
        (p_user_id, p_team_id, 'view_inventory', TRUE, NOW(), NOW()),
        (p_user_id, p_team_id, 'manage_inventory', TRUE, NOW(), NOW()),
        (p_user_id, p_team_id, 'view_purchase_orders', TRUE, NOW(), NOW()),
        (p_user_id, p_team_id, 'manage_purchase_orders', TRUE, NOW(), NOW())
    ON CONFLICT (user_id, team_id, permission) DO UPDATE
    SET enabled = TRUE, updated_at = NOW();
    
    RETURN TRUE;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error adding service provider permissions: %', SQLERRM;
    RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."add_service_provider_default_permissions"("p_user_id" "uuid", "p_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_user_to_team"("p_user_id" "uuid", "p_team_id" "uuid", "p_added_by" "uuid" DEFAULT NULL::"uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_added_by UUID;
    v_team_name TEXT;
BEGIN
    -- Get the team name for logging
    BEGIN
        SELECT name INTO v_team_name FROM teams WHERE id = p_team_id;
        RAISE NOTICE 'Adding user % to team % (%)', p_user_id, v_team_name, p_team_id;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error getting team name: %', SQLERRM;
        v_team_name := 'Unknown';
    END;
    
    -- If added_by is not provided, use the team owner
    IF p_added_by IS NULL THEN
        BEGIN
            SELECT owner_id INTO v_added_by FROM teams WHERE id = p_team_id;
            IF v_added_by IS NULL THEN
                -- If no owner found, use the user themselves
                v_added_by := p_user_id;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error getting team owner: %', SQLERRM;
            v_added_by := p_user_id;
        END;
    ELSE
        v_added_by := p_added_by;
    END IF;
    
    RAISE NOTICE 'User % added by %', p_user_id, v_added_by;
    
    -- Add the user to the team
    BEGIN
        INSERT INTO team_members (team_id, user_id, added_by, status, created_at, updated_at)
        VALUES (p_team_id, p_user_id, v_added_by, 'active', NOW(), NOW())
        ON CONFLICT (team_id, user_id) DO UPDATE
        SET status = 'active', updated_at = NOW();
        
        RAISE NOTICE 'Successfully added user % to team %', p_user_id, p_team_id;
        RETURN jsonb_build_object('success', true, 'team_id', p_team_id, 'team_name', v_team_name);
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error adding user to team: %', SQLERRM;
        RETURN jsonb_build_object('success', false, 'error', 'Failed to add user to team: ' || SQLERRM);
    END;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Unexpected error in add_user_to_team: %', SQLERRM;
    RETURN jsonb_build_object('success', false, 'error', 'Unexpected error: ' || SQLERRM);
END;
$$;


ALTER FUNCTION "public"."add_user_to_team"("p_user_id" "uuid", "p_team_id" "uuid", "p_added_by" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_access_damage_report"("report_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
    -- Check if user is the owner of the damage report
    SELECT EXISTS (
      SELECT 1 FROM damage_reports
      WHERE id = report_id
      AND user_id = auth.uid()
    )
    -- Check if user is admin
    OR EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND (is_super_admin = true OR role = 'admin')
    )
    -- Check if user is on the same team as the damage report owner
    OR EXISTS (
      SELECT 1 FROM damage_reports dr
      JOIN properties p ON dr.property_id = p.id
      JOIN team_properties tp ON p.id = tp.property_id
      JOIN team_members tm ON tp.team_id = tm.team_id
      WHERE dr.id = report_id
      AND tm.user_id = auth.uid()
      AND tm.status = 'active'
    );
  $$;


ALTER FUNCTION "public"."can_access_damage_report"("report_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_create_maintenance_task"("p_property_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_team_id uuid;
BEGIN
  -- Super admins and admins can create tasks for any property
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Property owners can create tasks for their properties
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Service providers can create tasks
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'service_provider'
  ) THEN
    RETURN TRUE;
  END IF;

  -- Get the team_id for this property
  SELECT team_id INTO v_team_id
  FROM team_properties
  WHERE property_id = p_property_id
  LIMIT 1;

  -- If property is not in any team, only the owner can create tasks
  IF v_team_id IS NULL THEN
    RETURN FALSE;
  END IF;

  -- Check if the user has permission to create maintenance tasks for this team
  RETURN EXISTS (
    SELECT 1 FROM user_permissions up
    JOIN team_members tm ON up.team_id = tm.team_id AND up.user_id = tm.user_id
    WHERE tm.team_id = v_team_id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
    AND (
      up.permission = 'create_maintenance_task'::permission_type OR
      up.permission = 'manage_maintenance'::permission_type
    )
    AND up.enabled = true
  );
END;
$$;


ALTER FUNCTION "public"."can_create_maintenance_task"("p_property_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_manage_invitations"("target_team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
    select exists (
      select 1 from teams
      where id = target_team_id
      and owner_id = auth.uid()
    )
    or exists (
      select 1 from user_permissions up
      where up.user_id = auth.uid()
      and (up.permission = 'manage_staff' or up.permission = 'manage_service_providers')
      and up.enabled = true
      and (up.team_id is null or up.team_id = target_team_id)
    );
  $$;


ALTER FUNCTION "public"."can_manage_invitations"("target_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_manage_invitations_safe"("target_team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
    select exists (
      select 1 from teams
      where id = target_team_id
      and owner_id = auth.uid()
    );
  $$;


ALTER FUNCTION "public"."can_manage_invitations_safe"("target_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_manage_permissions_safe"("target_team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
    select exists (
      select 1 from teams
      where id = target_team_id
      and owner_id = auth.uid()
    );
  $$;


ALTER FUNCTION "public"."can_manage_permissions_safe"("target_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_manage_service_providers"("user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id 
    AND (role = 'property_manager' OR role = 'staff' OR role = 'admin' OR is_super_admin = true)
  );
$$;


ALTER FUNCTION "public"."can_manage_service_providers"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_manage_staff"("user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id 
    AND (role = 'property_manager' OR role = 'admin' OR is_super_admin = true)
  );
$$;


ALTER FUNCTION "public"."can_manage_staff"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_manage_team_members"("target_team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
    select exists (
      select 1 from teams
      where id = target_team_id
      and owner_id = auth.uid()
    )
    or exists (
      select 1 from user_permissions up
      where up.user_id = auth.uid()
      and up.permission = 'manage_staff'
      and up.enabled = true
      and (up.team_id is null or up.team_id = target_team_id)
    );
  $$;


ALTER FUNCTION "public"."can_manage_team_members"("target_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_manage_team_permissions"("target_team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
    -- Check if user is team owner
    SELECT EXISTS (
      SELECT 1 FROM teams
      WHERE id = target_team_id
      AND owner_id = auth.uid()
    )
    -- Check if user is admin
    OR EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND (is_super_admin = true OR role = 'admin')
    )
    -- Check if user has manage_staff permission for this team
    OR EXISTS (
      SELECT 1 FROM user_permissions
      WHERE user_id = auth.uid()
      AND team_id = target_team_id
      AND permission = 'manage_staff'::permission_type
      AND enabled = true
    );
  $$;


ALTER FUNCTION "public"."can_manage_team_permissions"("target_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_damage_report_duplicate"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Check if there's already a damage report with the same property_id and title
    IF EXISTS (
        SELECT 1 FROM damage_reports
        WHERE property_id = NEW.property_id
        AND title = NEW.title
        AND id != NEW.id
    ) THEN
        RAISE EXCEPTION 'A damage report with the same title already exists for this property';
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."check_damage_report_duplicate"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_duplicate_property_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Check for duplicate names within the same user account
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE name = NEW.name
    AND user_id = NEW.user_id
    AND id != NEW.id
  ) THEN
    RAISE EXCEPTION 'A property with the name "%" already exists in your account', NEW.name;
  END IF;

  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."check_duplicate_property_name"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_property_in_team"("p_property_id" "uuid", "p_team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  SELECT EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id AND team_id = p_team_id
  );
$$;


ALTER FUNCTION "public"."check_property_in_team"("p_property_id" "uuid", "p_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_team_duplicate_property_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  v_property_name TEXT;
  v_team_name TEXT;
  v_duplicate_count INTEGER;
BEGIN
  -- Get the property name
  SELECT name INTO v_property_name FROM properties WHERE id = NEW.property_id;

  -- Get the team name
  SELECT name INTO v_team_name FROM teams WHERE id = NEW.team_id;

  -- Count properties with the same name in this team
  SELECT COUNT(*) INTO v_duplicate_count
  FROM team_properties tp
  JOIN properties p ON tp.property_id = p.id
  WHERE tp.team_id = NEW.team_id
  AND p.name = v_property_name
  AND tp.property_id != NEW.property_id;

  -- If there's a duplicate, raise a warning (not an exception to avoid breaking existing functionality)
  IF v_duplicate_count > 0 THEN
    RAISE WARNING 'Warning: Adding property "%" to team "%" will create a duplicate name (% existing properties with this name)',
      v_property_name, v_team_name, v_duplicate_count;
  END IF;

  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."check_team_duplicate_property_name"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_maintenance_provider_for_service_provider"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Wrapped in exception handling to prevent registration failures
  BEGIN
    -- Only create a provider record if the user is a service provider
    IF NEW.role = 'service_provider' THEN
      -- Check if a provider record already exists for this user
      IF NOT EXISTS (SELECT 1 FROM maintenance_providers WHERE user_id = NEW.id) THEN
        -- Create a new provider record
        BEGIN
          INSERT INTO maintenance_providers (
            user_id,
            name,
            email,
            specialty,
            phone,
            notes
          ) VALUES (
            NEW.id,
            COALESCE(NEW.first_name || ' ' || NEW.last_name, NEW.email),
            NEW.email,
            'General',
            '',
            ''
          );
          RAISE NOTICE 'Created maintenance provider record for user %', NEW.id;
        EXCEPTION WHEN OTHERS THEN
          -- Log the error but don't block the trigger
          RAISE NOTICE 'Error creating maintenance provider record: %', SQLERRM;
        END;
      END IF;
    END IF;
  EXCEPTION WHEN OTHERS THEN
    -- Log the error but don't block the trigger
    RAISE NOTICE 'Error in create_maintenance_provider_for_service_provider: %', SQLERRM;
  END;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."create_maintenance_provider_for_service_provider"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_maintenance_provider_for_team_member"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Only create a provider record if the user is a service provider
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = NEW.user_id
    AND role = 'service_provider'
  ) THEN
    -- Check if a provider record already exists for this user
    IF NOT EXISTS (SELECT 1 FROM maintenance_providers WHERE user_id = NEW.user_id) THEN
      -- Get the user's profile information
      INSERT INTO maintenance_providers (
        user_id,
        name,
        email,
        specialty,
        phone,
        notes
      )
      SELECT
        p.id,
        COALESCE(p.first_name || ' ' || p.last_name, p.email),
        p.email,
        'Team Service Provider',
        '',
        ''
      FROM profiles p
      WHERE p.id = NEW.user_id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."create_maintenance_provider_for_team_member"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_next_recurring_task"("completed_task_id" "uuid") RETURNS "uuid"
    LANGUAGE "plpgsql"
    AS $$ DECLARE task_record maintenance_tasks%ROWTYPE; new_task_id uuid; new_due_date timestamptz; BEGIN SELECT * INTO task_record FROM maintenance_tasks WHERE id = completed_task_id; IF NOT task_record.is_recurring OR (task_record.max_recurrences IS NOT NULL AND task_record.recurrence_count >= task_record.max_recurrences) THEN RETURN NULL; END IF; new_due_date := COALESCE(task_record.next_due_date, NOW()) + (task_record.recurrence_interval_days || ' days')::interval; INSERT INTO maintenance_tasks ( user_id, property_id, property_name, title, description, status, severity, due_date, assigned_to, provider_id, provider_email, team_id, is_recurring, recurrence_interval_days, parent_task_id, next_due_date, recurrence_count, max_recurrences, email_notification_sent ) VALUES ( task_record.user_id, task_record.property_id, task_record.property_name, task_record.title, task_record.description, 'open', task_record.severity, new_due_date::date, task_record.assigned_to, task_record.provider_id, task_record.provider_email, task_record.team_id, true, task_record.recurrence_interval_days, COALESCE(task_record.parent_task_id, completed_task_id), new_due_date + (task_record.recurrence_interval_days || ' days')::interval, task_record.recurrence_count + 1, task_record.max_recurrences, false ) RETURNING id INTO new_task_id; RETURN new_task_id; END; $$;


ALTER FUNCTION "public"."create_next_recurring_task"("completed_task_id" "uuid") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" "uuid" NOT NULL,
    "first_name" "text",
    "last_name" "text",
    "email" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "avatar_url" "text",
    "is_super_admin" boolean DEFAULT false NOT NULL,
    "role" "public"."user_role" DEFAULT 'property_manager'::"public"."user_role" NOT NULL
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_profile"("p_id" "uuid", "p_email" "text", "p_first_name" "text" DEFAULT 'User'::"text", "p_last_name" "text" DEFAULT ''::"text", "p_role" "text" DEFAULT 'property_manager'::"text", "p_is_super_admin" boolean DEFAULT false) RETURNS SETOF "public"."profiles"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if profile already exists
  IF EXISTS (SELECT 1 FROM profiles WHERE id = p_id) THEN
    -- Return existing profile
    RETURN QUERY 
    SELECT * FROM profiles
    WHERE id = p_id;
  ELSE
    -- Create new profile
    INSERT INTO profiles (id, email, first_name, last_name, role, is_super_admin)
    VALUES (
      p_id,
      p_email,
      p_first_name,
      p_last_name,
      p_role::user_role,
      p_is_super_admin
    );
    
    -- Return the newly created profile
    RETURN QUERY 
    SELECT * FROM profiles
    WHERE id = p_id;
  END IF;
  
  RETURN;
END;
$$;


ALTER FUNCTION "public"."create_profile"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_role" "text", "p_is_super_admin" boolean) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_profile_safely"("p_user_id" "uuid", "p_first_name" "text", "p_last_name" "text", "p_role" "text", "p_email" "text") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$ DECLARE v_role user_role; BEGIN BEGIN v_role := p_role::user_role; EXCEPTION WHEN OTHERS THEN v_role := 'service_provider'::user_role; END; BEGIN INSERT INTO public.profiles (id, email, first_name, last_name, role, is_super_admin, created_at, updated_at) VALUES (p_user_id, p_email, p_first_name, p_last_name, v_role, FALSE, NOW(), NOW()) ON CONFLICT (id) DO UPDATE SET email = EXCLUDED.email, first_name = EXCLUDED.first_name, last_name = EXCLUDED.last_name, role = EXCLUDED.role, updated_at = NOW(); RETURN jsonb_build_object('success', TRUE, 'message', 'Profile created or updated successfully'); EXCEPTION WHEN OTHERS THEN RETURN jsonb_build_object('success', FALSE, 'error', 'Failed to create profile: ' || SQLERRM); END; END; $$;


ALTER FUNCTION "public"."create_profile_safely"("p_user_id" "uuid", "p_first_name" "text", "p_last_name" "text", "p_role" "text", "p_email" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_service_provider_profile"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_status" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    INSERT INTO service_providers (id, email, first_name, last_name, status)
    VALUES (p_id, p_email, p_first_name, p_last_name, p_status);
    RETURN TRUE;
EXCEPTION WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."create_service_provider_profile"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_status" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_team_invitation"("team_id" "uuid", "email" "text", "role" "public"."user_role", "expires_in" interval DEFAULT '7 days'::interval) RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  invitation_id uuid;
  token_val text;
BEGIN
  -- Generate a random token
  token_val := encode(gen_random_bytes(32), 'hex');
  
  -- Insert invitation record
  INSERT INTO public.invitations(team_id, email, invited_by, role, token, expires_at)
  VALUES (team_id, email, auth.uid(), role, token_val, now() + expires_in)
  RETURNING id INTO invitation_id;
  
  RETURN invitation_id;
END;
$$;


ALTER FUNCTION "public"."create_team_invitation"("team_id" "uuid", "email" "text", "role" "public"."user_role", "expires_in" interval) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_team_invitation"("team_id" "uuid", "email" "text", "role" "public"."user_role", "invitation_token" "text", "expires_in" interval DEFAULT '7 days'::interval) RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  invitation_id UUID;
BEGIN
  -- Insert invitation record
  INSERT INTO public.team_invitations(team_id, email, invited_by, role, token, expires_at)
  VALUES (team_id, email, auth.uid(), role, invitation_token, now() + expires_in)
  RETURNING id INTO invitation_id;
  
  RETURN invitation_id;
END;
$$;


ALTER FUNCTION "public"."create_team_invitation"("team_id" "uuid", "email" "text", "role" "public"."user_role", "invitation_token" "text", "expires_in" interval) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_team_invitation"("p_team_id" "uuid", "p_email" "text", "p_role" "text", "p_invited_by" "uuid", "p_token" "text" DEFAULT NULL::"text", "p_expires_in" interval DEFAULT '7 days'::interval) RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
  declare
    v_token text;
    v_team_access boolean;
  begin
    -- Verify team access
    select can_manage_invitations(p_team_id) into v_team_access;
    if not v_team_access then
      raise exception 'Insufficient permissions to create invitations for this team';
    end if;

    -- Generate token if not provided
    v_token := coalesce(p_token, encode(gen_random_bytes(24), 'base64'));

    -- Create invitation
    insert into team_invitations (
      team_id,
      email,
      role,
      invited_by,
      token,
      status,
      expires_at
    ) values (
      p_team_id,
      p_email,
      p_role,
      p_invited_by,
      v_token,
      'pending',
      now() + p_expires_in
    );

    return v_token;
  end;
  $$;


ALTER FUNCTION "public"."create_team_invitation"("p_team_id" "uuid", "p_email" "text", "p_role" "text", "p_invited_by" "uuid", "p_token" "text", "p_expires_in" interval) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_team_invitation"("p_team_id" "uuid", "p_email" "text", "p_role" "public"."user_role", "p_invited_by" "uuid", "p_token" "text" DEFAULT NULL::"text", "p_expires_in" interval DEFAULT '7 days'::interval) RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
  DECLARE
    v_token text;
    v_team_access boolean;
  BEGIN
    -- Verify team access
    SELECT can_manage_invitations(p_team_id) INTO v_team_access;
    if not v_team_access then
      RAISE EXCEPTION 'Insufficient permissions to create invitations for this team';
    END IF;

    -- Generate token if not provided
    v_token := coalesce(p_token, encode(gen_random_bytes(24), 'base64'));

    -- Create invitation
    INSERT INTO team_invitations (
      team_id,
      email,
      role,
      invited_by,
      token,
      status,
      expires_at
    ) VALUES (
      p_team_id,
      p_email,
      p_role,  -- This should be of type user_role
      p_invited_by,
      v_token,
      'pending',
      now() + p_expires_in
    );

    RETURN v_token;
  END;
$$;


ALTER FUNCTION "public"."create_team_invitation"("p_team_id" "uuid", "p_email" "text", "p_role" "public"."user_role", "p_invited_by" "uuid", "p_token" "text", "p_expires_in" interval) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if the user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User with ID % does not exist', p_user_id;
  END IF;

  -- Insert or update the user preferences
  INSERT INTO public.user_preferences (user_id, onboarding_state)
  VALUES (p_user_id, p_onboarding_state)
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    onboarding_state = p_onboarding_state,
    updated_at = NOW();

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error creating user preferences: %', SQLERRM;
    RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."create_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."create_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") IS 'Creates or updates user preferences with SECURITY DEFINER to bypass RLS.';



CREATE OR REPLACE FUNCTION "public"."debug_team_access"("p_team_id" "uuid", "p_user_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_is_super_admin boolean;
  v_is_admin boolean;
  v_is_team_owner boolean;
  v_is_team_member boolean;
  v_user_email text;
  v_team_name text;
  v_team_owner_id uuid;
BEGIN
  -- Get user info
  SELECT 
    is_super_admin, 
    role = 'admin',
    email
  INTO 
    v_is_super_admin,
    v_is_admin,
    v_user_email
  FROM profiles
  WHERE id = p_user_id;
  
  -- Get team info
  SELECT 
    name,
    owner_id
  INTO 
    v_team_name,
    v_team_owner_id
  FROM teams
  WHERE id = p_team_id;
  
  -- Check if user is team owner
  v_is_team_owner := (v_team_owner_id = p_user_id);
  
  -- Check if user is team member
  SELECT EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = p_team_id
    AND user_id = p_user_id
    AND status = 'active'
  ) INTO v_is_team_member;
  
  -- Return debug info
  RETURN jsonb_build_object(
    'user_id', p_user_id,
    'user_email', v_user_email,
    'team_id', p_team_id,
    'team_name', v_team_name,
    'team_owner_id', v_team_owner_id,
    'is_super_admin', v_is_super_admin,
    'is_admin', v_is_admin,
    'is_team_owner', v_is_team_owner,
    'is_team_member', v_is_team_member,
    'should_have_access', (v_is_super_admin OR v_is_admin OR v_is_team_owner OR v_is_team_member)
  );
END;
$$;


ALTER FUNCTION "public"."debug_team_access"("p_team_id" "uuid", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."delete_property_cascade"("property_id_param" "uuid") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$ BEGIN DELETE FROM maintenance_tasks WHERE property_id = property_id_param; DELETE FROM inventory_items WHERE property_id = property_id_param; DELETE FROM team_properties WHERE property_id = property_id_param; DELETE FROM damage_photos WHERE damage_report_id IN (SELECT id FROM damage_reports WHERE property_id = property_id_param); DELETE FROM damage_notes WHERE damage_report_id IN (SELECT id FROM damage_reports WHERE property_id = property_id_param); DELETE FROM damage_reports WHERE property_id = property_id_param; DELETE FROM property_files WHERE property_id = property_id_param; DELETE FROM properties WHERE id = property_id_param; END; $$;


ALTER FUNCTION "public"."delete_property_cascade"("property_id_param" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."delete_team_cascade"("team_id_param" "uuid") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Delete team_members associated with the team
    DELETE FROM team_members WHERE team_id = team_id_param;
    
    -- Delete team_properties associations
    DELETE FROM team_properties WHERE team_id = team_id_param;
    
    -- Update inventory items to remove team_id
    UPDATE inventory_items SET team_id = NULL WHERE team_id = team_id_param;
    
    -- Finally, delete the team itself
    DELETE FROM teams WHERE id = team_id_param;
END;
$$;


ALTER FUNCTION "public"."delete_team_cascade"("team_id_param" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."ensure_all_users_have_profiles"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  user_record RECORD;
BEGIN
  -- Find all users without profiles
  FOR user_record IN 
    SELECT au.id, au.email, au.raw_user_meta_data 
    FROM auth.users au
    LEFT JOIN public.profiles p ON p.id = au.id
    WHERE p.id IS NULL
  LOOP
    -- Create a profile for each user
    INSERT INTO public.profiles (
      id, 
      email, 
      first_name, 
      last_name, 
      role, 
      is_super_admin,
      created_at,
      updated_at
    )
    VALUES (
      user_record.id,
      user_record.email,
      COALESCE(user_record.raw_user_meta_data->>'first_name', 'User'),
      COALESCE(user_record.raw_user_meta_data->>'last_name', ''),
      COALESCE((user_record.raw_user_meta_data->>'role')::user_role, 'property_manager'::user_role),
      COALESCE((user_record.raw_user_meta_data->>'is_super_admin')::boolean, FALSE),
      NOW(),
      NOW()
    );
    
    RAISE LOG 'Created missing profile for user %', user_record.id;
  END LOOP;
END;
$$;


ALTER FUNCTION "public"."ensure_all_users_have_profiles"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."ensure_profile_exists"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_first_name TEXT;
  v_last_name TEXT;
  v_role TEXT;
  v_email TEXT;
  v_profile_exists BOOLEAN;
BEGIN
  -- Check if the profile already exists
  SELECT EXISTS(SELECT 1 FROM public.profiles WHERE id = NEW.id) INTO v_profile_exists;
  
  IF v_profile_exists THEN
    RAISE LOG 'Profile already exists for user %', NEW.id;
    RETURN NEW;
  END IF;
  
  -- Extract user metadata
  v_first_name := COALESCE(NEW.raw_user_meta_data->>'first_name', '');
  v_last_name := COALESCE(NEW.raw_user_meta_data->>'last_name', '');
  v_role := COALESCE(NEW.raw_user_meta_data->>'role', 'property_manager');
  v_email := COALESCE(NEW.email, '');
  
  -- Log the values for debugging
  RAISE LOG 'Creating profile for user % with first_name=%, last_name=%, role=%, email=%', 
    NEW.id, v_first_name, v_last_name, v_role, v_email;
  
  -- Create the profile with a BEGIN/EXCEPTION block to catch errors
  BEGIN
    INSERT INTO public.profiles (id, first_name, last_name, role, email, created_at, updated_at)
    VALUES (
      NEW.id,
      v_first_name,
      v_last_name,
      v_role::user_role,
      v_email,
      NOW(),
      NOW()
    );
    
    RAISE LOG 'Profile created successfully for user %', NEW.id;
  EXCEPTION
    WHEN OTHERS THEN
      RAISE LOG 'Error creating profile for user %: %', NEW.id, SQLERRM;
      -- Don't raise an exception, just log the error and continue
      -- This prevents the trigger from failing and allows the user to be created
  END;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."ensure_profile_exists"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."ensure_profile_exists"("p_id" "uuid", "p_email" "text", "p_first_name" "text" DEFAULT ''::"text", "p_last_name" "text" DEFAULT ''::"text", "p_role" "text" DEFAULT 'property_manager'::"text") RETURNS TABLE("id" "uuid", "email" "text", "first_name" "text", "last_name" "text", "avatar_url" "text", "is_super_admin" boolean, "role" "public"."user_role", "created_at" timestamp with time zone, "updated_at" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_is_admin BOOLEAN;
BEGIN
  -- Check if profile exists
  IF EXISTS (SELECT 1 FROM profiles WHERE id = p_id) THEN
    -- Return existing profile
    RETURN QUERY 
    SELECT p.id, p.email, p.first_name, p.last_name, p.avatar_url, p.is_super_admin, p.role, p.created_at, p.updated_at
    FROM profiles p
    WHERE p.id = p_id;
    RETURN;
  END IF;
  
  -- Check if user is admin
  SELECT EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = p_id AND raw_app_meta_data->>'is_super_admin' = 'true'
  ) INTO v_is_admin;
  
  -- Create profile
  INSERT INTO profiles (id, email, first_name, last_name, is_super_admin, role, created_at, updated_at)
  VALUES (
    p_id,
    p_email,
    p_first_name,
    p_last_name,
    v_is_admin,
    p_role::user_role,
    NOW(),
    NOW()
  );
  
  -- Return the newly created profile
  RETURN QUERY 
  SELECT p.id, p.email, p.first_name, p.last_name, p.avatar_url, p.is_super_admin, p.role, p.created_at, p.updated_at
  FROM profiles p
  WHERE p.id = p_id;
  
  RETURN;
END;
$$;


ALTER FUNCTION "public"."ensure_profile_exists"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_role" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."ensure_team_property_access"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- When a new team_property is created, grant access to all team members
  IF TG_OP = 'INSERT' THEN
    -- No action needed, RLS policies will handle access
    RETURN NEW;
  END IF;
  
  -- When a team_property is deleted, revoke access from all team members
  IF TG_OP = 'DELETE' THEN
    -- No action needed, RLS policies will handle access
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."ensure_team_property_access"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."execute_sql"("query" "text") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    result JSONB;
BEGIN
    EXECUTE 'SELECT json_agg(t) FROM (' || query || ') t' INTO result;
    RETURN result;
END;
$$;


ALTER FUNCTION "public"."execute_sql"("query" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."execute_sql_query"("sql_query" "text") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  EXECUTE sql_query;
END;
$$;


ALTER FUNCTION "public"."execute_sql_query"("sql_query" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."execute_sql_query"("sql_query" "text") IS 'Executes a SQL query with admin privileges. Only to be used by super admins through secure edge functions.';



CREATE OR REPLACE FUNCTION "public"."fix_team_invitation"("p_email" "text", "p_team_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_user_id uuid;
  v_invitation_id uuid;
  v_invitation_token text;
  v_invited_by uuid;
  v_team_name text;
BEGIN
  -- Get the user ID from the email
  SELECT id INTO v_user_id
  FROM profiles
  WHERE email = p_email;
  
  IF v_user_id IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'User not found with email: ' || p_email);
  END IF;
  
  -- Get the team name
  SELECT name, owner_id INTO v_team_name, v_invited_by
  FROM teams
  WHERE id = p_team_id;
  
  IF v_team_name IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'Team not found with ID: ' || p_team_id);
  END IF;
  
  -- Check if there's a pending invitation
  SELECT id, token INTO v_invitation_id, v_invitation_token
  FROM team_invitations
  WHERE email = p_email AND team_id = p_team_id AND status = 'pending';
  
  -- If there's no pending invitation, create one
  IF v_invitation_id IS NULL THEN
    v_invitation_token := encode(gen_random_bytes(16), 'hex');
    
    INSERT INTO team_invitations (
      team_id,
      email,
      invited_by,
      role,
      token,
      status,
      created_at,
      updated_at,
      expires_at
    ) VALUES (
      p_team_id,
      p_email,
      v_invited_by,
      'property_manager',
      v_invitation_token,
      'pending',
      NOW(),
      NOW(),
      NOW() + INTERVAL '7 days'
    )
    RETURNING id, token INTO v_invitation_id, v_invitation_token;
  END IF;
  
  -- Add the user to the team
  INSERT INTO team_members (team_id, user_id, added_by, status, created_at, updated_at)
  VALUES (p_team_id, v_user_id, v_invited_by, 'active', NOW(), NOW())
  ON CONFLICT (team_id, user_id) 
  DO UPDATE SET status = 'active', updated_at = NOW();
  
  -- Update the invitation status to accepted
  UPDATE team_invitations
  SET status = 'accepted', updated_at = NOW()
  WHERE id = v_invitation_id;
  
  RETURN jsonb_build_object(
    'success', true, 
    'message', 'User ' || p_email || ' has been added to team ' || v_team_name,
    'user_id', v_user_id,
    'team_id', p_team_id,
    'invitation_id', v_invitation_id
  );
END;
$$;


ALTER FUNCTION "public"."fix_team_invitation"("p_email" "text", "p_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_all_damage_reports"("p_user_id" "uuid") RETURNS TABLE("id" "uuid", "title" "text", "description" "text", "status" "text", "property_id" "uuid", "property_name" "text", "user_id" "uuid", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "provider_id" "uuid", "provider_name" "text", "platform" "text")
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
    -- Using SQL language instead of plpgsql for simplicity
    SELECT DISTINCT ON (dr.id)
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        p.name AS property_name,
        dr.user_id,
        dr.created_at,
        dr.updated_at,
        dr.provider_id,
        mp.name AS provider_name,
        dr.platform
    FROM
        damage_reports dr
    JOIN
        properties p ON dr.property_id = p.id
    LEFT JOIN
        maintenance_providers mp ON dr.provider_id = mp.id
    WHERE
        -- User's own reports
        dr.user_id = p_user_id
        -- Admin access
        OR EXISTS (SELECT 1 FROM profiles prf WHERE prf.id = p_user_id AND (prf.is_super_admin = true OR prf.role = 'admin'))
        -- Team member access
        OR EXISTS (
            SELECT 1 FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tp.property_id = dr.property_id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
        -- Team direct access
        OR EXISTS (
            SELECT 1 FROM team_members tm
            WHERE tm.team_id = dr.team_id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
        -- Service provider access
        OR (dr.provider_id = p_user_id)
    ORDER BY
        dr.id, dr.created_at DESC;
$$;


ALTER FUNCTION "public"."get_all_damage_reports"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_all_tables"() RETURNS TABLE("tablename" "text")
    LANGUAGE "sql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT tablename::text FROM pg_tables WHERE schemaname = 'public';
$$;


ALTER FUNCTION "public"."get_all_tables"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_damage_notes"("p_damage_report_id" "uuid", "p_include_private" boolean DEFAULT false) RETURNS TABLE("id" "uuid", "damage_report_id" "uuid", "content" "text", "user_id" "uuid", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "private" boolean, "created_by" "text")
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
    SELECT
        dn.id,
        dn.damage_report_id,
        dn.content,
        dn.user_id,
        dn.created_at,
        dn.updated_at,
        dn.private,
        dn.created_by
    FROM
        damage_notes dn
    WHERE
        dn.damage_report_id = p_damage_report_id
        AND (p_include_private = TRUE OR dn.private = FALSE OR dn.user_id = auth.uid())
    ORDER BY
        dn.created_at DESC;
$$;


ALTER FUNCTION "public"."get_damage_notes"("p_damage_report_id" "uuid", "p_include_private" boolean) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_damage_photo_url"("p_photo_path" "text") RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_url TEXT;
  v_bucket TEXT := 'damage-photos';
BEGIN
  -- Generate a public URL for the photo
  v_url := 'https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/' || v_bucket || '/' || p_photo_path;
  
  RETURN v_url;
END;
$$;


ALTER FUNCTION "public"."get_damage_photo_url"("p_photo_path" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_damage_photos"("p_damage_report_id" "uuid") RETURNS TABLE("id" "uuid", "damage_report_id" "uuid", "file_name" "text", "file_path" "text", "caption" "text", "user_id" "uuid", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "media_type" "text", "public_url" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT
    dp.id,
    dp.damage_report_id,
    dp.file_name,
    dp.file_path,
    dp.caption,
    dp.user_id,
    dp.created_at,
    dp.updated_at,
    dp.media_type,
    'https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/damage-photos/' || dp.file_path AS public_url
  FROM damage_photos dp
  WHERE dp.damage_report_id = p_damage_report_id
  ORDER BY dp.created_at DESC;

  RETURN;
END;
$$;


ALTER FUNCTION "public"."get_damage_photos"("p_damage_report_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_database_size"() RETURNS TABLE("size_bytes" "text", "size_pretty" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT
    pg_database_size(current_database())::text,
    pg_size_pretty(pg_database_size(current_database()))
  FROM pg_database
  WHERE datname = current_database();
END;
$$;


ALTER FUNCTION "public"."get_database_size"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_functions"() RETURNS TABLE("function_name" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT p.proname::text
  FROM pg_proc p
  JOIN pg_namespace n ON p.pronamespace = n.oid
  WHERE n.nspname = 'public'
  AND p.prokind = 'f'
  ORDER BY p.proname;
END;
$$;


ALTER FUNCTION "public"."get_functions"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_low_stock_items"("user_id_param" "uuid", "property_id_param" "uuid" DEFAULT NULL::"uuid") RETURNS TABLE("id" "uuid", "name" "text", "quantity" integer, "min_quantity" integer, "property_id" "uuid", "collection" "text", "price" numeric, "amazon_url" "text", "walmart_url" "text", "property_name" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.id,
    i.name,
    i.quantity,
    i.min_quantity,
    i.property_id,
    i.collection,
    i.price,
    i.amazon_url,
    i.walmart_url,
    p.name as property_name
  FROM inventory_items i
  LEFT JOIN properties p ON i.property_id = p.id
  WHERE i.user_id = user_id_param
    AND i.quantity < i.min_quantity
    AND i.min_quantity > 0
    AND (property_id_param IS NULL OR i.property_id = property_id_param);
END;
$$;


ALTER FUNCTION "public"."get_low_stock_items"("user_id_param" "uuid", "property_id_param" "uuid") OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."maintenance_tasks" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "property_id" "uuid",
    "property_name" "text" NOT NULL,
    "title" "text" NOT NULL,
    "description" "text",
    "status" "text" DEFAULT 'new'::"text" NOT NULL,
    "severity" "text" DEFAULT 'medium'::"text" NOT NULL,
    "due_date" "text",
    "assigned_to" "text",
    "provider_id" "uuid",
    "provider_email" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "team_id" "uuid",
    "is_recurring" boolean DEFAULT false,
    "recurrence_interval_days" integer,
    "parent_task_id" "uuid",
    "next_due_date" timestamp with time zone,
    "recurrence_count" integer DEFAULT 0,
    "max_recurrences" integer,
    "completed_at" timestamp with time zone,
    "email_notification_sent" boolean DEFAULT false,
    "email_notification_sent_at" timestamp with time zone
);


ALTER TABLE "public"."maintenance_tasks" OWNER TO "postgres";


COMMENT ON COLUMN "public"."maintenance_tasks"."team_id" IS 'The team that this task belongs to, based on the property. This is used for team-based access control.';



CREATE OR REPLACE FUNCTION "public"."get_maintenance_tasks_for_team_member"() RETURNS SETOF "public"."maintenance_tasks"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_user_id UUID := auth.uid();
  v_is_super_admin BOOLEAN;
  v_is_admin BOOLEAN;
  v_is_property_manager BOOLEAN;
BEGIN
  -- Check if user is super admin
  SELECT is_super_admin INTO v_is_super_admin FROM profiles WHERE id = v_user_id;
  IF v_is_super_admin THEN
    -- Super admins can see all tasks
    RETURN QUERY SELECT * FROM maintenance_tasks;
    RETURN;
  END IF;
  
  -- Check if user is admin
  SELECT role = 'admin' INTO v_is_admin FROM profiles WHERE id = v_user_id;
  IF v_is_admin THEN
    -- Admins can see all tasks
    RETURN QUERY SELECT * FROM maintenance_tasks;
    RETURN;
  END IF;
  
  -- Check if user is property manager
  SELECT role = 'property_manager' INTO v_is_property_manager FROM profiles WHERE id = v_user_id;
  IF v_is_property_manager THEN
    -- Property managers can see tasks for their properties
    RETURN QUERY 
    SELECT mt.* 
    FROM maintenance_tasks mt
    JOIN properties p ON mt.property_id = p.id
    WHERE p.user_id = v_user_id
    UNION
    -- And tasks they created
    SELECT * FROM maintenance_tasks WHERE user_id = v_user_id;
    RETURN;
  END IF;
  
  -- For team members, get tasks for properties in their teams
  RETURN QUERY
  SELECT mt.*
  FROM maintenance_tasks mt
  JOIN team_properties tp ON mt.property_id = tp.property_id
  JOIN team_members tm ON tp.team_id = tm.team_id
  WHERE tm.user_id = v_user_id
  UNION
  -- And tasks they created
  SELECT * FROM maintenance_tasks WHERE user_id = v_user_id
  UNION
  -- And tasks assigned to them
  SELECT * FROM maintenance_tasks WHERE assigned_to::text = v_user_id::text
  UNION
  -- And tasks where they are the provider
  SELECT * FROM maintenance_tasks WHERE provider_id = v_user_id;
  
  RETURN;
END;
$$;


ALTER FUNCTION "public"."get_maintenance_tasks_for_team_member"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_maintenance_tasks_for_user"("p_user_id" "uuid") RETURNS SETOF "public"."maintenance_tasks"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
    -- Get all tasks for the user
    SELECT * FROM maintenance_tasks
    WHERE
        -- User owns the task
        user_id::text = p_user_id::text
        -- User is assigned to the task
        OR assigned_to::text = p_user_id::text
        -- User is the service provider for the task
        OR provider_id::text = p_user_id::text
        -- User owns the property
        OR property_id IN (
            SELECT id FROM properties WHERE user_id::text = p_user_id::text
        )
        -- User is a team member with access to the property
        OR property_id IN (
            SELECT tp.property_id
            FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tm.user_id::text = p_user_id::text AND tm.status = 'active'
        )
        -- User is a team member with access to the team
        OR team_id IN (
            SELECT team_id
            FROM team_members
            WHERE user_id::text = p_user_id::text AND status = 'active'
        )
        -- User is a team owner
        OR team_id IN (
            SELECT id
            FROM teams
            WHERE owner_id::text = p_user_id::text
        )
    ORDER BY created_at DESC;
$$;


ALTER FUNCTION "public"."get_maintenance_tasks_for_user"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_profile"("p_user_id" "uuid") RETURNS SETOF "public"."profiles"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Return the profile for the specified user ID
  RETURN QUERY 
  SELECT * FROM profiles
  WHERE id = p_user_id;
  
  RETURN;
END;
$$;


ALTER FUNCTION "public"."get_profile"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_profile_by_id"("user_id" "uuid") RETURNS SETOF "public"."profiles"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  SELECT * FROM profiles WHERE id = user_id;
$$;


ALTER FUNCTION "public"."get_profile_by_id"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_profile_by_id_or_email"("p_id" "uuid" DEFAULT NULL::"uuid", "p_email" "text" DEFAULT NULL::"text") RETURNS SETOF "public"."profiles"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- If ID is provided, try to get profile by ID
  IF p_id IS NOT NULL THEN
    RETURN QUERY 
    SELECT * FROM profiles
    WHERE id = p_id;
    
    -- If we found a profile, return it
    IF FOUND THEN
      RETURN;
    END IF;
  END IF;
  
  -- If email is provided, try to get profile by email
  IF p_email IS NOT NULL THEN
    RETURN QUERY 
    SELECT * FROM profiles
    WHERE email = p_email;
    
    -- If we found a profile, return it
    IF FOUND THEN
      RETURN;
    END IF;
  END IF;
  
  -- If we get here, no profile was found
  RETURN;
END;
$$;


ALTER FUNCTION "public"."get_profile_by_id_or_email"("p_id" "uuid", "p_email" "text") OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."properties" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "address" "text" NOT NULL,
    "city" "text" NOT NULL,
    "state" "text" NOT NULL,
    "zip" "text" NOT NULL,
    "image_url" "text",
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "bedrooms" integer DEFAULT 1,
    "bathrooms" integer DEFAULT 1,
    "budget" numeric,
    "ical_url" "text",
    "next_booking" "text",
    "collections" "jsonb",
    "last_ical_sync" "text",
    "is_occupied" boolean DEFAULT false,
    "current_checkout" "text",
    "next_checkin_date" "text",
    "next_checkin_formatted" "text",
    "team_id" "uuid",
    "timezone" "text" DEFAULT 'America/Los_Angeles'::"text",
    "check_in_time" time without time zone DEFAULT '15:00:00'::time without time zone,
    "check_out_time" time without time zone DEFAULT '11:00:00'::time without time zone
);


ALTER TABLE "public"."properties" OWNER TO "postgres";


COMMENT ON TABLE "public"."properties" IS 'Properties table with RLS policies. Super admins and admins have access to all properties through the has_property_access function.';



CREATE OR REPLACE FUNCTION "public"."get_properties_for_team_member"("p_user_id" "uuid", "p_team_id" "uuid") RETURNS SETOF "public"."properties"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  -- Get properties owned by the user
  SELECT p.* FROM properties p
  WHERE p.user_id = p_user_id
  
  UNION
  
  -- Get properties in the team
  SELECT p.* FROM properties p
  JOIN team_properties tp ON p.id = tp.property_id
  JOIN team_members tm ON tp.team_id = tm.team_id
  WHERE tm.user_id = p_user_id
  AND tm.team_id = p_team_id
  AND tm.status = 'active';
$$;


ALTER FUNCTION "public"."get_properties_for_team_member"("p_user_id" "uuid", "p_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_property_damage_reports"("p_property_id" "uuid") RETURNS TABLE("id" "uuid", "title" "text", "description" "text", "status" "text", "property_id" "uuid", "property_name" "text", "user_id" "uuid", "user_email" character varying, "provider_id" "uuid", "provider_name" "text", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "platform" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        p.name AS property_name,
        dr.user_id,
        u.email AS user_email,
        dr.provider_id,
        mp.name AS provider_name,
        dr.created_at,
        dr.updated_at,
        dr.platform
    FROM
        damage_reports dr
    JOIN
        properties p ON dr.property_id = p.id
    LEFT JOIN
        auth.users u ON dr.user_id = u.id
    LEFT JOIN
        maintenance_providers mp ON dr.provider_id = mp.id
    WHERE
        dr.property_id = p_property_id
    ORDER BY
        dr.created_at DESC;
END;
$$;


ALTER FUNCTION "public"."get_property_damage_reports"("p_property_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_property_details"("p_property_id" "uuid") RETURNS SETOF "public"."properties"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if the user has access to the property
  IF NOT has_property_access(p_property_id) THEN
    RAISE EXCEPTION 'You do not have access to this property';
  END IF;

  -- Return the property details
  RETURN QUERY
  SELECT * FROM properties
  WHERE id = p_property_id;
END;
$$;


ALTER FUNCTION "public"."get_property_details"("p_property_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_property_details_simple"("p_property_id" "uuid") RETURNS SETOF "public"."properties"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM properties
  WHERE id = p_property_id;
END;
$$;


ALTER FUNCTION "public"."get_property_details_simple"("p_property_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_property_if_team_member"("p_property_id" "uuid", "p_user_id" "uuid") RETURNS SETOF "public"."properties"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if the user is a super admin or admin
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin')
  ) THEN
    -- Return the property directly for admins
    RETURN QUERY
    SELECT * FROM properties
    WHERE id = p_property_id;
    RETURN;
  END IF;

  -- Check if the user owns the property
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = p_user_id
  ) THEN
    -- Return the property if the user owns it
    RETURN QUERY
    SELECT * FROM properties
    WHERE id = p_property_id AND user_id = p_user_id;
    RETURN;
  END IF;

  -- Check if the user is a member of a team that has access to the property
  IF EXISTS (
    SELECT 1 FROM team_members tm
    JOIN team_properties tp ON tm.team_id = tp.team_id
    WHERE tm.user_id = p_user_id
    AND tp.property_id = p_property_id
    AND tm.status = 'active'
  ) THEN
    -- Return the property if the user is in a team with access
    RETURN QUERY
    SELECT p.* FROM properties p
    JOIN team_properties tp ON p.id = tp.property_id
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE p.id = p_property_id
    AND tm.user_id = p_user_id
    AND tm.status = 'active';
    RETURN;
  END IF;

  -- If none of the above conditions are met, return an empty result
  RETURN;
END;
$$;


ALTER FUNCTION "public"."get_property_if_team_member"("p_property_id" "uuid", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_property_with_team_access"("p_property_id" "uuid", "p_user_id" "uuid") RETURNS SETOF "public"."properties"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if the user has access to the property
  IF NOT public.is_team_member_with_property_access(p_user_id, p_property_id) THEN
    RAISE EXCEPTION 'Access denied: User does not have permission to view this property';
  END IF;
  
  -- Return the property details
  RETURN QUERY
  SELECT * FROM properties
  WHERE id = p_property_id;
END;
$$;


ALTER FUNCTION "public"."get_property_with_team_access"("p_property_id" "uuid", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_providers"() RETURNS TABLE("id" "uuid", "name" "text", "email" "text", "phone" "text", "specialty" "text", "notes" "text", "user_id" "uuid", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "provider_type" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_user_id UUID;
  v_is_admin BOOLEAN;
  v_team_ids UUID[];
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();
  
  -- Check if the user is an admin
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = v_user_id AND (profiles.is_super_admin = true OR profiles.role = 'admin')
  ) INTO v_is_admin;
  
  -- Get all teams the user is a member of
  SELECT array_agg(team_members.team_id) INTO v_team_ids
  FROM team_members
  WHERE team_members.user_id = v_user_id AND team_members.status = 'active';
  
  -- Return maintenance providers based on access level
  IF v_is_admin THEN
    -- Admins can see all maintenance providers
    RETURN QUERY 
    SELECT 
      mp.id,
      mp.name,
      mp.email,
      mp.phone,
      mp.specialty,
      mp.notes,
      mp.user_id,
      mp.created_at,
      mp.updated_at,
      'maintenance'::TEXT AS provider_type
    FROM maintenance_providers mp;
  ELSE
    -- Return the user's own maintenance providers
    RETURN QUERY 
    SELECT 
      mp.id,
      mp.name,
      mp.email,
      mp.phone,
      mp.specialty,
      mp.notes,
      mp.user_id,
      mp.created_at,
      mp.updated_at,
      'maintenance'::TEXT AS provider_type
    FROM maintenance_providers mp
    WHERE mp.user_id = v_user_id
    
    UNION
    
    -- Return maintenance providers from team members
    SELECT 
      mp.id,
      mp.name,
      mp.email,
      mp.phone,
      mp.specialty,
      mp.notes,
      mp.user_id,
      mp.created_at,
      mp.updated_at,
      'maintenance'::TEXT AS provider_type
    FROM maintenance_providers mp
    JOIN team_members tm ON mp.user_id = tm.user_id
    WHERE tm.team_id = ANY(v_team_ids)
    AND tm.status = 'active'
    AND mp.user_id != v_user_id;
  END IF;
  
  -- Return service providers formatted to match maintenance providers
  RETURN QUERY 
  SELECT 
    sp.id,
    (COALESCE(sp.first_name, '') || ' ' || COALESCE(sp.last_name, ''))::TEXT AS name,
    sp.email,
    NULL::TEXT AS phone,
    NULL::TEXT AS specialty,
    NULL::TEXT AS notes,
    NULL::UUID AS user_id,
    sp.created_at,
    sp.updated_at,
    'service'::TEXT AS provider_type
  FROM service_providers sp
  WHERE v_is_admin  -- Admins see all service providers
  OR (
    -- Regular users see service providers in their teams
    EXISTS (
      SELECT 1 FROM team_members tm1
      JOIN team_members tm2 ON tm1.team_id = tm2.team_id
      JOIN profiles p ON tm2.user_id = p.id
      WHERE tm1.user_id = v_user_id
      AND tm1.status = 'active'
      AND tm2.status = 'active'
      AND p.role = 'service_provider'
      AND p.email = sp.email
    )
  );
  
  RETURN;
END;
$$;


ALTER FUNCTION "public"."get_providers"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."get_providers"() IS 'Gets service providers from all teams the user is a member of, ensuring service providers are synced between all members of the team';



CREATE OR REPLACE FUNCTION "public"."get_recurring_task_series"("p_task_id" "uuid") RETURNS TABLE("task_id" "uuid", "task_title" "text", "task_status" "text", "task_due_date" "text", "task_completed_at" timestamp with time zone, "task_recurrence_count" integer, "task_is_original" boolean)
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    -- Simple approach: get all tasks in the same recurring series
    SELECT 
        t.id,
        t.title,
        t.status,
        t.due_date,
        t.completed_at,
        t.recurrence_count,
        CASE WHEN t.parent_task_id IS NULL THEN true ELSE false END as is_original
    FROM maintenance_tasks t
    WHERE 
        -- Get the task itself if it's the root
        t.id = p_task_id
        OR
        -- Get tasks that share the same parent (siblings)
        t.parent_task_id = (
            SELECT COALESCE(parent_task_id, p_task_id) 
            FROM maintenance_tasks 
            WHERE id = p_task_id
        )
        OR
        -- Get tasks that have this task as parent (children)
        t.parent_task_id = p_task_id
    ORDER BY t.recurrence_count;
END;
$$;


ALTER FUNCTION "public"."get_recurring_task_series"("p_task_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_service_provider_properties"("p_user_id" "uuid") RETURNS SETOF "public"."properties"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    is_service_provider BOOLEAN;
BEGIN
    -- Check if the user is a service provider
    SELECT EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = p_user_id AND profiles.role = 'service_provider'
    ) INTO is_service_provider;
    
    -- If not a service provider, return empty result
    IF NOT is_service_provider THEN
        RETURN;
    END IF;
    
    -- Return properties the service provider has access to via team membership
    RETURN QUERY
    SELECT DISTINCT ON (p.id)
        p.*
    FROM properties p
    JOIN team_properties tp ON p.id = tp.property_id
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id = p_user_id
    AND tm.status = 'active'
    ORDER BY p.id, p.name;
END;
$$;


ALTER FUNCTION "public"."get_service_provider_properties"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_table_columns"("table_name" "text") RETURNS TABLE("column_name" "text", "data_type" "text", "is_nullable" boolean, "column_default" "text")
    LANGUAGE "sql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $_$
  SELECT 
    column_name::text,
    data_type::text,
    (is_nullable = 'YES') as is_nullable,
    column_default::text
  FROM information_schema.columns
  WHERE table_schema = 'public' AND table_name = $1;
$_$;


ALTER FUNCTION "public"."get_table_columns"("table_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_table_size"("table_name" "text") RETURNS TABLE("size_bytes" "text", "size_pretty" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT
    pg_total_relation_size(table_name)::text,
    pg_size_pretty(pg_total_relation_size(table_name))
  FROM information_schema.tables
  WHERE table_schema = 'public'
  AND table_name = table_name
  LIMIT 1;
END;
$$;


ALTER FUNCTION "public"."get_table_size"("table_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_table_structure"("table_name" "text") RETURNS TABLE("column_name" "text", "data_type" "text", "is_nullable" boolean, "column_default" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.column_name::text,
    c.data_type::text,
    c.is_nullable = 'YES' as is_nullable,
    c.column_default::text
  FROM information_schema.columns c
  WHERE c.table_schema = 'public'
  AND c.table_name = table_name
  ORDER BY c.ordinal_position;
END;
$$;


ALTER FUNCTION "public"."get_table_structure"("table_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_tables"() RETURNS TABLE("table_name" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT tablename::text
  FROM pg_tables
  WHERE schemaname = 'public'
  ORDER BY tablename;
END;
$$;


ALTER FUNCTION "public"."get_tables"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_tasks_for_properties_or_user"("property_ids" "uuid"[], "input_user_id" "uuid") RETURNS SETOF "public"."maintenance_tasks"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM maintenance_tasks
  WHERE property_id = ANY(property_ids) OR user_id = input_user_id;
END;
$$;


ALTER FUNCTION "public"."get_tasks_for_properties_or_user"("property_ids" "uuid"[], "input_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_team_damage_reports"("p_team_id" "uuid") RETURNS TABLE("id" "uuid", "title" "text", "description" "text", "status" "text", "property_id" "uuid", "property_name" "text", "user_id" "uuid", "user_email" character varying, "provider_id" "uuid", "provider_name" "text", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "platform" "text", "team_id" "uuid")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        p.name AS property_name,
        dr.user_id,
        u.email AS user_email,
        dr.provider_id,
        mp.name AS provider_name,
        dr.created_at,
        dr.updated_at,
        dr.platform,
        dr.team_id
    FROM
        damage_reports dr
    JOIN
        properties p ON dr.property_id = p.id
    LEFT JOIN
        auth.users u ON dr.user_id = u.id
    LEFT JOIN
        maintenance_providers mp ON dr.provider_id = mp.id
    WHERE
        dr.team_id = p_team_id
        OR dr.property_id IN (
            SELECT tp.property_id FROM team_properties tp WHERE tp.team_id = p_team_id
        )
    ORDER BY
        dr.created_at DESC;
END;
$$;


ALTER FUNCTION "public"."get_team_damage_reports"("p_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_team_members"("p_team_id" "uuid") RETURNS TABLE("id" "uuid", "team_id" "uuid", "user_id" "uuid", "added_by" "uuid", "status" "text", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "user_email" "text", "user_name" "text", "role" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$ BEGIN RETURN QUERY SELECT tm.id, tm.team_id, tm.user_id, tm.added_by, tm.status, tm.created_at, tm.updated_at, p.email AS user_email, CONCAT(p.first_name, ' ', p.last_name) AS user_name, p.role::text FROM team_members tm JOIN profiles p ON tm.user_id = p.id WHERE tm.team_id = p_team_id; END; $$;


ALTER FUNCTION "public"."get_team_members"("p_team_id" "uuid") OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."team_members" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "team_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "added_by" "uuid" NOT NULL,
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);

ALTER TABLE ONLY "public"."team_members" FORCE ROW LEVEL SECURITY;


ALTER TABLE "public"."team_members" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."team_members_with_profiles" AS
 SELECT "tm"."id",
    "tm"."team_id",
    "tm"."user_id",
    "tm"."added_by",
    "tm"."status",
    "tm"."created_at",
    "tm"."updated_at",
    "p"."email",
    "p"."first_name",
    "p"."last_name",
    "p"."avatar_url",
    "p"."role" AS "profile_role",
    "p"."is_super_admin"
   FROM ("public"."team_members" "tm"
     JOIN "public"."profiles" "p" ON (("tm"."user_id" = "p"."id")));


ALTER TABLE "public"."team_members_with_profiles" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_team_members_with_profiles"("p_team_id" "uuid") RETURNS SETOF "public"."team_members_with_profiles"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  SELECT * FROM team_members_with_profiles
  WHERE team_id = p_team_id;
$$;


ALTER FUNCTION "public"."get_team_members_with_profiles"("p_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_team_name_by_id"("team_id" "uuid") RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$ BEGIN RETURN (SELECT name FROM teams WHERE id = team_id); END; $$;


ALTER FUNCTION "public"."get_team_name_by_id"("team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_team_properties"("p_team_id" "uuid") RETURNS SETOF "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT tp.property_id
  FROM team_properties tp
  WHERE tp.team_id = p_team_id;
END;
$$;


ALTER FUNCTION "public"."get_team_properties"("p_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_unique_user_properties"("p_user_id" "uuid") RETURNS TABLE("id" "uuid", "name" "text", "address" "text", "city" "text", "state" "text", "user_id" "uuid", "team_id" "uuid", "created_at" timestamp with time zone, "updated_at" timestamp with time zone)
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
    -- Get user's own properties
    SELECT DISTINCT ON (p.id)
        p.id,
        p.name,
        p.address,
        p.city,
        p.state,
        p.user_id,
        p.team_id,
        p.created_at,
        p.updated_at
    FROM
        properties p
    WHERE
        -- User's own properties
        p.user_id = p_user_id
        -- Admin access
        OR EXISTS (SELECT 1 FROM profiles prf WHERE prf.id = p_user_id AND (prf.is_super_admin = true OR prf.role = 'admin'))
        -- Team member access
        OR EXISTS (
            SELECT 1 FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tp.property_id = p.id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
    ORDER BY
        p.id, p.name;
$$;


ALTER FUNCTION "public"."get_unique_user_properties"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_accessible_properties"("p_user_id" "uuid") RETURNS SETOF "public"."properties"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  is_admin BOOLEAN;
BEGIN
  -- Check if the user is a super admin or admin
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin')
  ) INTO is_admin;
  
  -- For admins, return all properties
  IF is_admin THEN
    RETURN QUERY
    SELECT * FROM properties;
  ELSE
    -- Return properties the user owns
    RETURN QUERY
    SELECT * FROM properties
    WHERE user_id = p_user_id
    
    UNION
    
    -- Return properties the user has access to via team membership
    SELECT p.* FROM properties p
    JOIN team_properties tp ON p.id = tp.property_id
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id = p_user_id
    AND tm.status = 'active';
  END IF;
END;
$$;


ALTER FUNCTION "public"."get_user_accessible_properties"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_damage_photos"("p_user_id" "uuid") RETURNS TABLE("id" "uuid", "damage_report_id" "uuid", "file_name" "text", "file_path" "text", "caption" "text", "user_id" "uuid", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "media_type" "text", "public_url" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_is_admin BOOLEAN;
  v_role TEXT;
BEGIN
  -- Check if user is admin or super admin
  SELECT 
    is_super_admin,
    role::TEXT
  INTO v_is_admin, v_role
  FROM profiles
  WHERE id = p_user_id;
  
  -- If admin or super admin, return all photos
  IF v_is_admin OR v_role = 'admin' THEN
    RETURN QUERY 
    SELECT 
      dp.id,
      dp.damage_report_id,
      dp.file_name,
      dp.file_path,
      dp.caption,
      dp.user_id,
      dp.created_at,
      dp.updated_at,
      dp.media_type,
      'https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/damage-photos/' || dp.file_path AS public_url
    FROM damage_photos dp;
    RETURN;
  END IF;
  
  -- Return photos for damage reports created by the user
  RETURN QUERY 
  SELECT 
    dp.id,
    dp.damage_report_id,
    dp.file_name,
    dp.file_path,
    dp.caption,
    dp.user_id,
    dp.created_at,
    dp.updated_at,
    dp.media_type,
    'https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/damage-photos/' || dp.file_path AS public_url
  FROM damage_photos dp
  JOIN damage_reports dr ON dp.damage_report_id = dr.id
  WHERE dr.user_id = p_user_id OR dp.user_id = p_user_id;
  
  -- Return photos for damage reports for properties owned by the user
  RETURN QUERY 
  SELECT 
    dp.id,
    dp.damage_report_id,
    dp.file_name,
    dp.file_path,
    dp.caption,
    dp.user_id,
    dp.created_at,
    dp.updated_at,
    dp.media_type,
    'https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/damage-photos/' || dp.file_path AS public_url
  FROM damage_photos dp
  JOIN damage_reports dr ON dp.damage_report_id = dr.id
  JOIN properties p ON dr.property_id = p.id
  WHERE p.user_id = p_user_id;
  
  -- Return photos for damage reports for properties in teams the user is a member of
  RETURN QUERY 
  SELECT 
    dp.id,
    dp.damage_report_id,
    dp.file_name,
    dp.file_path,
    dp.caption,
    dp.user_id,
    dp.created_at,
    dp.updated_at,
    dp.media_type,
    'https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/damage-photos/' || dp.file_path AS public_url
  FROM damage_photos dp
  JOIN damage_reports dr ON dp.damage_report_id = dr.id
  JOIN properties p ON dr.property_id = p.id
  JOIN team_properties tp ON p.id = tp.property_id
  JOIN team_members tm ON tp.team_id = tm.team_id
  WHERE tm.user_id = p_user_id;
  
  RETURN;
END;
$$;


ALTER FUNCTION "public"."get_user_damage_photos"("p_user_id" "uuid") OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."damage_reports" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "property_id" "uuid" NOT NULL,
    "title" "text" NOT NULL,
    "description" "text" NOT NULL,
    "status" "text" DEFAULT 'open'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "provider_id" "uuid",
    "platform" "text",
    "team_id" "uuid"
);


ALTER TABLE "public"."damage_reports" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_damage_reports"() RETURNS SETOF "public"."damage_reports"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if the user is a super admin or admin
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    -- Return all damage reports for admins
    RETURN QUERY
    SELECT dr.*
    FROM damage_reports dr
    ORDER BY dr.created_at DESC;
  ELSE
    -- For property managers, return their own reports
    RETURN QUERY
    SELECT dr.*
    FROM damage_reports dr
    WHERE dr.user_id = auth.uid()
    ORDER BY dr.created_at DESC;
  END IF;
END;
$$;


ALTER FUNCTION "public"."get_user_damage_reports"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_damage_reports"("p_user_id" "uuid") RETURNS TABLE("id" "uuid", "title" "text", "description" "text", "status" "text", "property_id" "uuid", "property_name" "text", "user_id" "uuid", "user_email" character varying, "provider_id" "uuid", "provider_name" "text", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "platform" "text", "team_id" "uuid", "property_owner_id" "uuid", "source" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_teams UUID[];
BEGIN
    -- Get user's own reports
    RETURN QUERY
    SELECT
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        dr.property_name,
        dr.user_id,
        dr.user_email,
        dr.provider_id,
        dr.provider_name,
        dr.created_at,
        dr.updated_at,
        dr.platform,
        dr.team_id,
        dr.property_owner_id,
        'own'::TEXT AS source
    FROM
        damage_reports_with_property dr
    WHERE
        dr.user_id = p_user_id;

    -- Get teams the user is a member of
    SELECT array_agg(tm.team_id) INTO v_teams
    FROM team_members tm
    WHERE tm.user_id = p_user_id AND tm.status = 'active';

    -- If user is in any teams, get team reports
    IF v_teams IS NOT NULL AND array_length(v_teams, 1) > 0 THEN
        -- Get reports for properties in teams the user is a member of
        RETURN QUERY
        SELECT DISTINCT ON (dr.id)
            dr.id,
            dr.title,
            dr.description,
            dr.status,
            dr.property_id,
            dr.property_name,
            dr.user_id,
            dr.user_email,
            dr.provider_id,
            dr.provider_name,
            dr.created_at,
            dr.updated_at,
            dr.platform,
            dr.team_id,
            dr.property_owner_id,
            'team'::TEXT AS source
        FROM
            damage_reports_with_property dr
        WHERE
            (dr.team_id = ANY(v_teams) OR dr.property_id IN (
                SELECT tp.property_id FROM team_properties tp WHERE tp.team_id = ANY(v_teams)
            ))
            AND dr.user_id != p_user_id;
    END IF;

    -- For admins, get all other reports
    IF EXISTS (SELECT 1 FROM profiles p_prof WHERE p_prof.id = p_user_id AND (p_prof.is_super_admin = true OR p_prof.role = 'admin')) THEN
        RETURN QUERY
        SELECT DISTINCT ON (dr.id)
            dr.id,
            dr.title,
            dr.description,
            dr.status,
            dr.property_id,
            dr.property_name,
            dr.user_id,
            dr.user_email,
            dr.provider_id,
            dr.provider_name,
            dr.created_at,
            dr.updated_at,
            dr.platform,
            dr.team_id,
            dr.property_owner_id,
            'admin'::TEXT AS source
        FROM
            damage_reports_with_property dr
        WHERE
            dr.user_id != p_user_id
            AND (v_teams IS NULL OR NOT (dr.team_id = ANY(v_teams) OR dr.property_id IN (
                SELECT tp.property_id FROM team_properties tp WHERE tp.team_id = ANY(v_teams)
            )));
    END IF;

    -- For service providers, get reports assigned to them
    IF EXISTS (SELECT 1 FROM profiles p_prof WHERE p_prof.id = p_user_id AND p_prof.role = 'service_provider') THEN
        RETURN QUERY
        SELECT DISTINCT ON (dr.id)
            dr.id,
            dr.title,
            dr.description,
            dr.status,
            dr.property_id,
            dr.property_name,
            dr.user_id,
            dr.user_email,
            dr.provider_id,
            dr.provider_name,
            dr.created_at,
            dr.updated_at,
            dr.platform,
            dr.team_id,
            dr.property_owner_id,
            'assigned'::TEXT AS source
        FROM
            damage_reports_with_property dr
        WHERE
            dr.provider_id = p_user_id
            AND dr.user_id != p_user_id
            AND (v_teams IS NULL OR NOT (dr.team_id = ANY(v_teams) OR dr.property_id IN (
                SELECT tp.property_id FROM team_properties tp WHERE tp.team_id = ANY(v_teams)
            )));
    END IF;
END;
$$;


ALTER FUNCTION "public"."get_user_damage_reports"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_damage_reports_simple"("p_user_id" "uuid") RETURNS TABLE("id" "uuid", "title" "text", "description" "text", "status" "text", "property_id" "uuid", "property_name" "text", "user_id" "uuid", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "provider_id" "uuid", "provider_name" "text", "platform" "text", "team_id" "uuid")
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
    SELECT
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        p.name AS property_name,
        dr.user_id,
        dr.created_at,
        dr.updated_at,
        dr.provider_id,
        mp.name AS provider_name,
        dr.platform,
        dr.team_id
    FROM
        damage_reports dr
    JOIN
        properties p ON dr.property_id = p.id
    LEFT JOIN
        maintenance_providers mp ON dr.provider_id = mp.id
    WHERE
        -- User's own reports
        dr.user_id = p_user_id
        -- Admin access
        OR EXISTS (SELECT 1 FROM profiles prf WHERE prf.id = p_user_id AND (prf.is_super_admin = true OR prf.role = 'admin'))
        -- Team member access via property
        OR EXISTS (
            SELECT 1 FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tp.property_id = dr.property_id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
        -- Team member access via team
        OR EXISTS (
            SELECT 1 FROM team_members tm
            WHERE tm.team_id = dr.team_id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
        -- Service provider access
        OR (dr.provider_id = p_user_id)
    ORDER BY dr.created_at DESC;
$$;


ALTER FUNCTION "public"."get_user_damage_reports_simple"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_inventory_items"("p_user_id" "uuid") RETURNS TABLE("id" "uuid", "name" "text", "property_id" "uuid", "property_name" "text", "collection" "text", "quantity" integer, "min_quantity" integer, "price" numeric, "amazon_url" "text", "walmart_url" "text", "image_url" "text", "last_ordered" timestamp with time zone, "user_id" "uuid")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.id,
    i.name,
    i.property_id,
    p.name AS property_name,
    i.collection,
    i.quantity,
    i.min_quantity,
    i.price,
    i.amazon_url,
    i.walmart_url,
    i.image_url,
    i.updated_at AS last_ordered,
    i.user_id
  FROM 
    inventory_items i
  LEFT JOIN 
    properties p ON i.property_id = p.id
  WHERE (
    -- Items owned by the user
    i.user_id = p_user_id
    OR
    -- Items from teams the user is a member of
    i.team_id IN (
      SELECT tm.team_id 
      FROM team_members tm 
      WHERE tm.user_id = p_user_id 
      AND tm.status = 'active'
    )
  )
  ORDER BY 
    i.name;
END;
$$;


ALTER FUNCTION "public"."get_user_inventory_items"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_maintenance_tasks"("p_user_id" "uuid") RETURNS SETOF "public"."maintenance_tasks"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_is_admin BOOLEAN;
  v_role user_role;
  v_team_ids UUID[];
  v_property_ids UUID[];
BEGIN
  -- Get user role and admin status
  SELECT is_super_admin, role INTO v_is_admin, v_role
  FROM profiles
  WHERE id = p_user_id;
  
  -- If user is admin or super admin, return all tasks
  IF v_is_admin OR v_role = 'admin' THEN
    RETURN QUERY SELECT * FROM maintenance_tasks;
    RETURN;
  END IF;
  
  -- If user is service provider, return tasks they created or are assigned to
  IF v_role = 'service_provider' THEN
    RETURN QUERY 
    SELECT * FROM maintenance_tasks
    WHERE user_id = p_user_id OR provider_id = p_user_id OR assigned_to = p_user_id::text;
    RETURN;
  END IF;
  
  -- For property managers and staff, get team memberships
  SELECT ARRAY_AGG(team_id) INTO v_team_ids
  FROM team_members
  WHERE user_id = p_user_id AND status = 'active';
  
  -- If user is in teams, get team property IDs
  IF v_team_ids IS NOT NULL AND array_length(v_team_ids, 1) > 0 THEN
    SELECT ARRAY_AGG(property_id) INTO v_property_ids
    FROM team_properties
    WHERE team_id = ANY(v_team_ids);
    
    -- Return tasks for team properties or created by the user
    IF v_property_ids IS NOT NULL AND array_length(v_property_ids, 1) > 0 THEN
      RETURN QUERY 
      SELECT * FROM maintenance_tasks
      WHERE property_id = ANY(v_property_ids) OR user_id = p_user_id;
      RETURN;
    END IF;
  END IF;
  
  -- Default case - just return tasks created by the user
  RETURN QUERY 
  SELECT * FROM maintenance_tasks
  WHERE user_id = p_user_id;
  
  RETURN;
END;
$$;


ALTER FUNCTION "public"."get_user_maintenance_tasks"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_properties"("p_user_id" "uuid") RETURNS SETOF "public"."properties"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
    SELECT DISTINCT ON (p.id)
        p.*
    FROM
        properties p
    WHERE
        -- User's own properties
        p.user_id = p_user_id
        -- Admin access
        OR EXISTS (SELECT 1 FROM profiles prf WHERE prf.id = p_user_id AND (prf.is_super_admin = true OR prf.role = 'admin'))
        -- Team member access
        OR EXISTS (
            SELECT 1 FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tp.property_id = p.id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
    ORDER BY
        p.id, p.name;
$$;


ALTER FUNCTION "public"."get_user_properties"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_role"("user_id" "uuid") RETURNS "text"
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT role::text FROM public.profiles WHERE id = user_id;
$$;


ALTER FUNCTION "public"."get_user_role"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_role_properties"("p_user_id" "uuid") RETURNS SETOF "public"."properties"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    user_role TEXT;
BEGIN
    -- Get the user's role
    SELECT profiles.role INTO user_role
    FROM profiles
    WHERE profiles.id = p_user_id;
    
    -- Route to the appropriate function based on role
    IF user_role = 'service_provider' THEN
        RETURN QUERY
        SELECT * FROM get_service_provider_properties(p_user_id);
    ELSE
        -- For all other roles (property_manager, admin, etc.), use get_user_properties
        RETURN QUERY
        SELECT * FROM get_user_properties(p_user_id);
    END IF;
END;
$$;


ALTER FUNCTION "public"."get_user_role_properties"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_role_sd"("user_id" "uuid") RETURNS "text"
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT role::text FROM profiles WHERE id = user_id;
$$;


ALTER FUNCTION "public"."get_user_role_sd"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_team_access"("team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
    select exists (
      select 1 from teams 
      where id = team_id 
      and owner_id = auth.uid()
    )
    or exists (
      select 1 from team_members
      where team_id = team_id
      and user_id = auth.uid()
      and status = 'active'
    );
  $$;


ALTER FUNCTION "public"."get_user_team_access"("team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_team_ids"() RETURNS SETOF "uuid"
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  -- Teams the user owns
  SELECT id FROM teams WHERE owner_id = auth.uid()
  UNION
  -- Teams the user is a member of
  SELECT team_id FROM team_members WHERE user_id = auth.uid() AND status = 'active';
$$;


ALTER FUNCTION "public"."get_user_team_ids"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_team_memberships"("p_user_id" "uuid") RETURNS TABLE("team_id" "uuid")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Return all team memberships for the user
  RETURN QUERY 
  SELECT tm.team_id
  FROM team_members tm
  WHERE tm.user_id = p_user_id
  AND tm.status = 'active';
  
  RETURN;
END;
$$;


ALTER FUNCTION "public"."get_user_team_memberships"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_teams"("user_id" "uuid") RETURNS SETOF "uuid"
    LANGUAGE "sql" SECURITY DEFINER
    AS $_$
  SELECT team_id FROM team_members WHERE user_id = $1;
$_$;


ALTER FUNCTION "public"."get_user_teams"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_booking_automation"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Insert a record into a queue table that will be processed by a scheduled function
  INSERT INTO automation_queue (booking_id, created_at)
  VALUES (NEW.id, NOW());
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_booking_automation"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if a profile already exists for this user
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = new.id) THEN
    -- Create the profile if it doesn't exist
    BEGIN
      INSERT INTO public.profiles (
        id, 
        email, 
        first_name, 
        last_name, 
        role, 
        is_super_admin,
        created_at,
        updated_at
      )
      VALUES (
        new.id,
        COALESCE(new.email, ''),
        COALESCE(new.raw_user_meta_data->>'first_name', 'User'),
        COALESCE(new.raw_user_meta_data->>'last_name', ''),
        COALESCE((new.raw_user_meta_data->>'role')::user_role, 'property_manager'::user_role),
        COALESCE((new.raw_user_meta_data->>'is_super_admin')::boolean, FALSE),
        NOW(),
        NOW()
      );
      
      -- Log successful profile creation
      RAISE LOG 'Profile created for user %', new.id;
    EXCEPTION WHEN OTHERS THEN
      -- Log the error but don't block signup
      RAISE LOG 'Error in handle_new_user creating profile: %', SQLERRM;
    END;
  ELSE
    -- Log that profile already exists
    RAISE LOG 'Profile already exists for user %', new.id;
  END IF;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't block signup
    RAISE LOG 'Error in handle_new_user: %', SQLERRM;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_task_completion"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Check if task status changed to completed
    IF OLD.status != 'completed' AND NEW.status = 'completed' THEN
        -- Set completed_at timestamp
        NEW.completed_at := NOW();
        
        -- If this is a recurring task, create the next occurrence
        IF NEW.is_recurring THEN
            PERFORM create_next_recurring_task(NEW.id);
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_task_completion"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_damage_note_access"("p_note_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_report_id uuid;
  v_property_id uuid;
BEGIN
  -- Get the report_id for this note
  SELECT damage_report_id INTO v_report_id
  FROM damage_notes
  WHERE id = p_note_id;

  -- Get the property_id for this report
  SELECT property_id INTO v_property_id
  FROM damage_reports
  WHERE id = v_report_id;

  -- Super admins and admins have access to all notes
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Note creator has access
  IF EXISTS (
    SELECT 1 FROM damage_notes
    WHERE id = p_note_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to notes for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_damage_note_access"("p_note_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_damage_photo_access"("p_photo_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_report_id uuid;
  v_property_id uuid;
BEGIN
  -- Get the report_id for this photo
  SELECT damage_report_id INTO v_report_id
  FROM damage_photos
  WHERE id = p_photo_id;

  -- Get the property_id for this report
  SELECT property_id INTO v_property_id
  FROM damage_reports
  WHERE id = v_report_id;

  -- Super admins and admins have access to all photos
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Photo creator has access
  IF EXISTS (
    SELECT 1 FROM damage_photos
    WHERE id = p_photo_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to photos for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_damage_photo_access"("p_photo_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_damage_report_access"("p_report_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_property_id uuid;
  v_team_id uuid;
BEGIN
  -- Get the property_id and team_id for this report
  SELECT property_id, team_id INTO v_property_id, v_team_id
  FROM damage_reports
  WHERE id = p_report_id;

  -- Super admins and admins have access to all reports
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Report creator has access
  IF EXISTS (
    SELECT 1 FROM damage_reports
    WHERE id = p_report_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to reports for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to reports directly associated with their teams
  IF v_team_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_members tm
    WHERE tm.team_id = v_team_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  -- Service providers have access to reports assigned to them
  IF EXISTS (
    SELECT 1 FROM damage_reports
    WHERE id = p_report_id AND provider_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_damage_report_access"("p_report_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_inventory_item_access"("p_item_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_property_id uuid;
  v_team_id uuid;
BEGIN
  -- Get the property_id and team_id for this item
  SELECT property_id, team_id INTO v_property_id, v_team_id
  FROM inventory_items
  WHERE id = p_item_id;

  -- Super admins and admins have access to all items
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Item creator has access
  IF EXISTS (
    SELECT 1 FROM inventory_items
    WHERE id = p_item_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Direct team access if team_id is set
  IF v_team_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = v_team_id AND user_id = auth.uid() AND status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to items for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_inventory_item_access"("p_item_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_maintenance_provider_access"("p_provider_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Super admins and admins have access to all providers
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Provider creator has access
  IF EXISTS (
    SELECT 1 FROM maintenance_providers
    WHERE id = p_provider_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Service providers can view their own provider record
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'service_provider' AND
    EXISTS (
      SELECT 1 FROM maintenance_providers
      WHERE id = p_provider_id AND email = profiles.email
    )
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to providers in their teams
  IF EXISTS (
    SELECT 1 FROM team_members tm1
    JOIN team_members tm2 ON tm1.team_id = tm2.team_id
    JOIN maintenance_providers mp ON tm2.user_id = mp.user_id
    WHERE tm1.user_id = auth.uid() AND mp.id = p_provider_id
    AND tm1.status = 'active' AND tm2.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team owners have access to all providers in their teams
  IF EXISTS (
    SELECT 1 FROM teams t
    JOIN team_members tm ON t.id = tm.team_id
    JOIN maintenance_providers mp ON tm.user_id = mp.user_id
    WHERE t.owner_id = auth.uid() AND mp.id = p_provider_id
    AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_maintenance_provider_access"("p_provider_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_maintenance_task_access"("task_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_user_id UUID := auth.uid();
  v_task_exists BOOLEAN;
  v_is_super_admin BOOLEAN;
  v_is_admin BOOLEAN;
  v_is_creator BOOLEAN;
  v_is_assignee BOOLEAN;
  v_is_provider BOOLEAN;
  v_is_property_manager_with_access BOOLEAN;
  v_is_team_member_with_access BOOLEAN;
BEGIN
  -- If task_id is null, just check if the user can access any tasks
  IF task_id IS NULL THEN
    RETURN TRUE;
  END IF;
  
  -- Check if task exists
  SELECT EXISTS(SELECT 1 FROM maintenance_tasks WHERE id = task_id) INTO v_task_exists;
  IF NOT v_task_exists THEN
    RETURN FALSE;
  END IF;
  
  -- Check if user is super admin
  SELECT is_super_admin INTO v_is_super_admin FROM profiles WHERE id = v_user_id;
  IF v_is_super_admin THEN
    RETURN TRUE;
  END IF;
  
  -- Check if user is admin
  SELECT role = 'admin' INTO v_is_admin FROM profiles WHERE id = v_user_id;
  IF v_is_admin THEN
    RETURN TRUE;
  END IF;
  
  -- Check if user created the task
  SELECT EXISTS(SELECT 1 FROM maintenance_tasks WHERE id = task_id AND user_id = v_user_id) 
  INTO v_is_creator;
  IF v_is_creator THEN
    RETURN TRUE;
  END IF;
  
  -- Check if user is assigned to the task
  SELECT EXISTS(SELECT 1 FROM maintenance_tasks WHERE id = task_id AND assigned_to::text = v_user_id::text) 
  INTO v_is_assignee;
  IF v_is_assignee THEN
    RETURN TRUE;
  END IF;
  
  -- Check if user is the service provider for this task
  SELECT EXISTS(SELECT 1 FROM maintenance_tasks WHERE id = task_id AND provider_id = v_user_id) 
  INTO v_is_provider;
  IF v_is_provider THEN
    RETURN TRUE;
  END IF;
  
  -- Check if user is a property manager with access to the property
  SELECT EXISTS(
    SELECT 1 
    FROM profiles, maintenance_tasks mt, properties p
    WHERE profiles.id = v_user_id 
    AND profiles.role = 'property_manager'
    AND mt.id = task_id
    AND p.id = mt.property_id
    AND p.user_id = v_user_id
  ) INTO v_is_property_manager_with_access;
  IF v_is_property_manager_with_access THEN
    RETURN TRUE;
  END IF;
  
  -- Check if user is a team member with access to the property via team
  SELECT EXISTS(
    SELECT 1 
    FROM team_members tm
    JOIN team_properties tp ON tm.team_id = tp.team_id
    JOIN maintenance_tasks mt ON tp.property_id = mt.property_id
    WHERE tm.user_id = v_user_id
    AND mt.id = task_id
  ) INTO v_is_team_member_with_access;
  IF v_is_team_member_with_access THEN
    RETURN TRUE;
  END IF;
  
  -- If none of the above conditions are met, user doesn't have access
  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_maintenance_task_access"("task_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_maintenance_task_access_impersonation"("p_task_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_property_id uuid;
  v_provider_id uuid;
  v_provider_email text;
  v_user_id uuid;
BEGIN
  -- Get the task details
  SELECT property_id, provider_id, provider_email, user_id INTO v_property_id, v_provider_id, v_provider_email, v_user_id
  FROM maintenance_tasks
  WHERE id = p_task_id;

  -- Super admins and admins have access to all tasks
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Task creator has access
  IF v_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- Provider assigned to the task has access
  IF v_provider_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- Provider with matching email has access
  IF v_provider_email IS NOT NULL AND EXISTS (
    SELECT 1 FROM service_providers
    WHERE id = auth.uid() AND email = v_provider_email
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to tasks for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_maintenance_task_access_impersonation"("p_task_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_permission"("user_id" "uuid", "permission_name" "public"."permission_type", "team_id" "uuid" DEFAULT NULL::"uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  user_role user_role;
  has_explicit_permission BOOLEAN;
BEGIN
  -- Get user's role
  SELECT role INTO user_role FROM public.profiles WHERE id = user_id;
  
  -- Super admins always have all permissions
  IF user_role = 'super_admin' THEN
    RETURN TRUE;
  END IF;
  
  -- Admin permissions
  IF user_role = 'admin' THEN
    -- Admins have access to all general permissions
    RETURN TRUE;
  END IF;
  
  -- For property_manager, staff, and service_provider, check explicit permissions
  IF team_id IS NOT NULL THEN
    SELECT EXISTS (
      SELECT 1 FROM public.user_permissions 
      WHERE user_id = user_id 
      AND permission = permission_name 
      AND team_id = team_id
      AND enabled = TRUE
    ) INTO has_explicit_permission;
    
    RETURN has_explicit_permission;
  ELSE
    -- If no team_id is provided, check if the user has the permission in any team
    SELECT EXISTS (
      SELECT 1 FROM public.user_permissions 
      WHERE user_id = user_id 
      AND permission = permission_name
      AND enabled = TRUE
    ) INTO has_explicit_permission;
    
    -- Property Managers can always manage their own teams and staff
    IF user_role = 'property_manager' AND 
       permission_name IN ('manage_staff', 'manage_service_providers') THEN
      RETURN TRUE;
    END IF;
    
    RETURN has_explicit_permission;
  END IF;
END;
$$;


ALTER FUNCTION "public"."has_permission"("user_id" "uuid", "permission_name" "public"."permission_type", "team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_property_access"("p_property_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Super admins and admins have access to all properties
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Property owners have access to their own properties
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to team properties
  IF EXISTS (
    SELECT 1 FROM team_members tm
    JOIN team_properties tp ON tm.team_id = tp.team_id
    WHERE tm.user_id = auth.uid()
    AND tp.property_id = p_property_id
    AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_property_access"("p_property_id" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."has_property_access"("p_property_id" "uuid") IS 'Determines if the current user has access to a property. Super admins and admins have access to all properties.';



CREATE OR REPLACE FUNCTION "public"."has_property_document_access"("p_document_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_property_id uuid;
  v_user_id uuid;
  v_is_private boolean;
BEGIN
  -- Get document details
  SELECT property_id, user_id, is_private INTO v_property_id, v_user_id, v_is_private
  FROM property_documents
  WHERE id = p_document_id;

  -- Super admins and admins have access to all documents
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Document creator always has access
  IF v_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- If document is private, only creator can access
  IF v_is_private = true THEN
    RETURN FALSE;
  END IF;

  -- Property owner has access to all non-private documents
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = v_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to non-private documents
  IF EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_property_document_access"("p_document_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_property_file_access"("p_file_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_property_id uuid;
  v_user_id uuid;
  v_is_private boolean;
BEGIN
  -- Get file details
  SELECT property_id, user_id, is_private INTO v_property_id, v_user_id, v_is_private
  FROM property_files
  WHERE id = p_file_id;

  -- Super admins and admins have access to all files
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- File uploader always has access
  IF v_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- If file is private, only uploader can access
  IF v_is_private = true THEN
    RETURN FALSE;
  END IF;

  -- Property owner has access to all non-private files
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = v_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to non-private files
  IF EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_property_file_access"("p_file_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_purchase_order_access"("p_order_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_property_id uuid;
  v_user_id uuid;
BEGIN
  -- Get the property_id and user_id for this order
  SELECT property_id, user_id INTO v_property_id, v_user_id
  FROM purchase_orders
  WHERE id = p_order_id;

  -- Super admins and admins have access to all orders
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Order creator has access
  IF v_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- Property managers have access to orders for properties they own
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = v_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to orders for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_purchase_order_access"("p_order_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_purchase_order_item_access"("p_item_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_order_id uuid;
  v_property_id uuid;
BEGIN
  -- Get the purchase_order_id for this item
  SELECT purchase_order_id INTO v_order_id
  FROM purchase_order_items
  WHERE id = p_item_id;

  -- Get the property_id for this order
  SELECT property_id INTO v_property_id
  FROM purchase_orders
  WHERE id = v_order_id;

  -- Super admins and admins have access to all items
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Order creator has access
  IF EXISTS (
    SELECT 1 FROM purchase_orders
    WHERE id = v_order_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to items for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_purchase_order_item_access"("p_item_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_service_provider_access"("p_provider_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Super admins and admins have access to all service providers
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Users have access to their own service provider profile
  IF auth.uid() = p_provider_id THEN
    RETURN TRUE;
  END IF;

  -- Property managers have access to service providers in their teams
  IF EXISTS (
    SELECT 1 FROM team_members tm1
    JOIN team_members tm2 ON tm1.team_id = tm2.team_id
    WHERE tm1.user_id = auth.uid()
    AND tm2.user_id = p_provider_id
    AND tm1.status = 'active'
    AND tm2.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_service_provider_access"("p_provider_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_user_access"("p_user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  impersonating_id text;
BEGIN
  -- Handle null user_id
  IF p_user_id IS NULL THEN
    RETURN FALSE;
  END IF;

  -- Super admins and admins have access to all users
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Users have access to their own data
  IF p_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- Check if the current user is impersonating the target user
  -- This requires a session variable to be set during impersonation
  BEGIN
    impersonating_id := current_setting('app.impersonating_user_id', TRUE);
    IF impersonating_id = p_user_id::text THEN
      RETURN TRUE;
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      -- If the setting doesn't exist, continue with other checks
      NULL;
  END;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."has_user_access"("p_user_id" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."has_user_access"("p_user_id" "uuid") IS 'Determines if the current user has access to another user''s data. Super admins and admins have access to all users, and users have access to their own data.';



CREATE OR REPLACE FUNCTION "public"."is_admin_user"("user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    AS $$
  SELECT is_super_admin FROM public.profiles WHERE id = user_id;
$$;


ALTER FUNCTION "public"."is_admin_user"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_current_user_super_admin"() RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles WHERE id = auth.uid() AND is_super_admin = true
  );
END;
$$;


ALTER FUNCTION "public"."is_current_user_super_admin"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_property_in_team"("p_property_id" "uuid", "p_team_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id AND team_id = p_team_id
  );
END;
$$;


ALTER FUNCTION "public"."is_property_in_team"("p_property_id" "uuid", "p_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_service_provider_in_team"("p_property_manager_id" "uuid", "p_service_provider_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM team_members tm1
    JOIN team_members tm2 ON tm1.team_id = tm2.team_id
    JOIN profiles sp ON tm2.user_id = sp.id
    WHERE tm1.user_id = p_property_manager_id
      AND tm1.status = 'active'
      AND tm2.user_id = p_service_provider_id
      AND tm2.status = 'active'
      AND sp.role = 'service_provider'
  );
END;
$$;


ALTER FUNCTION "public"."is_service_provider_in_team"("p_property_manager_id" "uuid", "p_service_provider_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_super_admin"("uid" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT COUNT(*) > 0 FROM profiles WHERE id = uid AND is_super_admin = true;
$$;


ALTER FUNCTION "public"."is_super_admin"("uid" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_super_admin_sd"("user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT is_super_admin FROM profiles WHERE id = user_id;
$$;


ALTER FUNCTION "public"."is_super_admin_sd"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_team_member"("team_id" "uuid", "user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.team_members 
    WHERE team_id = team_id AND user_id = user_id AND status = 'active'
  );
$$;


ALTER FUNCTION "public"."is_team_member"("team_id" "uuid", "user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_team_member_sd"("team_id" "uuid", "user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT EXISTS (
    SELECT 1 FROM team_members 
    WHERE team_id = team_id AND user_id = user_id AND status = 'active'
  );
$$;


ALTER FUNCTION "public"."is_team_member_sd"("team_id" "uuid", "user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_team_member_with_property_access"("p_user_id" "uuid", "p_property_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  is_member BOOLEAN;
BEGIN
  -- Check if the user is a super admin or admin
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin')
  ) INTO is_member;
  
  IF is_member THEN
    RETURN TRUE;
  END IF;
  
  -- Check if the user owns the property
  SELECT EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = p_user_id
  ) INTO is_member;
  
  IF is_member THEN
    RETURN TRUE;
  END IF;
  
  -- Check if the user is a member of a team that has access to the property
  SELECT EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = p_property_id
    AND tm.user_id = p_user_id
    AND tm.status = 'active'
  ) INTO is_member;
  
  RETURN is_member;
END;
$$;


ALTER FUNCTION "public"."is_team_member_with_property_access"("p_user_id" "uuid", "p_property_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_team_owner"("team_id" "uuid", "user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.teams WHERE id = team_id AND owner_id = user_id
  );
$$;


ALTER FUNCTION "public"."is_team_owner"("team_id" "uuid", "user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_team_owner_sd"("team_id" "uuid", "user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT EXISTS (
    SELECT 1 FROM teams WHERE id = team_id AND owner_id = user_id
  );
$$;


ALTER FUNCTION "public"."is_team_owner_sd"("team_id" "uuid", "user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."prevent_duplicate_property_names_in_team"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Check if there's already a property with the same name in any of the teams this property belongs to
    IF EXISTS (
        SELECT 1
        FROM team_properties tp1
        JOIN properties p ON tp1.property_id = p.id
        JOIN team_properties tp2 ON tp1.team_id = tp2.team_id
        WHERE
            p.name = NEW.name
            AND p.id != NEW.id
            AND tp2.property_id = NEW.id
    ) THEN
        RAISE EXCEPTION 'A property with the same name already exists in one of the teams this property belongs to';
    END IF;

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."prevent_duplicate_property_names_in_team"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."process_invitation_acceptance"("p_token" "text", "p_user_id" "uuid") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_invitation RECORD;
  v_team_id UUID;
  v_team_name TEXT;
  v_role TEXT;
  v_invited_by UUID;
  v_user_exists BOOLEAN;
  v_profile_exists BOOLEAN;
  v_result JSON;
  v_email TEXT;
BEGIN
  -- Check if the invitation exists and is valid
  SELECT * INTO v_invitation
  FROM team_invitations
  WHERE token = p_token
    AND status = 'pending'
    AND (expires_at IS NULL OR expires_at > NOW());
    
  IF v_invitation IS NULL THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'Invitation not found or expired'
    );
  END IF;
  
  -- Store invitation details
  v_team_id := v_invitation.team_id;
  v_team_name := v_invitation.team_name;
  v_role := v_invitation.role;
  v_invited_by := v_invitation.invited_by;
  v_email := v_invitation.email;
  
  -- Check if the user exists in auth.users
  SELECT EXISTS(SELECT 1 FROM auth.users WHERE id = p_user_id) INTO v_user_exists;
  
  IF NOT v_user_exists THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'User not found in auth system'
    );
  END IF;
  
  -- Check if the profile exists
  SELECT EXISTS(SELECT 1 FROM profiles WHERE id = p_user_id) INTO v_profile_exists;
  
  -- If profile doesn't exist, create it
  IF NOT v_profile_exists THEN
    -- Get user details from auth.users
    DECLARE
      v_auth_user RECORD;
      v_first_name TEXT;
      v_last_name TEXT;
    BEGIN
      SELECT * INTO v_auth_user FROM auth.users WHERE id = p_user_id;
      
      -- Extract metadata
      v_first_name := COALESCE(v_auth_user.raw_user_meta_data->>'first_name', '');
      v_last_name := COALESCE(v_auth_user.raw_user_meta_data->>'last_name', '');
      
      -- Create profile
      INSERT INTO profiles (id, email, first_name, last_name, role, created_at, updated_at)
      VALUES (
        p_user_id,
        COALESCE(v_auth_user.email, v_email),
        v_first_name,
        v_last_name,
        COALESCE(v_role, 'service_provider')::user_role,
        NOW(),
        NOW()
      );
      
      RAISE LOG 'Created profile for user % during invitation acceptance', p_user_id;
    EXCEPTION
      WHEN OTHERS THEN
        RAISE LOG 'Error creating profile for user % during invitation acceptance: %', p_user_id, SQLERRM;
        RETURN json_build_object(
          'success', FALSE,
          'error', 'Failed to create user profile: ' || SQLERRM
        );
    END;
  END IF;
  
  -- Check if the user is already a member of the team
  IF EXISTS(SELECT 1 FROM team_members WHERE team_id = v_team_id AND user_id = p_user_id) THEN
    -- Update the invitation status
    UPDATE team_invitations
    SET status = 'accepted', updated_at = NOW()
    WHERE token = p_token;
    
    RETURN json_build_object(
      'success', TRUE,
      'message', 'User is already a member of the team',
      'team_id', v_team_id,
      'team_name', v_team_name
    );
  END IF;
  
  -- Begin transaction
  BEGIN
    -- Add the user to the team
    INSERT INTO team_members (team_id, user_id, added_by, status)
    VALUES (v_team_id, p_user_id, v_invited_by, 'active');
    
    -- Update the invitation status
    UPDATE team_invitations
    SET status = 'accepted', updated_at = NOW()
    WHERE token = p_token;
    
    -- If the role is service_provider, add default permissions
    IF v_role = 'service_provider' THEN
      -- Call the function to add default permissions for service providers if it exists
      IF EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'add_service_provider_default_permissions') THEN
        PERFORM add_service_provider_default_permissions(p_user_id, v_team_id);
      END IF;
    END IF;
    
    -- Update the user's role if needed
    UPDATE profiles
    SET role = v_role::user_role
    WHERE id = p_user_id AND role != v_role::user_role;
    
    -- Commit transaction
    v_result := json_build_object(
      'success', TRUE,
      'message', 'Invitation accepted successfully',
      'team_id', v_team_id,
      'team_name', v_team_name,
      'role', v_role
    );
  EXCEPTION
    WHEN OTHERS THEN
      -- Rollback transaction
      RAISE LOG 'Error in process_invitation_acceptance: %', SQLERRM;
      v_result := json_build_object(
        'success', FALSE,
        'error', SQLERRM
      );
  END;
  
  RETURN v_result;
END;
$$;


ALTER FUNCTION "public"."process_invitation_acceptance"("p_token" "text", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."property_belongs_to_team"("p_property_id" "uuid", "p_team_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id
    AND team_id = p_team_id
  );
END;
$$;


ALTER FUNCTION "public"."property_belongs_to_team"("p_property_id" "uuid", "p_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."register_service_provider"("p_email" "text", "p_password" "text", "p_first_name" "text", "p_last_name" "text", "p_invitation_token" "text" DEFAULT NULL::"text") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
DECLARE
  v_user_id UUID;
  v_user JSONB;
  v_invitation_result JSONB;
  v_team_id UUID;
BEGIN
  -- Create the user
  v_user_id := extensions.uuid_generate_v4();
  
  -- Insert into auth.users
  INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    raw_app_meta_data,
    raw_user_meta_data,
    created_at,
    updated_at,
    last_sign_in_at
  )
  VALUES (
    v_user_id,
    (SELECT instance_id FROM auth.instances LIMIT 1),
    p_email,
    crypt(p_password, gen_salt('bf')),
    NOW(),
    '{"provider":"email","providers":["email"]}',
    jsonb_build_object(
      'first_name', p_first_name,
      'last_name', p_last_name,
      'role', 'service_provider'
    ),
    NOW(),
    NOW(),
    NOW()
  );
  
  -- Create the user profile
  INSERT INTO public.profiles (
    id,
    email,
    first_name,
    last_name,
    role,
    created_at,
    updated_at
  )
  VALUES (
    v_user_id,
    p_email,
    p_first_name,
    p_last_name,
    'service_provider',
    NOW(),
    NOW()
  );
  
  -- Create the service provider profile
  INSERT INTO public.service_providers (
    id,
    email,
    first_name,
    last_name,
    status
  )
  VALUES (
    v_user_id,
    p_email,
    p_first_name,
    p_last_name,
    'active'
  );
  
  -- Process invitation if provided
  IF p_invitation_token IS NOT NULL THEN
    -- Get the team ID from the invitation
    SELECT team_id INTO v_team_id
    FROM public.team_invitations
    WHERE token = p_invitation_token
    AND status = 'pending';
    
    IF v_team_id IS NOT NULL THEN
      -- Accept the invitation
      v_invitation_result := accept_invitation_and_add_member(p_invitation_token, v_user_id);
    END IF;
  END IF;
  
  -- Construct the user object
  v_user := jsonb_build_object(
    'id', v_user_id,
    'email', p_email,
    'user_metadata', jsonb_build_object(
      'first_name', p_first_name,
      'last_name', p_last_name,
      'role', 'service_provider'
    )
  );
  
  -- Return the result
  RETURN jsonb_build_object(
    'success', true,
    'user', v_user,
    'invitation', v_invitation_result
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$$;


ALTER FUNCTION "public"."register_service_provider"("p_email" "text", "p_password" "text", "p_first_name" "text", "p_last_name" "text", "p_invitation_token" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."remove_property_from_team_direct"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  -- Check if user owns the team
  WITH team_check AS (
    SELECT EXISTS (
      SELECT 1 FROM teams
      WHERE id = p_team_id AND owner_id = p_user_id
    ) AS is_owner
  ),
  -- Check if property is in team
  exists_check AS (
    SELECT EXISTS (
      SELECT 1 FROM team_properties
      WHERE property_id = p_property_id AND team_id = p_team_id
    ) AS exists_in_team
  ),
  -- Delete if all checks pass and property is in team
  delete_result AS (
    DELETE FROM team_properties
    WHERE team_id = p_team_id 
    AND property_id = p_property_id
    AND EXISTS (SELECT 1 FROM team_check WHERE is_owner)
    RETURNING true AS deleted
  ),
  -- Update inventory items
  update_result AS (
    UPDATE inventory_items
    SET team_id = NULL
    WHERE property_id = p_property_id
    AND team_id = p_team_id
    RETURNING true AS updated
  )
  -- Return true if deleted or didn't exist
  SELECT COALESCE(
    (SELECT deleted FROM delete_result),
    (SELECT NOT exists_in_team FROM exists_check),
    false
  );
$$;


ALTER FUNCTION "public"."remove_property_from_team_direct"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."remove_property_from_team_safe"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_is_owner boolean;
  v_exists boolean;
BEGIN
  -- Check if user owns the team
  SELECT EXISTS (
    SELECT 1 FROM teams
    WHERE id = p_team_id AND owner_id = p_user_id
  ) INTO v_is_owner;
  
  IF NOT v_is_owner THEN
    RAISE EXCEPTION 'User does not own this team';
  END IF;
  
  -- Check if property is in team
  SELECT EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id AND team_id = p_team_id
  ) INTO v_exists;
  
  IF NOT v_exists THEN
    RETURN true; -- Not in team, consider it a success
  END IF;
  
  -- Remove property from team
  DELETE FROM team_properties
  WHERE team_id = p_team_id AND property_id = p_property_id;
  
  -- Update inventory items for this property to remove the team_id
  UPDATE inventory_items
  SET team_id = NULL
  WHERE property_id = p_property_id
  AND team_id = p_team_id;
  
  RETURN true;
EXCEPTION
  WHEN others THEN
    RAISE;
END;
$$;


ALTER FUNCTION "public"."remove_property_from_team_safe"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."run_sql"("sql" "text") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  EXECUTE sql;
END;
$$;


ALTER FUNCTION "public"."run_sql"("sql" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."scheduled_ensure_profiles"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  PERFORM ensure_all_users_have_profiles();
END;
$$;


ALTER FUNCTION "public"."scheduled_ensure_profiles"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_damage_report_team_id"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    v_team_ids UUID[];
BEGIN
    -- Get all team_ids associated with this property
    SELECT array_agg(tp.team_id) INTO v_team_ids
    FROM team_properties tp
    WHERE tp.property_id = NEW.property_id;

    -- If there's exactly one team, set the team_id
    IF array_length(v_team_ids, 1) = 1 THEN
        NEW.team_id := v_team_ids[1];
    END IF;

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_damage_report_team_id"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_default_permissions"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Wrapped in exception handling to prevent registration failures
  BEGIN
    -- If a user is created or role changed to property_manager, set default permissions
    IF NEW.role = 'property_manager' THEN
      -- First, create a default team for the property manager if they don't have one
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM public.teams WHERE owner_id = NEW.id) THEN
          INSERT INTO public.teams (name, owner_id)
          VALUES (CONCAT(COALESCE(NEW.first_name, 'User'), '''s Team'), NEW.id);
          RAISE NOTICE 'Created default team for property manager %', NEW.id;
        END IF;
      EXCEPTION WHEN OTHERS THEN
        -- Log the error but don't block the trigger
        RAISE NOTICE 'Error creating default team: %', SQLERRM;
      END;
      
      -- Set default permissions for property managers
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'permission_type') THEN
          INSERT INTO public.user_permissions (user_id, permission, team_id)
          SELECT 
            NEW.id, 
            permission, 
            (SELECT id FROM public.teams WHERE owner_id = NEW.id LIMIT 1)
          FROM unnest(ARRAY[
            'manage_properties'::permission_type,
            'submit_damage_reports'::permission_type,
            'manage_inventory'::permission_type,
            'view_inventory'::permission_type,
            'manage_staff'::permission_type,
            'manage_service_providers'::permission_type,
            'view_reports'::permission_type,
            'edit_reports'::permission_type
          ]) AS permission
          ON CONFLICT (user_id, team_id, permission) DO NOTHING;
          RAISE NOTICE 'Set default permissions for property manager %', NEW.id;
        END IF;
      EXCEPTION WHEN OTHERS THEN
        -- Log the error but don't block the trigger
        RAISE NOTICE 'Error setting default permissions: %', SQLERRM;
      END;
    END IF;
  EXCEPTION WHEN OTHERS THEN
    -- Log the error but don't block the trigger
    RAISE NOTICE 'Error in set_default_permissions: %', SQLERRM;
  END;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_default_permissions"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_inventory_team_id"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- If property_id is set, find the team_id from team_properties
  IF NEW.property_id IS NOT NULL THEN
    -- Get the first team that has this property
    SELECT team_id INTO NEW.team_id
    FROM team_properties
    WHERE property_id = NEW.property_id
    LIMIT 1;
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_inventory_team_id"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_maintenance_task_team_id"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- If property_id is set, find the team_id from team_properties
  IF NEW.property_id IS NOT NULL THEN
    -- Get the first team that has this property
    SELECT team_id INTO NEW.team_id
    FROM team_properties
    WHERE property_id = NEW.property_id
    LIMIT 1;
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_maintenance_task_team_id"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_purchase_order_team_id"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- If property_id is set, find the team_id from team_properties
  IF NEW.property_id IS NOT NULL THEN
    -- Get the first team that has this property
    SELECT team_id INTO NEW.team_id
    FROM team_properties
    WHERE property_id = NEW.property_id
    LIMIT 1;
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_purchase_order_team_id"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_storage_policy"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Drop existing policies if they exist
  BEGIN
    EXECUTE 'DROP POLICY IF EXISTS "Allow public read access" ON storage.objects;';
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error dropping policy: %', SQLERRM;
  END;
  
  -- Create a policy to allow public read access to the damage-photos bucket
  BEGIN
    EXECUTE 'CREATE POLICY "Allow public read access" ON storage.objects FOR SELECT USING (bucket_id = ''damage-photos'');';
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error creating policy: %', SQLERRM;
  END;
  
  RETURN;
END;
$$;


ALTER FUNCTION "public"."set_storage_policy"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."setup_team_member_permissions"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
  declare
    v_role text;
  begin
    select role into v_role
    from profiles
    where id = NEW.user_id;

    if v_role = 'property_manager' then
      insert into user_permissions (user_id, team_id, permission, enabled)
      values
        (NEW.user_id, NEW.team_id, 'manage_properties', true),
        (NEW.user_id, NEW.team_id, 'manage_inventory', true),
        (NEW.user_id, NEW.team_id, 'manage_staff', true),
        (NEW.user_id, NEW.team_id, 'manage_service_providers', true);
    elsif v_role = 'service_provider' then
      insert into user_permissions (user_id, team_id, permission, enabled)
      values
        (NEW.user_id, NEW.team_id, 'view_inventory', true),
        (NEW.user_id, NEW.team_id, 'submit_damage_reports', true);
    end if;

    return NEW;
  end;
  $$;


ALTER FUNCTION "public"."setup_team_member_permissions"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."sync_inventory_team_ids"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    r RECORD;
BEGIN
    -- Log start of sync
    RAISE NOTICE 'Starting inventory team_id sync...';

    -- For each property that belongs to a team
    FOR r IN
        SELECT DISTINCT tp.property_id, tp.team_id
        FROM team_properties tp
    LOOP
        -- Update all inventory items for this property to have the team_id
        UPDATE inventory_items
        SET team_id = r.team_id
        WHERE property_id = r.property_id
        AND (team_id IS NULL OR team_id != r.team_id);

        -- Log each property update
        RAISE NOTICE 'Updated inventory items for property % with team %', r.property_id, r.team_id;
    END LOOP;

    -- Log completion
    RAISE NOTICE 'Inventory team_id sync completed';
END;
$$;


ALTER FUNCTION "public"."sync_inventory_team_ids"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_extension_token_last_used"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
begin
    update public.extension_api_tokens
    set last_used = now()
    where id = old.id;
    return new;
end;
$$;


ALTER FUNCTION "public"."update_extension_token_last_used"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_inventory_team_id_on_team_property_change"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- When a property is added to a team, update all inventory items for that property
  IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.team_id != NEW.team_id) THEN
    UPDATE inventory_items
    SET team_id = NEW.team_id
    WHERE property_id = NEW.property_id
    AND (team_id IS NULL OR team_id != NEW.team_id);

    RAISE NOTICE 'Updated inventory items for property % with team %', NEW.property_id, NEW.team_id;
  END IF;

  -- When a property is removed from a team, clear the team_id for inventory items
  IF TG_OP = 'DELETE' THEN
    UPDATE inventory_items
    SET team_id = NULL
    WHERE property_id = OLD.property_id
    AND team_id = OLD.team_id;

    RAISE NOTICE 'Cleared team_id for inventory items of property % from team %', OLD.property_id, OLD.team_id;
  END IF;

  RETURN NULL; -- for AFTER triggers
END;
$$;


ALTER FUNCTION "public"."update_inventory_team_id_on_team_property_change"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_modified_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Check if we're already inside the trigger to prevent recursion.
    IF current_setting('app.disable_update_modified', true) = 'true' THEN
        RETURN NEW;
    END IF;

    -- Set the session variable to disable the trigger temporarily.
    PERFORM set_config('app.disable_update_modified', 'true', true);

    -- Update the 'updated_at' column.
    NEW.updated_at = now();

    -- Reset the session variable (though it will be reset automatically at the end of the transaction).
    PERFORM set_config('app.disable_update_modified', 'false', true);

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_modified_column"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if the user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User with ID % does not exist', p_user_id;
  END IF;

  -- Update the user preferences
  UPDATE public.user_preferences
  SET 
    onboarding_state = p_onboarding_state,
    updated_at = NOW()
  WHERE user_id = p_user_id;

  -- If no rows were updated, the preferences don't exist yet, so create them
  IF NOT FOUND THEN
    INSERT INTO public.user_preferences (user_id, onboarding_state)
    VALUES (p_user_id, p_onboarding_state);
  END IF;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error updating user preferences: %', SQLERRM;
    RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."update_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."update_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") IS 'Updates user preferences with SECURITY DEFINER to bypass RLS.';



CREATE OR REPLACE FUNCTION "public"."user_can_access_team"("target_team_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Super admins and admins can access all teams
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team owners can access their teams
  IF EXISTS (
    SELECT 1 FROM teams
    WHERE id = target_team_id AND owner_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members can access their teams
  IF EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = target_team_id AND user_id = auth.uid() AND status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;


ALTER FUNCTION "public"."user_can_access_team"("target_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_can_access_team"("user_id" "uuid", "team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $_$
  SELECT EXISTS (
    SELECT 1 FROM public.team_members 
    WHERE team_id = $2 AND user_id = $1 AND status = 'active'
  );
$_$;


ALTER FUNCTION "public"."user_can_access_team"("user_id" "uuid", "team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_can_access_team_members"("target_team_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN user_can_access_team(target_team_id);
END;
$$;


ALTER FUNCTION "public"."user_can_access_team_members"("target_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_can_access_team_properties"("target_team_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN user_can_access_team(target_team_id);
END;
$$;


ALTER FUNCTION "public"."user_can_access_team_properties"("target_team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_can_access_team_sd"("user_id" "uuid", "team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $_$
  SELECT EXISTS (
    SELECT 1 
    FROM public.team_members 
    WHERE team_id = $2 AND user_id = $1 AND status = 'active'
  ) OR 
  EXISTS (
    SELECT 1
    FROM public.teams
    WHERE id = $2 AND owner_id = $1
  );
$_$;


ALTER FUNCTION "public"."user_can_access_team_sd"("user_id" "uuid", "team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_has_permission"("p_user_id" "uuid", "p_permission" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  has_permission BOOLEAN;
BEGIN
  -- Check if the user is a super admin or admin
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin')
  ) INTO has_permission;
  
  IF has_permission THEN
    RETURN TRUE;
  END IF;
  
  -- Check if the user has the specific permission
  SELECT EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = p_user_id AND permission = p_permission
  ) INTO has_permission;
  
  RETURN has_permission;
END;
$$;


ALTER FUNCTION "public"."user_has_permission"("p_user_id" "uuid", "p_permission" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_has_team_permission"("p_user_id" "uuid", "p_team_id" "uuid", "p_permission" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  has_permission BOOLEAN;
BEGIN
  -- Check if the user is a super admin or admin
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin')
  ) INTO has_permission;
  
  IF has_permission THEN
    RETURN TRUE;
  END IF;
  
  -- Check if the user is the team owner
  SELECT EXISTS (
    SELECT 1 FROM teams
    WHERE id = p_team_id AND owner_id = p_user_id
  ) INTO has_permission;
  
  IF has_permission THEN
    RETURN TRUE;
  END IF;
  
  -- Check if the user has the specific permission for the team
  SELECT EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = p_user_id AND team_id = p_team_id AND permission = p_permission
  ) INTO has_permission;
  
  RETURN has_permission;
END;
$$;


ALTER FUNCTION "public"."user_has_team_permission"("p_user_id" "uuid", "p_team_id" "uuid", "p_permission" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_is_team_member"("team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT EXISTS (
    SELECT 1 FROM team_members WHERE team_id = team_id AND user_id = auth.uid() AND status = 'active'
  );
$$;


ALTER FUNCTION "public"."user_is_team_member"("team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_is_team_owner"("team_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
begin
  return exists (
    select 1 from teams
    where id = team_id
    and owner_id = auth.uid()
  );
end;
$$;


ALTER FUNCTION "public"."user_is_team_owner"("team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_owns_team"("team_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT EXISTS (
    SELECT 1 FROM teams WHERE id = team_id AND owner_id = auth.uid()
  );
$$;


ALTER FUNCTION "public"."user_owns_team"("team_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."verify_backup"("backup_id" "text") RETURNS TABLE("is_valid" boolean, "tables_count" integer, "rows_count" integer, "issues" "text"[])
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  backup_record RECORD;
  backup_data JSONB;
  tables_json JSONB;
  tables_count INTEGER := 0;
  rows_count INTEGER := 0;
  issues TEXT[] := ARRAY[]::TEXT[];
BEGIN
  -- Get the backup record
  SELECT * INTO backup_record
  FROM backups
  WHERE id = backup_id;
  
  IF NOT FOUND THEN
    issues := array_append(issues, 'Backup not found');
    RETURN QUERY SELECT false, 0, 0, issues;
    RETURN;
  END IF;
  
  -- Check if backup is completed
  IF backup_record.status <> 'completed' THEN
    issues := array_append(issues, 'Backup is not completed');
    RETURN QUERY SELECT false, 0, 0, issues;
    RETURN;
  END IF;
  
  -- Get backup data
  backup_data := backup_record.backup_data;
  
  -- Check if backup data exists
  IF backup_data IS NULL THEN
    issues := array_append(issues, 'Backup data is missing');
    RETURN QUERY SELECT false, 0, 0, issues;
    RETURN;
  END IF;
  
  -- Check tables
  IF backup_record.includes_database THEN
    tables_json := backup_data->'tables';
    
    IF tables_json IS NULL OR jsonb_array_length(tables_json) = 0 THEN
      issues := array_append(issues, 'Database backup is empty');
    ELSE
      tables_count := jsonb_array_length(tables_json);
      
      -- Count rows
      FOR i IN 0..tables_count-1 LOOP
        IF tables_json->i->'data' IS NOT NULL THEN
          rows_count := rows_count + jsonb_array_length(tables_json->i->'data');
        END IF;
      END LOOP;
    END IF;
  END IF;
  
  -- Check storage
  IF backup_record.includes_storage AND (backup_data->'storage' IS NULL OR backup_data->'storage'->'buckets' IS NULL) THEN
    issues := array_append(issues, 'Storage backup is incomplete');
  END IF;
  
  -- Check auth
  IF backup_record.includes_auth AND backup_data->'auth' IS NULL THEN
    issues := array_append(issues, 'Auth backup is incomplete');
  END IF;
  
  -- Check edge functions
  IF backup_record.includes_edge_functions AND backup_data->'functions' IS NULL THEN
    issues := array_append(issues, 'Edge functions backup is incomplete');
  END IF;
  
  -- Return results
  RETURN QUERY SELECT 
    array_length(issues, 1) IS NULL OR array_length(issues, 1) = 0,
    tables_count,
    rows_count,
    issues;
END;
$$;


ALTER FUNCTION "public"."verify_backup"("backup_id" "text") OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."automation_queue" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "booking_id" "uuid" NOT NULL,
    "processed" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "processed_at" timestamp with time zone
);


ALTER TABLE "public"."automation_queue" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."automation_rules" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "trigger_type" "text" NOT NULL,
    "task_type" "text" NOT NULL,
    "time_offset" integer NOT NULL,
    "property_ids" "jsonb",
    "title" "text" NOT NULL,
    "description" "text",
    "severity" "text" NOT NULL,
    "assigned_to" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."automation_rules" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."backups" (
    "id" bigint NOT NULL,
    "status" "text" NOT NULL,
    "includes_database" boolean NOT NULL,
    "includes_storage" boolean NOT NULL,
    "includes_auth" boolean NOT NULL,
    "includes_edge_functions" boolean NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "schema" "jsonb",
    "backup_data" "jsonb"
);


ALTER TABLE "public"."backups" OWNER TO "postgres";


ALTER TABLE "public"."backups" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."backups_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."bookings" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "property_id" "uuid" NOT NULL,
    "check_in_date" "date" NOT NULL,
    "check_out_date" "date" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "user_id" "uuid" NOT NULL,
    CONSTRAINT "check_dates" CHECK (("check_out_date" >= "check_in_date"))
);


ALTER TABLE "public"."bookings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."collections" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."collections" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."damage_invoices" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "damage_report_id" "uuid" NOT NULL,
    "provider_id" "uuid",
    "invoice_number" "text",
    "total_amount" numeric(10,2),
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "issue_date" "date",
    "due_date" "date",
    "notes" "text",
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "file_path" "text",
    "file_name" "text",
    "file_url" "text"
);


ALTER TABLE "public"."damage_invoices" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."damage_notes" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "damage_report_id" "uuid" NOT NULL,
    "content" "text" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "private" boolean DEFAULT false NOT NULL,
    "created_by" "text"
);


ALTER TABLE "public"."damage_notes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."damage_photos" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "damage_report_id" "uuid" NOT NULL,
    "file_name" "text" NOT NULL,
    "file_path" "text" NOT NULL,
    "caption" "text",
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "media_type" "text" DEFAULT 'image'::"text"
);


ALTER TABLE "public"."damage_photos" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."maintenance_providers" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "specialty" "text",
    "phone" "text",
    "email" "text",
    "notes" "text",
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."maintenance_providers" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."damage_reports_with_property" AS
 SELECT "dr"."id",
    "dr"."title",
    "dr"."description",
    "dr"."status",
    "dr"."property_id",
    "p"."name" AS "property_name",
    "dr"."user_id",
    "u"."email" AS "user_email",
    "dr"."provider_id",
    "mp"."name" AS "provider_name",
    "dr"."created_at",
    "dr"."updated_at",
    "dr"."platform",
    "dr"."team_id",
    "p"."user_id" AS "property_owner_id"
   FROM ((("public"."damage_reports" "dr"
     JOIN "public"."properties" "p" ON (("dr"."property_id" = "p"."id")))
     LEFT JOIN "auth"."users" "u" ON (("dr"."user_id" = "u"."id")))
     LEFT JOIN "public"."maintenance_providers" "mp" ON (("dr"."provider_id" = "mp"."id")));


ALTER TABLE "public"."damage_reports_with_property" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."extension_api_tokens" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "token_hash" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "last_used" timestamp with time zone,
    "revoked_at" timestamp with time zone,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb"
);


ALTER TABLE "public"."extension_api_tokens" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inventory_items" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "property_id" "uuid" NOT NULL,
    "collection_id" "uuid",
    "quantity" integer DEFAULT 0 NOT NULL,
    "min_quantity" integer DEFAULT 1 NOT NULL,
    "price" numeric(10,2),
    "amazon_url" "text",
    "walmart_url" "text",
    "asin" "text",
    "walmart_item_id" "text",
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "collection" "text" NOT NULL,
    "image_url" "text",
    "team_id" "uuid"
);


ALTER TABLE "public"."inventory_items" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."invitations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "email" "text" NOT NULL,
    "team_id" "uuid" NOT NULL,
    "invited_by" "uuid" NOT NULL,
    "role" "public"."user_role" NOT NULL,
    "token" "text" NOT NULL,
    "expires_at" timestamp with time zone NOT NULL,
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."invitations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."invoice_items" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "invoice_id" "uuid" NOT NULL,
    "description" "text" NOT NULL,
    "quantity" integer NOT NULL,
    "unit_price" numeric NOT NULL,
    "amount" numeric NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."invoice_items" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."maintenance_requests" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "property_id" "uuid" NOT NULL,
    "title" "text" NOT NULL,
    "description" "text" NOT NULL,
    "status" "text" DEFAULT 'open'::"text" NOT NULL,
    "priority" "text" DEFAULT 'medium'::"text" NOT NULL,
    "provider_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "user_id" "uuid" NOT NULL
);


ALTER TABLE "public"."maintenance_requests" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."property_documents" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "property_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "title" "text" NOT NULL,
    "content" "text" NOT NULL,
    "is_private" boolean DEFAULT false NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."property_documents" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."property_files" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "property_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "filename" "text" NOT NULL,
    "file_path" "text" NOT NULL,
    "file_type" "text" NOT NULL,
    "file_size" integer NOT NULL,
    "is_private" boolean DEFAULT false NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "display_name" "text",
    "caption" "text"
);


ALTER TABLE "public"."property_files" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."purchase_order_items" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "purchase_order_id" "uuid" NOT NULL,
    "inventory_item_id" "uuid",
    "item_name" "text" NOT NULL,
    "quantity" integer NOT NULL,
    "price" numeric(10,2),
    "amazon_url" "text",
    "walmart_url" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."purchase_order_items" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."purchase_orders" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "property_id" "uuid" NOT NULL,
    "status" "public"."po_status" DEFAULT 'pending'::"public"."po_status" NOT NULL,
    "total_price" numeric(10,2),
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "notes" "text",
    "is_archived" boolean DEFAULT false NOT NULL,
    "team_id" "uuid"
);


ALTER TABLE "public"."purchase_orders" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."service_providers" (
    "id" "uuid" NOT NULL,
    "email" "text" NOT NULL,
    "first_name" "text",
    "last_name" "text",
    "status" "text" DEFAULT 'active'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."service_providers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."team_invitations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "team_id" "uuid" NOT NULL,
    "email" "text" NOT NULL,
    "invited_by" "uuid" NOT NULL,
    "role" "public"."user_role" NOT NULL,
    "token" "text" NOT NULL,
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "expires_at" timestamp with time zone NOT NULL,
    "team_name" "text"
);


ALTER TABLE "public"."team_invitations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."team_properties" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "team_id" "uuid" NOT NULL,
    "property_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."team_properties" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."teams" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "owner_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);

ALTER TABLE ONLY "public"."teams" FORCE ROW LEVEL SECURITY;


ALTER TABLE "public"."teams" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_permissions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "team_id" "uuid",
    "permission" "public"."permission_type" NOT NULL,
    "enabled" boolean DEFAULT true NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);

ALTER TABLE ONLY "public"."user_permissions" FORCE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_permissions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_preferences" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "onboarding_state" "jsonb" DEFAULT '{"hasSeenDamagesTutorial": false, "hasSeenDashboardTutorial": false, "hasSeenInventoryTutorial": false, "hasSeenPropertiesTutorial": false, "hasSeenMaintenanceTutorial": false}'::"jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."user_preferences" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_settings" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "dark_mode" boolean DEFAULT false NOT NULL,
    "compact_mode" boolean DEFAULT false NOT NULL,
    "animations" boolean DEFAULT true NOT NULL,
    "email_notifications" boolean DEFAULT true NOT NULL,
    "push_notifications" boolean DEFAULT false NOT NULL,
    "weekly_summary" boolean DEFAULT true NOT NULL,
    "inventory_alerts" boolean DEFAULT true NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."user_settings" OWNER TO "postgres";


ALTER TABLE ONLY "public"."automation_queue"
    ADD CONSTRAINT "automation_queue_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."automation_rules"
    ADD CONSTRAINT "automation_rules_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."backups"
    ADD CONSTRAINT "backups_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."bookings"
    ADD CONSTRAINT "bookings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."collections"
    ADD CONSTRAINT "collections_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."damage_invoices"
    ADD CONSTRAINT "damage_invoices_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."damage_notes"
    ADD CONSTRAINT "damage_notes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."damage_photos"
    ADD CONSTRAINT "damage_photos_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."damage_reports"
    ADD CONSTRAINT "damage_reports_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."damage_reports"
    ADD CONSTRAINT "damage_reports_property_id_title_key" UNIQUE ("property_id", "title");



ALTER TABLE ONLY "public"."damage_reports"
    ADD CONSTRAINT "damage_reports_title_property_id_key" UNIQUE ("title", "property_id");



ALTER TABLE ONLY "public"."extension_api_tokens"
    ADD CONSTRAINT "extension_api_tokens_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."invitations"
    ADD CONSTRAINT "invitations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."invitations"
    ADD CONSTRAINT "invitations_token_key" UNIQUE ("token");



ALTER TABLE ONLY "public"."invoice_items"
    ADD CONSTRAINT "invoice_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."maintenance_providers"
    ADD CONSTRAINT "maintenance_providers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."maintenance_requests"
    ADD CONSTRAINT "maintenance_requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."maintenance_tasks"
    ADD CONSTRAINT "maintenance_tasks_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."properties"
    ADD CONSTRAINT "properties_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."property_documents"
    ADD CONSTRAINT "property_documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."property_files"
    ADD CONSTRAINT "property_files_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."purchase_order_items"
    ADD CONSTRAINT "purchase_order_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."purchase_orders"
    ADD CONSTRAINT "purchase_orders_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service_providers"
    ADD CONSTRAINT "service_providers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."team_invitations"
    ADD CONSTRAINT "team_invitations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."team_invitations"
    ADD CONSTRAINT "team_invitations_token_key" UNIQUE ("token");



ALTER TABLE ONLY "public"."team_members"
    ADD CONSTRAINT "team_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."team_members"
    ADD CONSTRAINT "team_members_team_id_user_id_key" UNIQUE ("team_id", "user_id");



ALTER TABLE ONLY "public"."team_properties"
    ADD CONSTRAINT "team_properties_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."team_properties"
    ADD CONSTRAINT "team_properties_team_id_property_id_key" UNIQUE ("team_id", "property_id");



ALTER TABLE ONLY "public"."team_properties"
    ADD CONSTRAINT "team_properties_team_id_property_id_unique" UNIQUE ("team_id", "property_id");



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_permissions"
    ADD CONSTRAINT "user_permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_permissions"
    ADD CONSTRAINT "user_permissions_user_id_team_id_permission_key" UNIQUE ("user_id", "team_id", "permission");



ALTER TABLE ONLY "public"."user_preferences"
    ADD CONSTRAINT "user_preferences_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_preferences"
    ADD CONSTRAINT "user_preferences_user_id_key" UNIQUE ("user_id");



ALTER TABLE ONLY "public"."user_settings"
    ADD CONSTRAINT "user_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_settings"
    ADD CONSTRAINT "user_settings_user_id_key" UNIQUE ("user_id");



CREATE INDEX "damage_reports_property_id_idx" ON "public"."damage_reports" USING "btree" ("property_id");



CREATE INDEX "damage_reports_team_id_idx" ON "public"."damage_reports" USING "btree" ("team_id");



CREATE INDEX "idx_damage_photos_media_type" ON "public"."damage_photos" USING "btree" ("media_type");



CREATE UNIQUE INDEX "idx_extension_api_tokens_token_hash" ON "public"."extension_api_tokens" USING "btree" ("token_hash");



CREATE INDEX "idx_extension_api_tokens_user_id" ON "public"."extension_api_tokens" USING "btree" ("user_id");



CREATE INDEX "idx_maintenance_tasks_email_notifications" ON "public"."maintenance_tasks" USING "btree" ("is_recurring", "email_notification_sent", "created_at") WHERE (("is_recurring" = true) AND ("email_notification_sent" = false));



CREATE INDEX "idx_maintenance_tasks_next_due" ON "public"."maintenance_tasks" USING "btree" ("next_due_date") WHERE ("next_due_date" IS NOT NULL);



CREATE INDEX "idx_maintenance_tasks_parent" ON "public"."maintenance_tasks" USING "btree" ("parent_task_id") WHERE ("parent_task_id" IS NOT NULL);



CREATE INDEX "idx_maintenance_tasks_recurring" ON "public"."maintenance_tasks" USING "btree" ("is_recurring") WHERE ("is_recurring" = true);



CREATE INDEX "idx_team_invitations_email" ON "public"."team_invitations" USING "btree" ("email");



CREATE INDEX "idx_team_invitations_status" ON "public"."team_invitations" USING "btree" ("status");



CREATE INDEX "idx_team_invitations_team" ON "public"."team_invitations" USING "btree" ("team_id");



CREATE INDEX "idx_team_invitations_token" ON "public"."team_invitations" USING "btree" ("token");



CREATE INDEX "idx_team_members_composite" ON "public"."team_members" USING "btree" ("team_id", "user_id", "status");



CREATE INDEX "idx_team_members_status" ON "public"."team_members" USING "btree" ("status");



CREATE INDEX "idx_team_members_team_id" ON "public"."team_members" USING "btree" ("team_id");



CREATE INDEX "idx_team_members_user_id" ON "public"."team_members" USING "btree" ("user_id");



CREATE INDEX "idx_teams_owner_id" ON "public"."teams" USING "btree" ("owner_id");



CREATE INDEX "idx_user_permissions_composite" ON "public"."user_permissions" USING "btree" ("user_id", "team_id", "permission");



CREATE INDEX "idx_user_permissions_permission" ON "public"."user_permissions" USING "btree" ("permission");



CREATE INDEX "idx_user_permissions_team_id" ON "public"."user_permissions" USING "btree" ("team_id");



CREATE INDEX "idx_user_permissions_user_id" ON "public"."user_permissions" USING "btree" ("user_id");



CREATE INDEX "inventory_items_team_id_idx" ON "public"."inventory_items" USING "btree" ("team_id");



CREATE INDEX "maintenance_tasks_property_id_idx" ON "public"."maintenance_tasks" USING "btree" ("property_id");



COMMENT ON INDEX "public"."maintenance_tasks_property_id_idx" IS 'Improves query performance when filtering maintenance tasks by property_id, especially for team members viewing tasks for their team properties';



CREATE INDEX "maintenance_tasks_team_id_idx" ON "public"."maintenance_tasks" USING "btree" ("team_id");



COMMENT ON INDEX "public"."maintenance_tasks_team_id_idx" IS 'Improves query performance when filtering maintenance tasks by team_id';



CREATE INDEX "purchase_orders_team_id_idx" ON "public"."purchase_orders" USING "btree" ("team_id");



CREATE OR REPLACE TRIGGER "booking_automation_trigger" AFTER INSERT OR UPDATE OF "check_in_date", "check_out_date" ON "public"."bookings" FOR EACH ROW EXECUTE FUNCTION "public"."handle_booking_automation"();



CREATE OR REPLACE TRIGGER "check_damage_report_duplicate_trigger" BEFORE INSERT OR UPDATE ON "public"."damage_reports" FOR EACH ROW EXECUTE FUNCTION "public"."check_damage_report_duplicate"();



CREATE OR REPLACE TRIGGER "check_duplicate_property_name_trigger" BEFORE INSERT OR UPDATE ON "public"."properties" FOR EACH ROW EXECUTE FUNCTION "public"."check_duplicate_property_name"();



CREATE OR REPLACE TRIGGER "check_team_duplicate_property_name_trigger" BEFORE INSERT ON "public"."team_properties" FOR EACH ROW EXECUTE FUNCTION "public"."check_team_duplicate_property_name"();



CREATE OR REPLACE TRIGGER "create_maintenance_provider_trigger" AFTER INSERT OR UPDATE OF "role" ON "public"."profiles" FOR EACH ROW EXECUTE FUNCTION "public"."create_maintenance_provider_for_service_provider"();



CREATE OR REPLACE TRIGGER "create_maintenance_provider_trigger_team" AFTER INSERT OR UPDATE OF "status" ON "public"."team_members" FOR EACH ROW WHEN (("new"."status" = 'active'::"text")) EXECUTE FUNCTION "public"."create_maintenance_provider_for_team_member"();



CREATE OR REPLACE TRIGGER "ensure_team_property_access_trigger" AFTER INSERT OR DELETE ON "public"."team_properties" FOR EACH ROW EXECUTE FUNCTION "public"."ensure_team_property_access"();



CREATE OR REPLACE TRIGGER "prevent_duplicate_property_names_in_team_trigger" BEFORE INSERT OR UPDATE OF "name" ON "public"."properties" FOR EACH ROW EXECUTE FUNCTION "public"."prevent_duplicate_property_names_in_team"();



CREATE OR REPLACE TRIGGER "set_bookings_updated_at" BEFORE UPDATE ON "public"."bookings" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "set_damage_report_team_id_trigger" BEFORE INSERT OR UPDATE OF "property_id" ON "public"."damage_reports" FOR EACH ROW EXECUTE FUNCTION "public"."set_damage_report_team_id"();



CREATE OR REPLACE TRIGGER "set_inventory_items_updated_at" BEFORE UPDATE ON "public"."inventory_items" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "set_inventory_team_id_trigger" BEFORE INSERT OR UPDATE ON "public"."inventory_items" FOR EACH ROW EXECUTE FUNCTION "public"."set_inventory_team_id"();



CREATE OR REPLACE TRIGGER "set_invitations_updated_at" BEFORE UPDATE ON "public"."invitations" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "set_maintenance_providers_updated_at" BEFORE UPDATE ON "public"."maintenance_providers" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "set_maintenance_task_team_id_trigger" BEFORE INSERT OR UPDATE ON "public"."maintenance_tasks" FOR EACH ROW EXECUTE FUNCTION "public"."set_maintenance_task_team_id"();



CREATE OR REPLACE TRIGGER "set_maintenance_tasks_updated_at" BEFORE UPDATE ON "public"."maintenance_tasks" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "set_properties_updated_at" BEFORE UPDATE ON "public"."properties" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "set_purchase_order_team_id_trigger" BEFORE INSERT OR UPDATE ON "public"."purchase_orders" FOR EACH ROW EXECUTE FUNCTION "public"."set_purchase_order_team_id"();



CREATE OR REPLACE TRIGGER "set_purchase_orders_updated_at" BEFORE UPDATE ON "public"."purchase_orders" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "set_team_members_updated_at" BEFORE UPDATE ON "public"."team_members" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "set_teams_updated_at" BEFORE UPDATE ON "public"."teams" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "set_user_default_permissions" AFTER INSERT OR UPDATE OF "role" ON "public"."profiles" FOR EACH ROW EXECUTE FUNCTION "public"."set_default_permissions"();



CREATE OR REPLACE TRIGGER "set_user_permissions_updated_at" BEFORE UPDATE ON "public"."user_permissions" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "team_member_permissions_trigger" AFTER INSERT ON "public"."team_members" FOR EACH ROW EXECUTE FUNCTION "public"."setup_team_member_permissions"();



CREATE OR REPLACE TRIGGER "trigger_task_completion" BEFORE UPDATE ON "public"."maintenance_tasks" FOR EACH ROW EXECUTE FUNCTION "public"."handle_task_completion"();



CREATE OR REPLACE TRIGGER "update_damage_invoices_modtime" BEFORE UPDATE ON "public"."damage_invoices" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_damage_notes_modtime" BEFORE UPDATE ON "public"."damage_notes" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_damage_photos_modtime" BEFORE UPDATE ON "public"."damage_photos" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_damage_reports_modtime" BEFORE UPDATE ON "public"."damage_reports" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_extension_token_last_used" AFTER UPDATE ON "public"."extension_api_tokens" FOR EACH ROW WHEN ((("old"."last_used" IS NULL) OR ("old"."last_used" < ("now"() - '01:00:00'::interval)))) EXECUTE FUNCTION "public"."update_extension_token_last_used"();



CREATE OR REPLACE TRIGGER "update_inventory_items_modtime" BEFORE UPDATE ON "public"."inventory_items" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_inventory_team_id_trigger" AFTER INSERT OR DELETE OR UPDATE ON "public"."team_properties" FOR EACH ROW EXECUTE FUNCTION "public"."update_inventory_team_id_on_team_property_change"();



CREATE OR REPLACE TRIGGER "update_invoice_items_modtime" BEFORE UPDATE ON "public"."invoice_items" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_maintenance_providers_modtime" BEFORE UPDATE ON "public"."maintenance_providers" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_maintenance_requests_modtime" BEFORE UPDATE ON "public"."maintenance_requests" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_profiles_modtime" BEFORE UPDATE ON "public"."profiles" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_properties_modtime" BEFORE UPDATE ON "public"."properties" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_purchase_orders_modtime" BEFORE UPDATE ON "public"."purchase_orders" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_user_preferences_modtime" BEFORE UPDATE ON "public"."user_preferences" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_user_settings_modtime" BEFORE UPDATE ON "public"."user_settings" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



ALTER TABLE ONLY "public"."automation_queue"
    ADD CONSTRAINT "automation_queue_booking_id_fkey" FOREIGN KEY ("booking_id") REFERENCES "public"."bookings"("id");



ALTER TABLE ONLY "public"."automation_rules"
    ADD CONSTRAINT "automation_rules_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."automation_rules"
    ADD CONSTRAINT "automation_rules_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."bookings"
    ADD CONSTRAINT "bookings_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id");



ALTER TABLE ONLY "public"."collections"
    ADD CONSTRAINT "collections_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."damage_invoices"
    ADD CONSTRAINT "damage_invoices_damage_report_id_fkey" FOREIGN KEY ("damage_report_id") REFERENCES "public"."damage_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."damage_invoices"
    ADD CONSTRAINT "damage_invoices_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."maintenance_providers"("id");



ALTER TABLE ONLY "public"."damage_invoices"
    ADD CONSTRAINT "damage_invoices_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."damage_notes"
    ADD CONSTRAINT "damage_notes_damage_report_id_fkey" FOREIGN KEY ("damage_report_id") REFERENCES "public"."damage_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."damage_notes"
    ADD CONSTRAINT "damage_notes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."damage_photos"
    ADD CONSTRAINT "damage_photos_damage_report_id_fkey" FOREIGN KEY ("damage_report_id") REFERENCES "public"."damage_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."damage_photos"
    ADD CONSTRAINT "damage_photos_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."damage_reports"
    ADD CONSTRAINT "damage_reports_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."damage_reports"
    ADD CONSTRAINT "damage_reports_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."maintenance_providers"("id");



ALTER TABLE ONLY "public"."damage_reports"
    ADD CONSTRAINT "damage_reports_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id");



ALTER TABLE ONLY "public"."damage_reports"
    ADD CONSTRAINT "damage_reports_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."extension_api_tokens"
    ADD CONSTRAINT "extension_api_tokens_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_collection_id_fkey" FOREIGN KEY ("collection_id") REFERENCES "public"."collections"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id");



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invitations"
    ADD CONSTRAINT "invitations_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."invitations"
    ADD CONSTRAINT "invitations_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invoice_items"
    ADD CONSTRAINT "invoice_items_invoice_id_fkey" FOREIGN KEY ("invoice_id") REFERENCES "public"."damage_invoices"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."maintenance_providers"
    ADD CONSTRAINT "maintenance_providers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."maintenance_requests"
    ADD CONSTRAINT "maintenance_requests_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."maintenance_requests"
    ADD CONSTRAINT "maintenance_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."maintenance_tasks"
    ADD CONSTRAINT "maintenance_tasks_parent_task_id_fkey" FOREIGN KEY ("parent_task_id") REFERENCES "public"."maintenance_tasks"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."maintenance_tasks"
    ADD CONSTRAINT "maintenance_tasks_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id");



ALTER TABLE ONLY "public"."maintenance_tasks"
    ADD CONSTRAINT "maintenance_tasks_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id");



ALTER TABLE ONLY "public"."maintenance_tasks"
    ADD CONSTRAINT "maintenance_tasks_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."properties"
    ADD CONSTRAINT "properties_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id");



ALTER TABLE ONLY "public"."properties"
    ADD CONSTRAINT "properties_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."property_documents"
    ADD CONSTRAINT "property_documents_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."property_documents"
    ADD CONSTRAINT "property_documents_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."property_files"
    ADD CONSTRAINT "property_files_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."property_files"
    ADD CONSTRAINT "property_files_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."purchase_order_items"
    ADD CONSTRAINT "purchase_order_items_inventory_item_id_fkey" FOREIGN KEY ("inventory_item_id") REFERENCES "public"."inventory_items"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."purchase_order_items"
    ADD CONSTRAINT "purchase_order_items_purchase_order_id_fkey" FOREIGN KEY ("purchase_order_id") REFERENCES "public"."purchase_orders"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."purchase_orders"
    ADD CONSTRAINT "purchase_orders_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."purchase_orders"
    ADD CONSTRAINT "purchase_orders_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id");



ALTER TABLE ONLY "public"."purchase_orders"
    ADD CONSTRAINT "purchase_orders_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."team_invitations"
    ADD CONSTRAINT "team_invitations_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."team_invitations"
    ADD CONSTRAINT "team_invitations_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."team_members"
    ADD CONSTRAINT "team_members_added_by_fkey" FOREIGN KEY ("added_by") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."team_members"
    ADD CONSTRAINT "team_members_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."team_members"
    ADD CONSTRAINT "team_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."team_properties"
    ADD CONSTRAINT "team_properties_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."team_properties"
    ADD CONSTRAINT "team_properties_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_permissions"
    ADD CONSTRAINT "user_permissions_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_permissions"
    ADD CONSTRAINT "user_permissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_preferences"
    ADD CONSTRAINT "user_preferences_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."user_settings"
    ADD CONSTRAINT "user_settings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Authenticated users can delete their own inventory items" ON "public"."inventory_items" FOR DELETE TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Authenticated users can insert their own inventory items" ON "public"."inventory_items" FOR INSERT TO "authenticated" WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Authenticated users can update their own inventory items" ON "public"."inventory_items" FOR UPDATE TO "authenticated" USING (("auth"."uid"() = "user_id")) WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Damage reports are insertable by authenticated users" ON "public"."damage_reports" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Damage reports are viewable by owners and team members" ON "public"."damage_reports" FOR SELECT USING ("public"."has_damage_report_access"("id"));



CREATE POLICY "Inventory items are viewable by owners and team members" ON "public"."inventory_items" FOR SELECT USING ("public"."has_inventory_item_access"("id"));



CREATE POLICY "Maintenance providers are manageable by owners and admins" ON "public"."maintenance_providers" USING ((("user_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role"))))))) WITH CHECK ((("user_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Maintenance providers are viewable by owners and admins" ON "public"."maintenance_providers" FOR SELECT USING ((("user_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Maintenance requests are manageable by authorized users" ON "public"."maintenance_requests" USING (((EXISTS ( SELECT 1
   FROM "public"."properties" "p"
  WHERE (("p"."id" = "maintenance_requests"."property_id") AND ("p"."user_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM (("public"."team_properties" "tp"
     JOIN "public"."team_members" "tm" ON (("tp"."team_id" = "tm"."team_id")))
     JOIN "public"."user_permissions" "up" ON ((("tm"."user_id" = "up"."user_id") AND ("tm"."team_id" = "up"."team_id"))))
  WHERE (("tp"."property_id" = "maintenance_requests"."property_id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text") AND ("up"."permission" = 'manage_properties'::"public"."permission_type") AND ("up"."enabled" = true)))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role"))))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."properties" "p"
  WHERE (("p"."id" = "maintenance_requests"."property_id") AND ("p"."user_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM (("public"."team_properties" "tp"
     JOIN "public"."team_members" "tm" ON (("tp"."team_id" = "tm"."team_id")))
     JOIN "public"."user_permissions" "up" ON ((("tm"."user_id" = "up"."user_id") AND ("tm"."team_id" = "up"."team_id"))))
  WHERE (("tp"."property_id" = "maintenance_requests"."property_id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text") AND ("up"."permission" = 'manage_properties'::"public"."permission_type") AND ("up"."enabled" = true)))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Maintenance requests are viewable by authorized users" ON "public"."maintenance_requests" FOR SELECT USING (((EXISTS ( SELECT 1
   FROM "public"."properties" "p"
  WHERE (("p"."id" = "maintenance_requests"."property_id") AND ("p"."user_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM ("public"."team_properties" "tp"
     JOIN "public"."team_members" "tm" ON (("tp"."team_id" = "tm"."team_id")))
  WHERE (("tp"."property_id" = "maintenance_requests"."property_id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text")))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Maintenance tasks are deletable by authorized users" ON "public"."maintenance_tasks" FOR DELETE USING (((EXISTS ( SELECT 1
   FROM "public"."properties" "p"
  WHERE (("p"."id" = "maintenance_tasks"."property_id") AND ("p"."user_id" = "auth"."uid"())))) OR (("team_id" IS NOT NULL) AND (EXISTS ( SELECT 1
   FROM ("public"."team_members" "tm"
     JOIN "public"."user_permissions" "up" ON ((("tm"."user_id" = "up"."user_id") AND ("tm"."team_id" = "up"."team_id"))))
  WHERE (("tm"."team_id" = "maintenance_tasks"."team_id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text") AND ("up"."permission" = 'manage_properties'::"public"."permission_type") AND ("up"."enabled" = true))))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Maintenance tasks are deletable by owners and team members" ON "public"."maintenance_tasks" FOR DELETE USING ("public"."has_maintenance_task_access"("id"));



CREATE POLICY "Maintenance tasks are insertable by authenticated users" ON "public"."maintenance_tasks" FOR INSERT WITH CHECK (("auth"."uid"() IS NOT NULL));



CREATE POLICY "Maintenance tasks are insertable by authorized users" ON "public"."maintenance_tasks" FOR INSERT WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."properties" "p"
  WHERE (("p"."id" = "maintenance_tasks"."property_id") AND ("p"."user_id" = "auth"."uid"())))) OR (("team_id" IS NOT NULL) AND (EXISTS ( SELECT 1
   FROM ("public"."team_members" "tm"
     JOIN "public"."user_permissions" "up" ON ((("tm"."user_id" = "up"."user_id") AND ("tm"."team_id" = "up"."team_id"))))
  WHERE (("tm"."team_id" = "maintenance_tasks"."team_id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text") AND ("up"."permission" = 'manage_properties'::"public"."permission_type") AND ("up"."enabled" = true))))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Maintenance tasks are updatable by authorized users" ON "public"."maintenance_tasks" FOR UPDATE USING (((EXISTS ( SELECT 1
   FROM "public"."properties" "p"
  WHERE (("p"."id" = "maintenance_tasks"."property_id") AND ("p"."user_id" = "auth"."uid"())))) OR (("team_id" IS NOT NULL) AND (EXISTS ( SELECT 1
   FROM ("public"."team_members" "tm"
     JOIN "public"."user_permissions" "up" ON ((("tm"."user_id" = "up"."user_id") AND ("tm"."team_id" = "up"."team_id"))))
  WHERE (("tm"."team_id" = "maintenance_tasks"."team_id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text") AND ("up"."permission" = 'manage_properties'::"public"."permission_type") AND ("up"."enabled" = true))))) OR ("provider_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role"))))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."properties" "p"
  WHERE (("p"."id" = "maintenance_tasks"."property_id") AND ("p"."user_id" = "auth"."uid"())))) OR (("team_id" IS NOT NULL) AND (EXISTS ( SELECT 1
   FROM ("public"."team_members" "tm"
     JOIN "public"."user_permissions" "up" ON ((("tm"."user_id" = "up"."user_id") AND ("tm"."team_id" = "up"."team_id"))))
  WHERE (("tm"."team_id" = "maintenance_tasks"."team_id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text") AND ("up"."permission" = 'manage_properties'::"public"."permission_type") AND ("up"."enabled" = true))))) OR ("provider_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Maintenance tasks are updatable by owners and team members" ON "public"."maintenance_tasks" FOR UPDATE USING ("public"."has_maintenance_task_access"("id")) WITH CHECK ("public"."has_maintenance_task_access"("id"));



CREATE POLICY "Maintenance tasks are viewable by authorized users" ON "public"."maintenance_tasks" FOR SELECT USING (((EXISTS ( SELECT 1
   FROM "public"."properties" "p"
  WHERE (("p"."id" = "maintenance_tasks"."property_id") AND ("p"."user_id" = "auth"."uid"())))) OR (("team_id" IS NOT NULL) AND (EXISTS ( SELECT 1
   FROM "public"."team_members" "tm"
  WHERE (("tm"."team_id" = "maintenance_tasks"."team_id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text"))))) OR ("provider_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Maintenance tasks are viewable by owners and team members" ON "public"."maintenance_tasks" FOR SELECT USING ("public"."has_maintenance_task_access"("id"));



CREATE POLICY "Properties are deletable by owners and admins" ON "public"."properties" FOR DELETE USING ((("auth"."uid"() = "user_id") OR (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND (("profiles"."is_super_admin" = true) OR ("profiles"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Properties are editable by owners directly" ON "public"."properties" FOR UPDATE USING (("auth"."uid"() = "user_id")) WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Properties are insertable by authenticated users" ON "public"."properties" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Properties are updatable by team members with permission" ON "public"."properties" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM (("public"."team_properties" "tp"
     JOIN "public"."team_members" "tm" ON (("tp"."team_id" = "tm"."team_id")))
     JOIN "public"."user_permissions" "up" ON ((("tm"."user_id" = "up"."user_id") AND ("tm"."team_id" = "up"."team_id"))))
  WHERE (("tp"."property_id" = "properties"."id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text") AND ("up"."permission" = 'manage_properties'::"public"."permission_type") AND ("up"."enabled" = true))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM (("public"."team_properties" "tp"
     JOIN "public"."team_members" "tm" ON (("tp"."team_id" = "tm"."team_id")))
     JOIN "public"."user_permissions" "up" ON ((("tm"."user_id" = "up"."user_id") AND ("tm"."team_id" = "up"."team_id"))))
  WHERE (("tp"."property_id" = "properties"."id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text") AND ("up"."permission" = 'manage_properties'::"public"."permission_type") AND ("up"."enabled" = true)))));



CREATE POLICY "Properties are viewable by admins directly" ON "public"."properties" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND (("profiles"."is_super_admin" = true) OR ("profiles"."role" = 'admin'::"public"."user_role"))))));



CREATE POLICY "Properties are viewable by owners directly" ON "public"."properties" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Properties are viewable by team members" ON "public"."properties" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM ("public"."team_properties" "tp"
     JOIN "public"."team_members" "tm" ON (("tp"."team_id" = "tm"."team_id")))
  WHERE (("tp"."property_id" = "properties"."id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text")))));



CREATE POLICY "Property owners can delete any document" ON "public"."property_documents" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."properties"
  WHERE (("properties"."id" = "property_documents"."property_id") AND ("properties"."user_id" = "auth"."uid"())))));



CREATE POLICY "Property owners can delete any file" ON "public"."property_files" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."properties"
  WHERE (("properties"."id" = "property_files"."property_id") AND ("properties"."user_id" = "auth"."uid"())))));



CREATE POLICY "Property owners can update any document" ON "public"."property_documents" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."properties"
  WHERE (("properties"."id" = "property_documents"."property_id") AND ("properties"."user_id" = "auth"."uid"())))));



CREATE POLICY "Property owners can update any file" ON "public"."property_files" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."properties"
  WHERE (("properties"."id" = "property_files"."property_id") AND ("properties"."user_id" = "auth"."uid"())))));



CREATE POLICY "Team members are deletable by team owners, admins, and self" ON "public"."team_members" FOR DELETE USING (((EXISTS ( SELECT 1
   FROM "public"."teams" "t"
  WHERE (("t"."id" = "team_members"."team_id") AND ("t"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role"))))) OR ("user_id" = "auth"."uid"())));



CREATE POLICY "Team members are insertable by team owners and admins" ON "public"."team_members" FOR INSERT WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."teams" "t"
  WHERE (("t"."id" = "team_members"."team_id") AND ("t"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Team members are readable for policy evaluation" ON "public"."team_members" FOR SELECT USING (true);



CREATE POLICY "Team members are updatable by team owners and admins" ON "public"."team_members" FOR UPDATE USING (((EXISTS ( SELECT 1
   FROM "public"."teams" "t"
  WHERE (("t"."id" = "team_members"."team_id") AND ("t"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role"))))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."teams" "t"
  WHERE (("t"."id" = "team_members"."team_id") AND ("t"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Team properties are editable by team owners and admins" ON "public"."team_properties" USING (((EXISTS ( SELECT 1
   FROM "public"."teams"
  WHERE (("teams"."id" = "team_properties"."team_id") AND ("teams"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND (("profiles"."is_super_admin" = true) OR ("profiles"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Team properties are viewable by team members and property owner" ON "public"."team_properties" FOR SELECT USING (((EXISTS ( SELECT 1
   FROM "public"."team_members" "tm"
  WHERE (("tm"."team_id" = "team_properties"."team_id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text")))) OR (EXISTS ( SELECT 1
   FROM "public"."properties" "p"
  WHERE (("p"."id" = "team_properties"."property_id") AND ("p"."user_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."teams" "t"
  WHERE (("t"."id" = "team_properties"."team_id") AND ("t"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Teams are deletable by owners and admins" ON "public"."teams" FOR DELETE USING ((("owner_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Teams are insertable by authenticated users" ON "public"."teams" FOR INSERT WITH CHECK (("auth"."uid"() = "owner_id"));



CREATE POLICY "Teams are updatable by owners and admins" ON "public"."teams" FOR UPDATE USING ((("owner_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role"))))))) WITH CHECK ((("owner_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Teams are viewable by team members" ON "public"."teams" FOR SELECT USING (((EXISTS ( SELECT 1
   FROM "public"."team_members" "tm"
  WHERE (("tm"."team_id" = "teams"."id") AND ("tm"."user_id" = "auth"."uid"()) AND ("tm"."status" = 'active'::"text")))) OR ("owner_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND (("profiles"."is_super_admin" = true) OR ("profiles"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "User permissions are manageable by team owners and admins" ON "public"."user_permissions" USING (((EXISTS ( SELECT 1
   FROM "public"."teams" "t"
  WHERE (("t"."id" = "user_permissions"."team_id") AND ("t"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role"))))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."teams" "t"
  WHERE (("t"."id" = "user_permissions"."team_id") AND ("t"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "User permissions are viewable by owners and admins" ON "public"."user_permissions" FOR SELECT USING ((("user_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."teams" "t"
  WHERE (("t"."id" = "user_permissions"."team_id") AND ("t"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND (("p"."is_super_admin" = true) OR ("p"."role" = 'admin'::"public"."user_role")))))));



CREATE POLICY "Users can create their own damage reports" ON "public"."damage_reports" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can delete damage reports they have access to" ON "public"."damage_reports" FOR DELETE USING ("public"."has_damage_report_access"("id"));



CREATE POLICY "Users can delete their own damage reports" ON "public"."damage_reports" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can delete their own documents" ON "public"."property_documents" FOR DELETE USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can delete their own files" ON "public"."property_files" FOR DELETE USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can insert documents for properties they have access to" ON "public"."property_documents" FOR INSERT WITH CHECK (("public"."has_property_access"("property_id") AND ("user_id" = "auth"."uid"())));



CREATE POLICY "Users can insert files for properties they have access to" ON "public"."property_files" FOR INSERT WITH CHECK (("public"."has_property_access"("property_id") AND ("user_id" = "auth"."uid"())));



CREATE POLICY "Users can update damage reports they have access to" ON "public"."damage_reports" FOR UPDATE USING ("public"."has_damage_report_access"("id")) WITH CHECK ("public"."has_damage_report_access"("id"));



CREATE POLICY "Users can update their own damage reports" ON "public"."damage_reports" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id")) WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can update their own documents" ON "public"."property_documents" FOR UPDATE USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can update their own files" ON "public"."property_files" FOR UPDATE USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can view non-private property documents they have access " ON "public"."property_documents" FOR SELECT USING ((("is_private" = false) AND "public"."has_property_access"("property_id")));



CREATE POLICY "Users can view non-private property files they have access to" ON "public"."property_files" FOR SELECT USING ((("is_private" = false) AND "public"."has_property_access"("property_id")));



CREATE POLICY "Users can view their own damage reports" ON "public"."damage_reports" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can view their own documents" ON "public"."property_documents" FOR SELECT USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can view their own files" ON "public"."property_files" FOR SELECT USING (("user_id" = "auth"."uid"()));



ALTER TABLE "public"."damage_reports" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."inventory_items" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."maintenance_providers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."maintenance_requests" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."maintenance_tasks" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."property_documents" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."property_files" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."team_members" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."team_properties" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."teams" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_permissions" ENABLE ROW LEVEL SECURITY;


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON FUNCTION "public"."accept_invitation_and_add_member"("p_token" "text", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."accept_invitation_and_add_member"("p_token" "text", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."accept_invitation_and_add_member"("p_token" "text", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."accept_invitation_direct"("p_token" "text", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_password" "text", "p_role" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."accept_invitation_direct"("p_token" "text", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_password" "text", "p_role" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."accept_invitation_direct"("p_token" "text", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_password" "text", "p_role" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."accept_team_invitation"("invitation_token" "text", "accepting_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."accept_team_invitation"("invitation_token" "text", "accepting_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."accept_team_invitation"("invitation_token" "text", "accepting_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."accept_team_invitation_safe"("invitation_token" "text", "accepting_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."accept_team_invitation_safe"("invitation_token" "text", "accepting_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."accept_team_invitation_safe"("invitation_token" "text", "accepting_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_property_to_team_direct"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."add_property_to_team_direct"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_property_to_team_direct"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_property_to_team_safe"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."add_property_to_team_safe"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_property_to_team_safe"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_service_provider_default_permissions"("p_user_id" "uuid", "p_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."add_service_provider_default_permissions"("p_user_id" "uuid", "p_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_service_provider_default_permissions"("p_user_id" "uuid", "p_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_user_to_team"("p_user_id" "uuid", "p_team_id" "uuid", "p_added_by" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."add_user_to_team"("p_user_id" "uuid", "p_team_id" "uuid", "p_added_by" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_user_to_team"("p_user_id" "uuid", "p_team_id" "uuid", "p_added_by" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."can_access_damage_report"("report_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_access_damage_report"("report_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_access_damage_report"("report_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."can_create_maintenance_task"("p_property_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_create_maintenance_task"("p_property_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_create_maintenance_task"("p_property_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."can_manage_invitations"("target_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_manage_invitations"("target_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_manage_invitations"("target_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."can_manage_invitations_safe"("target_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_manage_invitations_safe"("target_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_manage_invitations_safe"("target_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."can_manage_permissions_safe"("target_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_manage_permissions_safe"("target_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_manage_permissions_safe"("target_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."can_manage_service_providers"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_manage_service_providers"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_manage_service_providers"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."can_manage_staff"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_manage_staff"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_manage_staff"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."can_manage_team_members"("target_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_manage_team_members"("target_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_manage_team_members"("target_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."can_manage_team_permissions"("target_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_manage_team_permissions"("target_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_manage_team_permissions"("target_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."check_damage_report_duplicate"() TO "anon";
GRANT ALL ON FUNCTION "public"."check_damage_report_duplicate"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_damage_report_duplicate"() TO "service_role";



GRANT ALL ON FUNCTION "public"."check_duplicate_property_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."check_duplicate_property_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_duplicate_property_name"() TO "service_role";



GRANT ALL ON FUNCTION "public"."check_property_in_team"("p_property_id" "uuid", "p_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."check_property_in_team"("p_property_id" "uuid", "p_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_property_in_team"("p_property_id" "uuid", "p_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."check_team_duplicate_property_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."check_team_duplicate_property_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_team_duplicate_property_name"() TO "service_role";



GRANT ALL ON FUNCTION "public"."create_maintenance_provider_for_service_provider"() TO "anon";
GRANT ALL ON FUNCTION "public"."create_maintenance_provider_for_service_provider"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_maintenance_provider_for_service_provider"() TO "service_role";



GRANT ALL ON FUNCTION "public"."create_maintenance_provider_for_team_member"() TO "anon";
GRANT ALL ON FUNCTION "public"."create_maintenance_provider_for_team_member"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_maintenance_provider_for_team_member"() TO "service_role";



GRANT ALL ON FUNCTION "public"."create_next_recurring_task"("completed_task_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."create_next_recurring_task"("completed_task_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_next_recurring_task"("completed_task_id" "uuid") TO "service_role";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";



GRANT ALL ON FUNCTION "public"."create_profile"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_role" "text", "p_is_super_admin" boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."create_profile"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_role" "text", "p_is_super_admin" boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_profile"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_role" "text", "p_is_super_admin" boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."create_profile_safely"("p_user_id" "uuid", "p_first_name" "text", "p_last_name" "text", "p_role" "text", "p_email" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_profile_safely"("p_user_id" "uuid", "p_first_name" "text", "p_last_name" "text", "p_role" "text", "p_email" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_profile_safely"("p_user_id" "uuid", "p_first_name" "text", "p_last_name" "text", "p_role" "text", "p_email" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_service_provider_profile"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_status" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_service_provider_profile"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_status" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_service_provider_profile"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_status" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_team_invitation"("team_id" "uuid", "email" "text", "role" "public"."user_role", "expires_in" interval) TO "anon";
GRANT ALL ON FUNCTION "public"."create_team_invitation"("team_id" "uuid", "email" "text", "role" "public"."user_role", "expires_in" interval) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_team_invitation"("team_id" "uuid", "email" "text", "role" "public"."user_role", "expires_in" interval) TO "service_role";



GRANT ALL ON FUNCTION "public"."create_team_invitation"("team_id" "uuid", "email" "text", "role" "public"."user_role", "invitation_token" "text", "expires_in" interval) TO "anon";
GRANT ALL ON FUNCTION "public"."create_team_invitation"("team_id" "uuid", "email" "text", "role" "public"."user_role", "invitation_token" "text", "expires_in" interval) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_team_invitation"("team_id" "uuid", "email" "text", "role" "public"."user_role", "invitation_token" "text", "expires_in" interval) TO "service_role";



GRANT ALL ON FUNCTION "public"."create_team_invitation"("p_team_id" "uuid", "p_email" "text", "p_role" "text", "p_invited_by" "uuid", "p_token" "text", "p_expires_in" interval) TO "anon";
GRANT ALL ON FUNCTION "public"."create_team_invitation"("p_team_id" "uuid", "p_email" "text", "p_role" "text", "p_invited_by" "uuid", "p_token" "text", "p_expires_in" interval) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_team_invitation"("p_team_id" "uuid", "p_email" "text", "p_role" "text", "p_invited_by" "uuid", "p_token" "text", "p_expires_in" interval) TO "service_role";



GRANT ALL ON FUNCTION "public"."create_team_invitation"("p_team_id" "uuid", "p_email" "text", "p_role" "public"."user_role", "p_invited_by" "uuid", "p_token" "text", "p_expires_in" interval) TO "anon";
GRANT ALL ON FUNCTION "public"."create_team_invitation"("p_team_id" "uuid", "p_email" "text", "p_role" "public"."user_role", "p_invited_by" "uuid", "p_token" "text", "p_expires_in" interval) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_team_invitation"("p_team_id" "uuid", "p_email" "text", "p_role" "public"."user_role", "p_invited_by" "uuid", "p_token" "text", "p_expires_in" interval) TO "service_role";



GRANT ALL ON FUNCTION "public"."create_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."create_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."debug_team_access"("p_team_id" "uuid", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."debug_team_access"("p_team_id" "uuid", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."debug_team_access"("p_team_id" "uuid", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."delete_property_cascade"("property_id_param" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."delete_property_cascade"("property_id_param" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."delete_property_cascade"("property_id_param" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."delete_team_cascade"("team_id_param" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."delete_team_cascade"("team_id_param" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."delete_team_cascade"("team_id_param" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."ensure_all_users_have_profiles"() TO "anon";
GRANT ALL ON FUNCTION "public"."ensure_all_users_have_profiles"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."ensure_all_users_have_profiles"() TO "service_role";



GRANT ALL ON FUNCTION "public"."ensure_profile_exists"() TO "anon";
GRANT ALL ON FUNCTION "public"."ensure_profile_exists"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."ensure_profile_exists"() TO "service_role";



GRANT ALL ON FUNCTION "public"."ensure_profile_exists"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_role" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."ensure_profile_exists"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_role" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ensure_profile_exists"("p_id" "uuid", "p_email" "text", "p_first_name" "text", "p_last_name" "text", "p_role" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."ensure_team_property_access"() TO "anon";
GRANT ALL ON FUNCTION "public"."ensure_team_property_access"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."ensure_team_property_access"() TO "service_role";



GRANT ALL ON FUNCTION "public"."execute_sql"("query" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."execute_sql"("query" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."execute_sql"("query" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."execute_sql_query"("sql_query" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."execute_sql_query"("sql_query" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."execute_sql_query"("sql_query" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."fix_team_invitation"("p_email" "text", "p_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."fix_team_invitation"("p_email" "text", "p_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."fix_team_invitation"("p_email" "text", "p_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_all_damage_reports"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_all_damage_reports"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_all_damage_reports"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_all_tables"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_all_tables"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_all_tables"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_damage_notes"("p_damage_report_id" "uuid", "p_include_private" boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."get_damage_notes"("p_damage_report_id" "uuid", "p_include_private" boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_damage_notes"("p_damage_report_id" "uuid", "p_include_private" boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_damage_photo_url"("p_photo_path" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_damage_photo_url"("p_photo_path" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_damage_photo_url"("p_photo_path" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_damage_photos"("p_damage_report_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_damage_photos"("p_damage_report_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_damage_photos"("p_damage_report_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_database_size"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_database_size"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_database_size"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_functions"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_functions"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_functions"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_low_stock_items"("user_id_param" "uuid", "property_id_param" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_low_stock_items"("user_id_param" "uuid", "property_id_param" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_low_stock_items"("user_id_param" "uuid", "property_id_param" "uuid") TO "service_role";



GRANT ALL ON TABLE "public"."maintenance_tasks" TO "anon";
GRANT ALL ON TABLE "public"."maintenance_tasks" TO "authenticated";
GRANT ALL ON TABLE "public"."maintenance_tasks" TO "service_role";



GRANT ALL ON FUNCTION "public"."get_maintenance_tasks_for_team_member"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_maintenance_tasks_for_team_member"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_maintenance_tasks_for_team_member"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_maintenance_tasks_for_user"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_maintenance_tasks_for_user"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_maintenance_tasks_for_user"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_profile"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_profile"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_profile"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_profile_by_id"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_profile_by_id"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_profile_by_id"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_profile_by_id_or_email"("p_id" "uuid", "p_email" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_profile_by_id_or_email"("p_id" "uuid", "p_email" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_profile_by_id_or_email"("p_id" "uuid", "p_email" "text") TO "service_role";



GRANT ALL ON TABLE "public"."properties" TO "anon";
GRANT ALL ON TABLE "public"."properties" TO "authenticated";
GRANT ALL ON TABLE "public"."properties" TO "service_role";



GRANT ALL ON FUNCTION "public"."get_properties_for_team_member"("p_user_id" "uuid", "p_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_properties_for_team_member"("p_user_id" "uuid", "p_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_properties_for_team_member"("p_user_id" "uuid", "p_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_property_damage_reports"("p_property_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_property_damage_reports"("p_property_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_property_damage_reports"("p_property_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_property_details"("p_property_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_property_details"("p_property_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_property_details"("p_property_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_property_details_simple"("p_property_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_property_details_simple"("p_property_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_property_details_simple"("p_property_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_property_if_team_member"("p_property_id" "uuid", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_property_if_team_member"("p_property_id" "uuid", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_property_if_team_member"("p_property_id" "uuid", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_property_with_team_access"("p_property_id" "uuid", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_property_with_team_access"("p_property_id" "uuid", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_property_with_team_access"("p_property_id" "uuid", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_providers"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_providers"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_providers"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_recurring_task_series"("p_task_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_recurring_task_series"("p_task_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_recurring_task_series"("p_task_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_service_provider_properties"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_service_provider_properties"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_service_provider_properties"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_table_columns"("table_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_table_columns"("table_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_table_columns"("table_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_table_size"("table_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_table_size"("table_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_table_size"("table_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_table_structure"("table_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_table_structure"("table_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_table_structure"("table_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_tables"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_tables"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_tables"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_tasks_for_properties_or_user"("property_ids" "uuid"[], "input_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_tasks_for_properties_or_user"("property_ids" "uuid"[], "input_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_tasks_for_properties_or_user"("property_ids" "uuid"[], "input_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_team_damage_reports"("p_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_team_damage_reports"("p_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_team_damage_reports"("p_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_team_members"("p_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_team_members"("p_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_team_members"("p_team_id" "uuid") TO "service_role";



GRANT ALL ON TABLE "public"."team_members" TO "anon";
GRANT ALL ON TABLE "public"."team_members" TO "authenticated";
GRANT ALL ON TABLE "public"."team_members" TO "service_role";



GRANT ALL ON TABLE "public"."team_members_with_profiles" TO "anon";
GRANT ALL ON TABLE "public"."team_members_with_profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."team_members_with_profiles" TO "service_role";



GRANT ALL ON FUNCTION "public"."get_team_members_with_profiles"("p_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_team_members_with_profiles"("p_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_team_members_with_profiles"("p_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_team_name_by_id"("team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_team_name_by_id"("team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_team_name_by_id"("team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_team_properties"("p_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_team_properties"("p_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_team_properties"("p_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_unique_user_properties"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_unique_user_properties"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_unique_user_properties"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_accessible_properties"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_accessible_properties"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_accessible_properties"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_damage_photos"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_damage_photos"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_damage_photos"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON TABLE "public"."damage_reports" TO "anon";
GRANT ALL ON TABLE "public"."damage_reports" TO "authenticated";
GRANT ALL ON TABLE "public"."damage_reports" TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_damage_reports"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_damage_reports"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_damage_reports"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_damage_reports"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_damage_reports"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_damage_reports"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_damage_reports_simple"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_damage_reports_simple"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_damage_reports_simple"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_inventory_items"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_inventory_items"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_inventory_items"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_maintenance_tasks"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_maintenance_tasks"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_maintenance_tasks"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_properties"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_properties"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_properties"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_role_properties"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_role_properties"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_role_properties"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_role_sd"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_role_sd"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_role_sd"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_team_access"("team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_team_access"("team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_team_access"("team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_team_ids"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_team_ids"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_team_ids"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_team_memberships"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_team_memberships"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_team_memberships"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_teams"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_teams"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_teams"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_booking_automation"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_booking_automation"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_booking_automation"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_task_completion"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_task_completion"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_task_completion"() TO "service_role";



GRANT ALL ON FUNCTION "public"."has_damage_note_access"("p_note_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_damage_note_access"("p_note_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_damage_note_access"("p_note_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_damage_photo_access"("p_photo_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_damage_photo_access"("p_photo_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_damage_photo_access"("p_photo_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_damage_report_access"("p_report_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_damage_report_access"("p_report_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_damage_report_access"("p_report_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_inventory_item_access"("p_item_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_inventory_item_access"("p_item_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_inventory_item_access"("p_item_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_maintenance_provider_access"("p_provider_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_maintenance_provider_access"("p_provider_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_maintenance_provider_access"("p_provider_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_maintenance_task_access"("task_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_maintenance_task_access"("task_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_maintenance_task_access"("task_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_maintenance_task_access_impersonation"("p_task_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_maintenance_task_access_impersonation"("p_task_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_maintenance_task_access_impersonation"("p_task_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_permission"("user_id" "uuid", "permission_name" "public"."permission_type", "team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_permission"("user_id" "uuid", "permission_name" "public"."permission_type", "team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_permission"("user_id" "uuid", "permission_name" "public"."permission_type", "team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_property_access"("p_property_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_property_access"("p_property_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_property_access"("p_property_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_property_document_access"("p_document_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_property_document_access"("p_document_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_property_document_access"("p_document_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_property_file_access"("p_file_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_property_file_access"("p_file_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_property_file_access"("p_file_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_purchase_order_access"("p_order_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_purchase_order_access"("p_order_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_purchase_order_access"("p_order_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_purchase_order_item_access"("p_item_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_purchase_order_item_access"("p_item_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_purchase_order_item_access"("p_item_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_service_provider_access"("p_provider_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_service_provider_access"("p_provider_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_service_provider_access"("p_provider_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_user_access"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_user_access"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_user_access"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_admin_user"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_admin_user"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_admin_user"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_current_user_super_admin"() TO "anon";
GRANT ALL ON FUNCTION "public"."is_current_user_super_admin"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_current_user_super_admin"() TO "service_role";



GRANT ALL ON FUNCTION "public"."is_property_in_team"("p_property_id" "uuid", "p_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_property_in_team"("p_property_id" "uuid", "p_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_property_in_team"("p_property_id" "uuid", "p_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_service_provider_in_team"("p_property_manager_id" "uuid", "p_service_provider_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_service_provider_in_team"("p_property_manager_id" "uuid", "p_service_provider_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_service_provider_in_team"("p_property_manager_id" "uuid", "p_service_provider_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_super_admin"("uid" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_super_admin"("uid" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_super_admin"("uid" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_super_admin_sd"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_super_admin_sd"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_super_admin_sd"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_team_member"("team_id" "uuid", "user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_team_member"("team_id" "uuid", "user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_team_member"("team_id" "uuid", "user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_team_member_sd"("team_id" "uuid", "user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_team_member_sd"("team_id" "uuid", "user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_team_member_sd"("team_id" "uuid", "user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_team_member_with_property_access"("p_user_id" "uuid", "p_property_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_team_member_with_property_access"("p_user_id" "uuid", "p_property_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_team_member_with_property_access"("p_user_id" "uuid", "p_property_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_team_owner"("team_id" "uuid", "user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_team_owner"("team_id" "uuid", "user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_team_owner"("team_id" "uuid", "user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_team_owner_sd"("team_id" "uuid", "user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_team_owner_sd"("team_id" "uuid", "user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_team_owner_sd"("team_id" "uuid", "user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."prevent_duplicate_property_names_in_team"() TO "anon";
GRANT ALL ON FUNCTION "public"."prevent_duplicate_property_names_in_team"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."prevent_duplicate_property_names_in_team"() TO "service_role";



GRANT ALL ON FUNCTION "public"."process_invitation_acceptance"("p_token" "text", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."process_invitation_acceptance"("p_token" "text", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."process_invitation_acceptance"("p_token" "text", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."property_belongs_to_team"("p_property_id" "uuid", "p_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."property_belongs_to_team"("p_property_id" "uuid", "p_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."property_belongs_to_team"("p_property_id" "uuid", "p_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."register_service_provider"("p_email" "text", "p_password" "text", "p_first_name" "text", "p_last_name" "text", "p_invitation_token" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."register_service_provider"("p_email" "text", "p_password" "text", "p_first_name" "text", "p_last_name" "text", "p_invitation_token" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."register_service_provider"("p_email" "text", "p_password" "text", "p_first_name" "text", "p_last_name" "text", "p_invitation_token" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."remove_property_from_team_direct"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."remove_property_from_team_direct"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."remove_property_from_team_direct"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."remove_property_from_team_safe"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."remove_property_from_team_safe"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."remove_property_from_team_safe"("p_team_id" "uuid", "p_property_id" "uuid", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."run_sql"("sql" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."run_sql"("sql" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."run_sql"("sql" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."scheduled_ensure_profiles"() TO "anon";
GRANT ALL ON FUNCTION "public"."scheduled_ensure_profiles"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."scheduled_ensure_profiles"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_damage_report_team_id"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_damage_report_team_id"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_damage_report_team_id"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_default_permissions"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_default_permissions"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_default_permissions"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_inventory_team_id"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_inventory_team_id"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_inventory_team_id"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_maintenance_task_team_id"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_maintenance_task_team_id"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_maintenance_task_team_id"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_purchase_order_team_id"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_purchase_order_team_id"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_purchase_order_team_id"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_storage_policy"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_storage_policy"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_storage_policy"() TO "service_role";



GRANT ALL ON FUNCTION "public"."setup_team_member_permissions"() TO "anon";
GRANT ALL ON FUNCTION "public"."setup_team_member_permissions"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."setup_team_member_permissions"() TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_inventory_team_ids"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_inventory_team_ids"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_inventory_team_ids"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_extension_token_last_used"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_extension_token_last_used"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_extension_token_last_used"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_inventory_team_id_on_team_property_change"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_inventory_team_id_on_team_property_change"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_inventory_team_id_on_team_property_change"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."update_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_user_preferences"("p_user_id" "uuid", "p_onboarding_state" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_can_access_team"("target_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."user_can_access_team"("target_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_can_access_team"("target_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_can_access_team"("user_id" "uuid", "team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."user_can_access_team"("user_id" "uuid", "team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_can_access_team"("user_id" "uuid", "team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_can_access_team_members"("target_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."user_can_access_team_members"("target_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_can_access_team_members"("target_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_can_access_team_properties"("target_team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."user_can_access_team_properties"("target_team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_can_access_team_properties"("target_team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_can_access_team_sd"("user_id" "uuid", "team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."user_can_access_team_sd"("user_id" "uuid", "team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_can_access_team_sd"("user_id" "uuid", "team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_has_permission"("p_user_id" "uuid", "p_permission" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."user_has_permission"("p_user_id" "uuid", "p_permission" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_has_permission"("p_user_id" "uuid", "p_permission" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_has_team_permission"("p_user_id" "uuid", "p_team_id" "uuid", "p_permission" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."user_has_team_permission"("p_user_id" "uuid", "p_team_id" "uuid", "p_permission" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_has_team_permission"("p_user_id" "uuid", "p_team_id" "uuid", "p_permission" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_is_team_member"("team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."user_is_team_member"("team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_is_team_member"("team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_is_team_owner"("team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."user_is_team_owner"("team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_is_team_owner"("team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_owns_team"("team_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."user_owns_team"("team_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_owns_team"("team_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."verify_backup"("backup_id" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."verify_backup"("backup_id" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."verify_backup"("backup_id" "text") TO "service_role";



GRANT ALL ON TABLE "public"."automation_queue" TO "anon";
GRANT ALL ON TABLE "public"."automation_queue" TO "authenticated";
GRANT ALL ON TABLE "public"."automation_queue" TO "service_role";



GRANT ALL ON TABLE "public"."automation_rules" TO "anon";
GRANT ALL ON TABLE "public"."automation_rules" TO "authenticated";
GRANT ALL ON TABLE "public"."automation_rules" TO "service_role";



GRANT ALL ON TABLE "public"."backups" TO "anon";
GRANT ALL ON TABLE "public"."backups" TO "authenticated";
GRANT ALL ON TABLE "public"."backups" TO "service_role";



GRANT ALL ON SEQUENCE "public"."backups_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."backups_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."backups_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."bookings" TO "anon";
GRANT ALL ON TABLE "public"."bookings" TO "authenticated";
GRANT ALL ON TABLE "public"."bookings" TO "service_role";



GRANT ALL ON TABLE "public"."collections" TO "anon";
GRANT ALL ON TABLE "public"."collections" TO "authenticated";
GRANT ALL ON TABLE "public"."collections" TO "service_role";



GRANT ALL ON TABLE "public"."damage_invoices" TO "anon";
GRANT ALL ON TABLE "public"."damage_invoices" TO "authenticated";
GRANT ALL ON TABLE "public"."damage_invoices" TO "service_role";



GRANT ALL ON TABLE "public"."damage_notes" TO "anon";
GRANT ALL ON TABLE "public"."damage_notes" TO "authenticated";
GRANT ALL ON TABLE "public"."damage_notes" TO "service_role";



GRANT ALL ON TABLE "public"."damage_photos" TO "anon";
GRANT ALL ON TABLE "public"."damage_photos" TO "authenticated";
GRANT ALL ON TABLE "public"."damage_photos" TO "service_role";



GRANT ALL ON TABLE "public"."maintenance_providers" TO "anon";
GRANT ALL ON TABLE "public"."maintenance_providers" TO "authenticated";
GRANT ALL ON TABLE "public"."maintenance_providers" TO "service_role";



GRANT ALL ON TABLE "public"."damage_reports_with_property" TO "anon";
GRANT ALL ON TABLE "public"."damage_reports_with_property" TO "authenticated";
GRANT ALL ON TABLE "public"."damage_reports_with_property" TO "service_role";



GRANT ALL ON TABLE "public"."extension_api_tokens" TO "anon";
GRANT ALL ON TABLE "public"."extension_api_tokens" TO "authenticated";
GRANT ALL ON TABLE "public"."extension_api_tokens" TO "service_role";



GRANT ALL ON TABLE "public"."inventory_items" TO "anon";
GRANT ALL ON TABLE "public"."inventory_items" TO "authenticated";
GRANT ALL ON TABLE "public"."inventory_items" TO "service_role";



GRANT ALL ON TABLE "public"."invitations" TO "anon";
GRANT ALL ON TABLE "public"."invitations" TO "authenticated";
GRANT ALL ON TABLE "public"."invitations" TO "service_role";



GRANT ALL ON TABLE "public"."invoice_items" TO "anon";
GRANT ALL ON TABLE "public"."invoice_items" TO "authenticated";
GRANT ALL ON TABLE "public"."invoice_items" TO "service_role";



GRANT ALL ON TABLE "public"."maintenance_requests" TO "anon";
GRANT ALL ON TABLE "public"."maintenance_requests" TO "authenticated";
GRANT ALL ON TABLE "public"."maintenance_requests" TO "service_role";



GRANT ALL ON TABLE "public"."property_documents" TO "anon";
GRANT ALL ON TABLE "public"."property_documents" TO "authenticated";
GRANT ALL ON TABLE "public"."property_documents" TO "service_role";



GRANT ALL ON TABLE "public"."property_files" TO "anon";
GRANT ALL ON TABLE "public"."property_files" TO "authenticated";
GRANT ALL ON TABLE "public"."property_files" TO "service_role";



GRANT ALL ON TABLE "public"."purchase_order_items" TO "anon";
GRANT ALL ON TABLE "public"."purchase_order_items" TO "authenticated";
GRANT ALL ON TABLE "public"."purchase_order_items" TO "service_role";



GRANT ALL ON TABLE "public"."purchase_orders" TO "anon";
GRANT ALL ON TABLE "public"."purchase_orders" TO "authenticated";
GRANT ALL ON TABLE "public"."purchase_orders" TO "service_role";



GRANT ALL ON TABLE "public"."service_providers" TO "anon";
GRANT ALL ON TABLE "public"."service_providers" TO "authenticated";
GRANT ALL ON TABLE "public"."service_providers" TO "service_role";



GRANT ALL ON TABLE "public"."team_invitations" TO "anon";
GRANT ALL ON TABLE "public"."team_invitations" TO "authenticated";
GRANT ALL ON TABLE "public"."team_invitations" TO "service_role";



GRANT ALL ON TABLE "public"."team_properties" TO "anon";
GRANT ALL ON TABLE "public"."team_properties" TO "authenticated";
GRANT ALL ON TABLE "public"."team_properties" TO "service_role";



GRANT ALL ON TABLE "public"."teams" TO "anon";
GRANT ALL ON TABLE "public"."teams" TO "authenticated";
GRANT ALL ON TABLE "public"."teams" TO "service_role";



GRANT ALL ON TABLE "public"."user_permissions" TO "anon";
GRANT ALL ON TABLE "public"."user_permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."user_permissions" TO "service_role";



GRANT ALL ON TABLE "public"."user_preferences" TO "anon";
GRANT ALL ON TABLE "public"."user_preferences" TO "authenticated";
GRANT ALL ON TABLE "public"."user_preferences" TO "service_role";



GRANT ALL ON TABLE "public"."user_settings" TO "anon";
GRANT ALL ON TABLE "public"."user_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."user_settings" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






RESET ALL;
