import './App.css';
import { Routes, Route, Outlet, Navigate } from 'react-router-dom';
import { RequireAuth } from './components/auth/RequireAuth';
import ProtectedRoute from './components/auth/ProtectedRoute';
import MainLayout from './components/layout/MainLayout';
import { Toaster } from 'sonner';
import MaintenanceResponseHandler from './components/maintenance/MaintenanceResponseHandler';
import { OnboardingProvider } from './contexts/OnboardingContext';
import { ImpersonationProvider } from './contexts/ImpersonationContext';
import InstallPWAPrompt from './components/pwa/InstallPWAPrompt';
import { useEffect } from 'react';
import { createStorageBuckets } from './utils/createStorageBuckets';
import { toast } from 'sonner';
import SessionManager from './components/auth/SessionManager';
import { useAuth } from './contexts/AuthContext';
import ConnectionStatusIndicator from './components/ui/ConnectionStatusIndicator';

// Import pages
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import Register from './pages/Register';
import Auth from './pages/Auth';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import Properties from './pages/Properties';
import PropertyDetail from './pages/PropertyDetail';
import Settings from './pages/Settings';
import Contacts from './pages/Contacts';
import ContactDetail from './pages/ContactDetail';
import Index from './pages/Index';
import Inventory from './pages/Inventory';
import PurchaseOrders from './pages/PurchaseOrders';
import Collections from './pages/Collections';
import CollectionDetail from './pages/CollectionDetail';
import Damages from './pages/Damages';
import DamageDetail from './pages/DamageDetail';
import InvoicePage from './pages/InvoicePage';
import InvoiceDetail from './pages/InvoiceDetail';
import AddEntry from './pages/AddEntry';
import Maintenance from './pages/Maintenance';
import MaintenanceProviders from './pages/MaintenanceProviders';
import AdminDashboard from './pages/AdminDashboard';
import TeamDashboard from './pages/TeamDashboard';
import InvitationPage from './pages/InvitationPage';
import Operations from './pages/Operations';
import TaskAutomation from './pages/TaskAutomation';
import NotFound from './pages/NotFound';
import StyleGuideDemo from './pages/StyleGuideDemo';
import PropertyDebugger from './components/debug/PropertyDebugger';
import DataLoadingDebugPage from './pages/DataLoadingDebugPage';
import DiagnosticPage from './pages/DiagnosticPage';
import TestSimple from './pages/TestSimple';
import PropertiesRebuild from './pages/PropertiesRebuild';
import CalendarTestPage from './pages/CalendarTestPage';

function App() {
  // Initialize storage buckets on app load
  useEffect(() => {
    // Set default global variables immediately to ensure they're always available
    window.INVENTORY_BUCKET_NAME = 'inventory';
    window.INVENTORY_FOLDER_PATH = 'inventory-images';

    // Initialize storage in the background without blocking the app
    const initStorage = async () => {
      try {
        console.log('Initializing storage buckets...');

        // Simplified approach - just set the defaults and continue
        // This prevents the app from hanging on storage initialization
        if (import.meta.env.DEV) {
          console.log('Development mode: assuming all buckets exist');
          return;
        }

        // Try to initialize buckets but don't wait too long
        setTimeout(() => {
          console.log('Storage bucket initialization taking too long, continuing with defaults');
        }, 3000);

        // Attempt to initialize but don't block the app
        createStorageBuckets().then(success => {
          if (success) {
            console.log('Storage buckets initialized successfully');
          } else {
            console.warn('Failed to initialize storage buckets. Will use edge functions for uploads.');
          }
        }).catch(error => {
          console.error('Storage bucket initialization failed:', error);
        });
      } catch (error) {
        console.error('Error initializing storage buckets:', error);
      }
    };

    // Start the initialization process but don't wait for it
    initStorage();
  }, []);

  return (
    <>
      <OnboardingProvider>
        <ImpersonationProvider>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/auth" element={<Auth />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/invite" element={<InvitationPage />} />
            <Route path="/diagnostic" element={<DiagnosticPage />} />

            {/* Hash routes are handled by HashRouter directly, no need for redirects */}

            <Route element={<RequireAuth />}>
              <Route element={<MainLayout><Outlet /></MainLayout>}>
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/operations" element={<Operations />} />
                <Route path="/properties" element={<Properties />} />
                <Route path="/properties/:id" element={<PropertyDetail />} />
                <Route path="/contacts" element={<Contacts />} />
                <Route path="/contacts/:id" element={<ContactDetail />} />
                <Route path="/inventory" element={<Inventory />} />
                <Route path="/purchase-orders" element={<PurchaseOrders />} />
                <Route path="/collections" element={<Collections />} />
                <Route path="/collections/:id" element={<CollectionDetail />} />
                <Route path="/damages" element={<Damages />} />
                <Route path="/damages/:id" element={<DamageDetail />} />
                <Route path="/invoices" element={<InvoicePage />} />
                <Route path="/invoices/:id" element={<InvoiceDetail />} />
                <Route path="/maintenance" element={<Maintenance />} />
                <Route path="/maintenance/providers" element={<MaintenanceProviders />} />
                <Route path="/maintenance/automation" element={<TaskAutomation />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/settings/:section" element={<Settings />} />
                <Route path="/add" element={<AddEntry />} />
                <Route path="/teams" element={<TeamDashboard />} />
                <Route path="/style-guide" element={<StyleGuideDemo />} />
                <Route path="/debug/properties" element={<PropertyDebugger />} />
                <Route path="/debug/data-loading" element={<DataLoadingDebugPage />} />
                <Route path="/test-simple" element={<TestSimple />} />
                <Route path="/properties-rebuild" element={<PropertiesRebuild />} />
                <Route path="/calendar-test" element={<CalendarTestPage />} />

                {/* Admin Dashboard Route - added debug property to help with tracing issues */}
                <Route
                  path="/admin"
                  element={
                    <ProtectedRoute requireSuperAdmin={true}>
                      <AdminDashboard />
                    </ProtectedRoute>
                  }
                />
              </Route>
            </Route>

            {/* Add new route for maintenance responses */}
            <Route
              path="/maintenance/response"
              element={
                <MaintenanceResponseHandler />
              }
            />

            {/* Special route to handle password reset from email links */}
            <Route
              path="/reset-password"
              element={<ResetPassword />}
            />

            <Route path="*" element={<NotFound />} />
          </Routes>
          <Toaster position="top-right" />
          <InstallPWAPrompt />
          <ConnectionStatusIndicator />
          <SessionManager
            idleTimeout={72 * 60 * 60 * 1000} // 72 hours
            warningTime={30 * 60 * 1000} // 30 minutes
            refreshInterval={24 * 60 * 60 * 1000} // 24 hours - increased to match longer session
            maxStaleTime={60 * 60 * 1000} // 1 hour - unchanged
          />
        </ImpersonationProvider>
      </OnboardingProvider>
    </>
  );
}

export default App;
