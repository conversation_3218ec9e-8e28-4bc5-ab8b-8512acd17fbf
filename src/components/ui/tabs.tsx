import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const Tabs = TabsPrimitive.Root

const tabsListVariants = cva(
  "inline-flex h-10 items-center justify-center p-1 text-muted-foreground transition-all duration-300",
  {
    variants: {
      variant: {
        default:
          "rounded-md bg-muted",
        glass:
          "rounded-lg bg-white/10 dark:bg-black/10 backdrop-blur-xl border border-white/30 dark:border-white/20 shadow-lg",
        glass_nav:
          "rounded-lg bg-white/5 dark:bg-black/5 backdrop-blur-sm border border-white/20 dark:border-white/10 shadow-md",
        minimal:
          "rounded-md bg-transparent",
        outline:
          "rounded-lg border border-white/30 dark:border-white/20 bg-transparent backdrop-blur-sm shadow-sm",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface TabsListProps
  extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>,
    VariantProps<typeof tabsListVariants> {}

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  TabsListProps
>(({ className, variant, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(tabsListVariants({ variant }), className)}
    {...props}
  />
))
TabsList.displayName = TabsPrimitive.List.displayName

const tabsTriggerVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:scale-105 active:scale-95",
  {
    variants: {
      variant: {
        default:
          "rounded-sm data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
        glass:
          "rounded-md data-[state=active]:bg-white/25 dark:data-[state=active]:bg-black/25 data-[state=active]:text-foreground data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-sm hover:bg-white/15 dark:hover:bg-black/15",
        glass_nav:
          "rounded-md data-[state=active]:bg-white/20 dark:data-[state=active]:bg-black/20 data-[state=active]:border data-[state=active]:border-white/30 dark:data-[state=active]:border-white/20 data-[state=active]:shadow-md hover:bg-white/10 dark:hover:bg-black/10",
        minimal:
          "rounded-md data-[state=active]:text-foreground data-[state=active]:border-b-2 data-[state=active]:border-primary hover:text-foreground/80",
        outline:
          "rounded-md data-[state=active]:bg-white/15 dark:data-[state=active]:bg-black/15 data-[state=active]:border data-[state=active]:border-white/40 dark:data-[state=active]:border-white/30 data-[state=active]:shadow-sm hover:bg-white/10 dark:hover:bg-black/10",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface TabsTriggerProps
  extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>,
    VariantProps<typeof tabsTriggerVariants> {}

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  TabsTriggerProps
>(({ className, variant, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(tabsTriggerVariants({ variant }), className)}
    {...props}
  />
))
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      "animate-in fade-in-50 slide-in-from-bottom-1 duration-300",
      className
    )}
    {...props}
  />
))
TabsContent.displayName = TabsPrimitive.Content.displayName

export { Tabs, TabsList, TabsTrigger, TabsContent }
