import * as React from "react"
import * as NavigationMenuPrimitive from "@radix-ui/react-navigation-menu"
import { cva, type VariantProps } from "class-variance-authority"
import { ChevronDown } from "lucide-react"

import { cn } from "@/lib/utils"

const NavigationMenu = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Root>
>(({ className, children, ...props }, ref) => (
  <NavigationMenuPrimitive.Root
    ref={ref}
    className={cn(
      "relative z-10 flex max-w-max flex-1 items-center justify-center",
      className
    )}
    {...props}
  >
    {children}
    <NavigationMenuViewport />
  </NavigationMenuPrimitive.Root>
))
NavigationMenu.displayName = NavigationMenuPrimitive.Root.displayName

const navigationMenuListVariants = cva(
  "group flex flex-1 list-none items-center justify-center space-x-1 transition-all duration-300",
  {
    variants: {
      variant: {
        default: "",
        glass:
          "bg-white/10 dark:bg-black/10 backdrop-blur-xl border border-white/30 dark:border-white/20 rounded-lg p-2 shadow-lg",
        glass_nav:
          "bg-white/5 dark:bg-black/5 backdrop-blur-sm border border-white/20 dark:border-white/10 rounded-md p-1 shadow-md",
        minimal:
          "bg-transparent",
        outline:
          "border border-white/30 dark:border-white/20 bg-transparent backdrop-blur-sm rounded-md p-1 shadow-sm",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface NavigationMenuListProps
  extends React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.List>,
    VariantProps<typeof navigationMenuListVariants> {}

const NavigationMenuList = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.List>,
  NavigationMenuListProps
>(({ className, variant, ...props }, ref) => (
  <NavigationMenuPrimitive.List
    ref={ref}
    className={cn(navigationMenuListVariants({ variant }), className)}
    {...props}
  />
))
NavigationMenuList.displayName = NavigationMenuPrimitive.List.displayName

const NavigationMenuItem = NavigationMenuPrimitive.Item

const navigationMenuTriggerStyle = cva(
  "group inline-flex h-10 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-all duration-300 focus:outline-none disabled:pointer-events-none disabled:opacity-50 hover:scale-105 active:scale-95",
  {
    variants: {
      variant: {
        default:
          "bg-background hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground data-[active]:bg-accent/50 data-[state=open]:bg-accent/50",
        glass:
          "bg-white/10 dark:bg-black/10 backdrop-blur-xl border border-white/30 dark:border-white/20 shadow-lg hover:bg-white/20 dark:hover:bg-black/20 hover:border-white/40 dark:hover:border-white/30 focus:bg-white/25 dark:focus:bg-black/25 data-[active]:bg-white/25 dark:data-[active]:bg-black/25 data-[state=open]:bg-white/25 dark:data-[state=open]:bg-black/25",
        glass_nav:
          "bg-white/5 dark:bg-black/5 backdrop-blur-sm border border-white/20 dark:border-white/10 shadow-md hover:bg-white/15 dark:hover:bg-black/15 hover:border-white/30 dark:hover:border-white/20 focus:bg-white/20 dark:focus:bg-black/20 data-[active]:bg-white/20 dark:data-[active]:bg-black/20 data-[state=open]:bg-white/20 dark:data-[state=open]:bg-black/20",
        minimal:
          "bg-transparent hover:bg-white/10 dark:hover:bg-black/10 hover:text-foreground focus:bg-white/15 dark:focus:bg-black/15 data-[active]:bg-white/15 dark:data-[active]:bg-black/15 data-[state=open]:bg-white/15 dark:data-[state=open]:bg-black/15",
        outline:
          "border border-white/30 dark:border-white/20 bg-transparent backdrop-blur-sm hover:bg-white/10 dark:hover:bg-black/10 hover:border-white/40 dark:hover:border-white/30 focus:bg-white/15 dark:focus:bg-black/15 data-[active]:bg-white/15 dark:data-[active]:bg-black/15 data-[state=open]:bg-white/15 dark:data-[state=open]:bg-black/15",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface NavigationMenuTriggerProps
  extends React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Trigger>,
    VariantProps<typeof navigationMenuTriggerStyle> {}

const NavigationMenuTrigger = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.Trigger>,
  NavigationMenuTriggerProps
>(({ className, children, variant, ...props }, ref) => (
  <NavigationMenuPrimitive.Trigger
    ref={ref}
    className={cn(navigationMenuTriggerStyle({ variant }), "group", className)}
    {...props}
  >
    {children}{" "}
    <ChevronDown
      className="relative top-[1px] ml-1 h-3 w-3 transition duration-300 group-data-[state=open]:rotate-180"
      aria-hidden="true"
    />
  </NavigationMenuPrimitive.Trigger>
))
NavigationMenuTrigger.displayName = NavigationMenuPrimitive.Trigger.displayName

const NavigationMenuContent = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Content>
>(({ className, ...props }, ref) => (
  <NavigationMenuPrimitive.Content
    ref={ref}
    className={cn(
      "left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto",
      "bg-white/10 dark:bg-black/10 backdrop-blur-xl border border-white/30 dark:border-white/20 rounded-lg shadow-2xl",
      className
    )}
    {...props}
  />
))
NavigationMenuContent.displayName = NavigationMenuPrimitive.Content.displayName

const NavigationMenuLink = NavigationMenuPrimitive.Link

const NavigationMenuViewport = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.Viewport>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Viewport>
>(({ className, ...props }, ref) => (
  <div className={cn("absolute left-0 top-full flex justify-center")}>
    <NavigationMenuPrimitive.Viewport
      className={cn(
        "origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-lg border border-white/30 dark:border-white/20 bg-white/10 dark:bg-black/10 backdrop-blur-xl text-popover-foreground shadow-2xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]",
        className
      )}
      ref={ref}
      {...props}
    />
  </div>
))
NavigationMenuViewport.displayName =
  NavigationMenuPrimitive.Viewport.displayName

const NavigationMenuIndicator = React.forwardRef<
  React.ElementRef<typeof NavigationMenuPrimitive.Indicator>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Indicator>
>(({ className, ...props }, ref) => (
  <NavigationMenuPrimitive.Indicator
    ref={ref}
    className={cn(
      "top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",
      className
    )}
    {...props}
  >
    <div className="relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-white/30 dark:bg-black/30 backdrop-blur-sm shadow-md" />
  </NavigationMenuPrimitive.Indicator>
))
NavigationMenuIndicator.displayName =
  NavigationMenuPrimitive.Indicator.displayName

export {
  navigationMenuTriggerStyle,
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuContent,
  NavigationMenuTrigger,
  NavigationMenuLink,
  NavigationMenuIndicator,
  NavigationMenuViewport,
}
