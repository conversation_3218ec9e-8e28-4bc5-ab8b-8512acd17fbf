import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

/**
 * ButtonGroup - A standardized container for grouping buttons
 * 
 * Usage:
 * <ButtonGroup position="right">
 *   <Button>Cancel</Button>
 *   <Button variant="primary">Save</Button>
 * </ButtonGroup>
 */
export const ButtonGroup: React.FC<{
  children: React.ReactNode;
  position?: 'left' | 'right' | 'center' | 'between';
  className?: string;
}> = ({ children, position = 'right', className }) => {
  const positionClasses = {
    left: 'justify-start',
    right: 'justify-end',
    center: 'justify-center',
    between: 'justify-between',
  };

  return (
    <div 
      className={cn(
        'flex flex-wrap items-center gap-2 mt-4',
        positionClasses[position],
        className
      )}
    >
      {children}
    </div>
  );
};

/**
 * ActionButton - A standardized button for primary actions
 * 
 * Usage:
 * <ActionButton 
 *   onClick={handleSave} 
 *   loading={isSaving}
 *   icon={<Save />}
 * >
 *   Save Changes
 * </ActionButton>
 */
export const ActionButton: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  loading?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}> = ({ 
  children, 
  onClick, 
  loading = false, 
  disabled = false, 
  icon, 
  variant = 'default',
  size = 'default',
  className,
  type = 'button'
}) => {
  return (
    <Button
      type={type}
      variant={variant}
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn('flex items-center gap-2', className)}
    >
      {loading ? <Loader2 className="h-4 w-4 animate-spin text-foreground" /> : icon}
      {children}
    </Button>
  );
};

/**
 * PageHeaderButtons - A standardized container for page header buttons
 * 
 * Usage:
 * <PageHeaderButtons>
 *   <Button variant="outline" size="sm">Filter</Button>
 *   <Button size="sm">Add New</Button>
 * </PageHeaderButtons>
 */
export const PageHeaderButtons: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn('flex items-center space-x-1.5', className)}>
      {children}
    </div>
  );
};

/**
 * FormButtons - A standardized container for form buttons
 * 
 * Usage:
 * <FormButtons
 *   onCancel={handleCancel}
 *   onSubmit={handleSubmit}
 *   submitLabel="Save Changes"
 *   cancelLabel="Cancel"
 *   loading={isSubmitting}
 * />
 */
export const FormButtons: React.FC<{
  onCancel?: () => void;
  onSubmit?: () => void;
  submitLabel?: string;
  cancelLabel?: string;
  loading?: boolean;
  disabled?: boolean;
  position?: 'left' | 'right' | 'center';
  className?: string;
}> = ({ 
  onCancel, 
  onSubmit, 
  submitLabel = 'Save', 
  cancelLabel = 'Cancel', 
  loading = false, 
  disabled = false,
  position = 'right',
  className
}) => {
  return (
    <ButtonGroup position={position} className={className}>
      {onCancel && (
        <Button 
          type="button" 
          variant="outline" 
          onClick={onCancel}
          disabled={loading}
        >
          {cancelLabel}
        </Button>
      )}
      {onSubmit && (
        <ActionButton
          type="submit"
          onClick={onSubmit}
          loading={loading}
          disabled={disabled}
        >
          {submitLabel}
        </ActionButton>
      )}
    </ButtonGroup>
  );
};

/**
 * DataTableButtons - A standardized container for data table action buttons
 * 
 * Usage:
 * <DataTableButtons>
 *   <Button variant="ghost" size="sm"><Edit className="h-4 w-4 mr-1" /> Edit</Button>
 *   <Button variant="ghost" size="sm" className="text-destructive"><Trash className="h-4 w-4 mr-1" /> Delete</Button>
 * </DataTableButtons>
 */
export const DataTableButtons: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn('flex items-center space-x-1', className)}>
      {children}
    </div>
  );
};
