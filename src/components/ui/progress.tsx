import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const progressVariants = cva(
  "relative w-full overflow-hidden transition-all duration-300",
  {
    variants: {
      variant: {
        default: "h-4 rounded-full bg-secondary",
        glass: 
          "h-4 rounded-full bg-white/20 dark:bg-black/20 backdrop-blur-xl border border-white/30 dark:border-white/20 shadow-lg",
        glass_thin:
          "h-2 rounded-full bg-white/15 dark:bg-black/15 backdrop-blur-sm border border-white/20 dark:border-white/10 shadow-md",
        minimal:
          "h-3 rounded-full bg-white/10 dark:bg-black/10 backdrop-blur-sm",
        gradient:
          "h-4 rounded-full bg-gradient-to-r from-secondary/50 to-secondary backdrop-blur-sm shadow-inner",
      },
      size: {
        default: "h-4",
        sm: "h-2",
        lg: "h-6",
        xl: "h-8",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const progressIndicatorVariants = cva(
  "h-full w-full flex-1 transition-all duration-500 ease-out",
  {
    variants: {
      variant: {
        default: "bg-primary",
        glass: 
          "bg-gradient-to-r from-primary/80 to-primary backdrop-blur-sm shadow-lg rounded-full",
        glass_glow:
          "bg-gradient-to-r from-primary/90 to-primary backdrop-blur-sm shadow-lg rounded-full animate-pulse-glow",
        minimal:
          "bg-primary/80 rounded-full",
        gradient:
          "bg-gradient-to-r from-primary via-primary/90 to-primary/80 rounded-full shadow-md",
        rainbow:
          "bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full shadow-lg",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface ProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,
    VariantProps<typeof progressVariants> {
  indicatorVariant?: VariantProps<typeof progressIndicatorVariants>['variant'];
  showLabel?: boolean;
  label?: string;
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ className, value, variant, size, indicatorVariant = 'default', showLabel, label, ...props }, ref) => (
  <div className="space-y-2">
    {showLabel && (
      <div className="flex justify-between items-center text-sm text-muted-foreground/80">
        <span>{label || 'Progress'}</span>
        <span>{value || 0}%</span>
      </div>
    )}
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(progressVariants({ variant, size }), className)}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className={cn(progressIndicatorVariants({ variant: indicatorVariant }))}
        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
      />
    </ProgressPrimitive.Root>
  </div>
))
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress, progressVariants, progressIndicatorVariants }
