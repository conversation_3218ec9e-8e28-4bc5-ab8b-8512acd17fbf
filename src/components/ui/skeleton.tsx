
import { cn } from "@/lib/utils"
import { cva, type VariantProps } from "class-variance-authority"

const skeletonVariants = cva(
  "animate-pulse rounded-md",
  {
    variants: {
      variant: {
        default: "bg-muted/50",
        glass: 
          "bg-gradient-to-r from-white/20 via-white/10 to-white/20 dark:from-black/20 dark:via-black/10 dark:to-black/20 backdrop-blur-sm border border-white/20 dark:border-white/10 shadow-lg",
        glass_shimmer:
          "bg-gradient-to-r from-white/10 via-white/30 to-white/10 dark:from-black/10 dark:via-black/30 dark:to-black/10 backdrop-blur-sm border border-white/20 dark:border-white/10 shadow-lg animate-shimmer",
        minimal:
          "bg-white/10 dark:bg-black/10 backdrop-blur-sm",
        outline:
          "border border-white/30 dark:border-white/20 bg-white/5 dark:bg-black/5 backdrop-blur-sm",
      },
      size: {
        default: "h-4",
        sm: "h-3",
        lg: "h-6",
        xl: "h-8",
        card: "h-32",
        avatar: "h-10 w-10 rounded-full",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface SkeletonProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof skeletonVariants> {}

function Skeleton({
  className,
  variant,
  size,
  ...props
}: SkeletonProps) {
  return (
    <div
      className={cn(skeletonVariants({ variant, size }), className)}
      {...props}
    />
  )
}

export { Skeleton, skeletonVariants }
