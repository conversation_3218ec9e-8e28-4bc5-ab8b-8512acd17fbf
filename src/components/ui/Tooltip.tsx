
import React, { useState } from 'react';
import { Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const tooltipVariants = cva(
  "absolute z-50 px-3 py-2 text-xs font-medium whitespace-nowrap pointer-events-none transition-all duration-300 transform",
  {
    variants: {
      variant: {
        default: "text-white bg-black/80 backdrop-blur-sm rounded-md shadow-lg",
        glass: 
          "text-foreground bg-white/90 dark:bg-black/90 backdrop-blur-xl border border-white/30 dark:border-white/20 rounded-lg shadow-2xl",
        glass_dark:
          "text-white bg-black/60 backdrop-blur-xl border border-white/20 rounded-lg shadow-2xl",
        minimal:
          "text-foreground bg-white/95 dark:bg-black/95 backdrop-blur-sm border border-border rounded-md shadow-md",
        accent:
          "text-primary-foreground bg-primary backdrop-blur-sm rounded-md shadow-lg",
      },
      size: {
        default: "px-3 py-2 text-xs",
        sm: "px-2 py-1 text-xs",
        lg: "px-4 py-3 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const tooltipTriggerVariants = cva(
  "inline-flex items-center cursor-help transition-all duration-200",
  {
    variants: {
      variant: {
        default: "hover:opacity-80",
        glass: "hover:bg-white/10 dark:hover:bg-black/10 hover:scale-105 rounded-md p-1",
        subtle: "hover:bg-white/5 dark:hover:bg-black/5 rounded-sm p-0.5",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface TooltipProps extends VariantProps<typeof tooltipVariants> {
  text: string;
  children?: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  icon?: React.ReactNode;
  className?: string;
  iconClassName?: string;
  triggerVariant?: VariantProps<typeof tooltipTriggerVariants>['variant'];
  delay?: number;
}

const Tooltip: React.FC<TooltipProps> = ({
  text,
  children,
  position = 'top',
  icon = <Info size={16} />,
  className = '',
  iconClassName = '',
  variant,
  size,
  triggerVariant = 'default',
  delay = 0
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);

  const positionClasses = {
    top: 'bottom-full mb-2',
    bottom: 'top-full mt-2',
    left: 'right-full mr-2',
    right: 'left-full ml-2'
  };

  const arrowClasses = {
    top: "top-full -translate-y-1 left-1/2 -translate-x-1/2",
    bottom: "bottom-full translate-y-1 left-1/2 -translate-x-1/2",
    left: "left-full -translate-x-1 top-1/2 -translate-y-1/2",
    right: "right-full translate-x-1 top-1/2 -translate-y-1/2"
  };

  const handleMouseEnter = () => {
    if (delay > 0) {
      const id = setTimeout(() => setIsVisible(true), delay);
      setTimeoutId(id);
    } else {
      setIsVisible(true);
    }
  };

  const handleMouseLeave = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  const getArrowColor = () => {
    switch (variant) {
      case 'glass':
        return 'bg-white/90 dark:bg-black/90 border border-white/30 dark:border-white/20';
      case 'glass_dark':
        return 'bg-black/60 border border-white/20';
      case 'minimal':
        return 'bg-white/95 dark:bg-black/95 border border-border';
      case 'accent':
        return 'bg-primary';
      default:
        return 'bg-black/80';
    }
  };

  return (
    <div className="relative inline-block">
      <div 
        className={cn(tooltipTriggerVariants({ variant: triggerVariant }), iconClassName)}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onFocus={handleMouseEnter}
        onBlur={handleMouseLeave}
      >
        {children || icon}
      </div>
      
      {isVisible && (
        <div 
          className={cn(
            tooltipVariants({ variant, size }),
            positionClasses[position],
            "animate-in fade-in-0 zoom-in-95 duration-200",
            className
          )}
        >
          {text}
          <div 
            className={cn(
              "absolute w-2 h-2 rotate-45",
              getArrowColor(),
              arrowClasses[position]
            )}
          />
        </div>
      )}
    </div>
  );
};

export default Tooltip;
export { tooltipVariants, tooltipTriggerVariants };
