
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:scale-105 active:scale-95",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80 shadow-md hover:shadow-lg",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm hover:shadow-md",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 shadow-md hover:shadow-lg",
        outline: 
          "text-foreground border-border hover:bg-accent hover:text-accent-foreground",
        glass:
          "border border-white/30 dark:border-white/20 bg-white/20 dark:bg-black/20 backdrop-blur-xl text-foreground hover:bg-white/30 dark:hover:bg-black/30 hover:border-white/40 dark:hover:border-white/30 shadow-lg hover:shadow-xl",
        glass_primary:
          "border border-primary/30 bg-primary/20 backdrop-blur-xl text-primary-foreground hover:bg-primary/30 hover:border-primary/40 shadow-lg hover:shadow-xl",
        glass_success:
          "border border-green-500/30 bg-green-500/20 backdrop-blur-xl text-green-700 dark:text-green-300 hover:bg-green-500/30 hover:border-green-500/40 shadow-lg hover:shadow-xl",
        glass_warning:
          "border border-yellow-500/30 bg-yellow-500/20 backdrop-blur-xl text-yellow-700 dark:text-yellow-300 hover:bg-yellow-500/30 hover:border-yellow-500/40 shadow-lg hover:shadow-xl",
        glass_error:
          "border border-red-500/30 bg-red-500/20 backdrop-blur-xl text-red-700 dark:text-red-300 hover:bg-red-500/30 hover:border-red-500/40 shadow-lg hover:shadow-xl",
        minimal:
          "border border-white/20 dark:border-white/10 bg-white/10 dark:bg-black/10 backdrop-blur-sm text-foreground hover:bg-white/15 dark:hover:bg-black/15 shadow-sm hover:shadow-md",
        gradient:
          "border-transparent bg-gradient-to-r from-primary to-primary/80 text-primary-foreground hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl",
      },
      size: {
        default: "px-2.5 py-0.5 text-xs",
        sm: "px-2 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
        xl: "px-4 py-1.5 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
