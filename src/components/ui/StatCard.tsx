import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface StatCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  value: string | number;
  icon: LucideIcon;
  colorScheme?: 'blue' | 'amber' | 'purple' | 'green';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  alert?: boolean;
  loading?: boolean;
  subtitle?: string;
  className?: string;
}

const StatCard = forwardRef<HTMLDivElement, StatCardProps>(
  ({ 
    title,
    value,
    icon: Icon,
    colorScheme = 'blue',
    trend,
    alert = false,
    loading = false,
    subtitle,
    className,
    onClick,
    ...props 
  }, ref) => {
    
    const getIconColorClass = () => {
      switch (colorScheme) {
        case 'blue':
          return 'text-primary';
        case 'amber':
          return 'text-primary';
        case 'purple':
          return 'text-primary';
        case 'green':
          return 'text-primary';
        default:
          return 'text-foreground';
      }
    };

    const getGlassStatClass = () => {
      switch (colorScheme) {
        case 'blue':
          return 'glass-stat glass-stat-blue';
        case 'amber':
          return 'glass-stat glass-stat-amber';
        case 'purple':
          return 'glass-stat glass-stat-purple';
        case 'green':
          return 'glass-stat glass-stat-green';
        default:
          return 'glass-stat glass-stat-blue';
      }
    };

    return (
      <div
        ref={ref}
        className={cn(
          "overflow-hidden group h-20 w-full min-w-0 rounded-xl transition-all duration-300", // Increased height from h-16 to h-20
          getGlassStatClass(),
          alert && "ring-2 ring-destructive/50 shadow-destructive/20",
          onClick && "cursor-pointer hover-lift",
          className
        )}
        onClick={onClick}
        {...props}
      >
        {/* Enhanced Layout - Icon + Content + Trend */}
        <div className="flex items-center h-full px-4 gap-3">
          {/* Icon Section */}
          <div className="relative flex-shrink-0">
            <div className="p-2 rounded-lg bg-white/20 dark:bg-black/20 backdrop-blur-sm transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
              <Icon className={cn("h-5 w-5", getIconColorClass())} />
            </div>
            {alert && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full animate-pulse shadow-lg" />
            )}
          </div>

          {/* Enhanced Content Section */}
          <div className="flex-1 min-w-0 overflow-hidden">
            <div className="space-y-0.5">
              <p className="text-xs font-semibold text-glass-muted uppercase tracking-wide truncate">
                {title}
              </p>
              <p className="text-xl font-bold text-glass transition-all duration-300 group-hover:scale-105">
                {loading ? (
                  <span className="inline-block w-12 h-5 bg-muted animate-pulse rounded" />
                ) : (
                  value
                )}
              </p>
              {subtitle && (
                <p className="text-xs text-glass-muted leading-tight truncate">
                  {subtitle}
                </p>
              )}
            </div>
          </div>

          {/* Enhanced Trend Section */}
          {trend && (
            <div className={cn(
              "flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-sm font-semibold flex-shrink-0 backdrop-blur-sm",
              trend.isPositive
                ? "status-success"
                : "status-error"
            )}>
              <span className="text-sm">
                {trend.isPositive ? "↗" : "↘"}
              </span>
              <span>{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>

        {/* Enhanced gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-transparent to-white/5 dark:from-white/5 dark:via-transparent dark:to-white/2 pointer-events-none" />
      </div>
    );
  }
);

StatCard.displayName = 'StatCard';

export default StatCard;
