import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-300 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transform active:scale-95 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:scale-105 active:scale-95",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-to-r from-primary to-primary/90 text-primary-foreground shadow-lg hover:shadow-xl hover:from-primary/95 hover:to-primary/80 backdrop-blur-sm",
        destructive:
          "bg-gradient-to-r from-destructive to-destructive/90 text-destructive-foreground shadow-lg hover:shadow-xl hover:from-destructive/95 hover:to-destructive/80 backdrop-blur-sm",
        outline:
          "border border-white/30 dark:border-white/20 bg-white/10 dark:bg-black/10 text-foreground backdrop-blur-xl hover:bg-white/20 dark:hover:bg-black/20 hover:border-white/40 dark:hover:border-white/30 shadow-lg hover:shadow-xl",
        secondary:
          "bg-gradient-to-r from-white/20 to-white/10 dark:from-black/20 dark:to-black/10 text-secondary-foreground hover:from-white/30 hover:to-white/20 dark:hover:from-black/30 dark:hover:to-black/20 backdrop-blur-xl border border-white/30 dark:border-white/20 shadow-lg hover:shadow-xl",
        ghost:
          "text-foreground hover:bg-white/20 dark:hover:bg-black/20 hover:text-accent-foreground backdrop-blur-sm transition-all duration-300 border border-transparent hover:border-white/30 dark:hover:border-white/20",
        link: "text-primary underline-offset-4 hover:underline",
        glass:
          "glass-card glass-card-hover text-foreground backdrop-blur-xl shadow-lg hover:shadow-2xl",
        glass_primary:
          "bg-gradient-to-r from-primary/30 to-primary/15 dark:from-primary/25 dark:to-primary/10 text-primary-foreground backdrop-blur-xl border border-primary/30 dark:border-primary/20 shadow-lg hover:shadow-xl hover:from-primary/40 hover:to-primary/25",
        glass_secondary:
          "bg-gradient-to-r from-white/25 to-white/10 dark:from-black/25 dark:to-black/10 text-foreground backdrop-blur-xl border border-white/30 dark:border-white/20 shadow-lg hover:shadow-xl hover:from-white/35 hover:to-white/15 dark:hover:from-black/35 dark:hover:to-black/15",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-lg px-8",
        icon: "h-10 w-10",
        xl: "h-12 rounded-xl px-10 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
