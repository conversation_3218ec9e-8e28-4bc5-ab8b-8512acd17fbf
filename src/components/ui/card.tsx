import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const cardVariants = cva(
  "rounded-xl text-card-foreground transition-all duration-300 ease-in-out",
  {
    variants: {
      variant: {
        default:
          "bg-card border border-border shadow-sm hover:shadow-md",
        glass:
          "glass-card glass-card-hover shadow-lg hover:shadow-2xl",
        glass_interactive:
          "glass-interactive cursor-pointer active:scale-[0.98]",
        glass_floating:
          "glass-card rounded-2xl shadow-2xl hover:shadow-3xl bg-gradient-to-br from-white/25 to-white/10 dark:from-black/25 dark:to-black/10",
        outline:
          "border border-white/30 dark:border-white/20 bg-white/10 dark:bg-black/10 backdrop-blur-xl shadow-lg hover:shadow-xl",
        subtle:
          "bg-white/5 dark:bg-black/5 backdrop-blur-sm border border-white/20 dark:border-white/10 shadow-md hover:shadow-lg",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
        none: "p-0",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  interactive?: boolean
  hoverEffect?: boolean
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, interactive, hoverEffect, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        cardVariants({ variant, size }),
        interactive && "cursor-pointer active:scale-[0.98]",
        hoverEffect && "hover:scale-[1.02]",
        className
      )}
      {...props}
    />
  )
)
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex flex-col space-y-1.5 p-6 pb-4",
      "bg-gradient-to-r from-transparent via-white/5 to-transparent dark:via-black/5",
      "border-b border-white/10 dark:border-white/5",
      className
    )}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      "text-foreground/95 font-medium",
      "bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn(
      "text-sm text-muted-foreground/80 leading-relaxed",
      "text-body font-normal",
      className
    )}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div 
    ref={ref} 
    className={cn(
      "p-6 pt-4",
      "text-body leading-relaxed",
      className
    )} 
    {...props} 
  />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex items-center p-6 pt-4",
      "bg-gradient-to-r from-transparent via-white/5 to-transparent dark:via-black/5",
      "border-t border-white/10 dark:border-white/5",
      className
    )}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, cardVariants }
