
import React, { useState, useEffect } from 'react';
import { Package, ShoppingCart, AlertTriangle, Trash2, Image as ImageIcon } from 'lucide-react';
import GlassCard from '../ui/GlassCard';
import { cn } from '@/lib/utils';
import { FormattedInventoryItem } from '@/types/inventory';
import { Button } from '@/components/ui/button';
import InventoryImage from './InventoryImage';
import { useIsMobile } from '@/hooks/use-mobile';

interface InventoryCardProps {
  item: FormattedInventoryItem;
  onClick: () => void;
  selected?: boolean;
  showDelete?: boolean;
  onDeleteClick?: (e: React.MouseEvent) => void;
}

const InventoryCard: React.FC<InventoryCardProps> = ({
  item,
  onClick,
  selected = false,
  showDelete = false,
  onDeleteClick
}) => {
  const isMobile = useIsMobile();
  const lowStock = item.quantity < item.minQuantity;

  // Handle delete button click
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    if (onDeleteClick) {
      onDeleteClick(e);
    }
  };

  if (isMobile) {
    // Mobile-optimized layout: horizontal card with better information hierarchy
    return (
      <GlassCard
        onClick={onClick}
        className={cn(
          "relative transition-all cursor-pointer active:scale-95",
          selected && "ring-2 ring-primary",
          "flex gap-3 p-3 min-h-[100px]" // Horizontal layout
        )}
      >
        {/* Mobile Image section - smaller, left-aligned */}
        <div className="relative w-20 h-20 bg-slate-100 rounded-lg overflow-hidden flex-shrink-0">
          <InventoryImage
            imageUrl={item.imageUrl}
            amazonUrl={item.amazonUrl}
            itemName={item.name}
            className="w-full h-full object-contain p-1"
            size="sm"
          />
          
          {/* Low stock indicator - smaller */}
          {lowStock && (
            <div className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1">
              <AlertTriangle className="h-3 w-3 text-white" />
            </div>
          )}
        </div>

        {/* Mobile Content section - takes remaining space */}
        <div className="flex-1 min-w-0 flex flex-col justify-center">
          {/* Title and price row */}
          <div className="flex items-start justify-between gap-2 mb-2">
            <h3 className="font-medium text-sm leading-tight line-clamp-2 flex-1">{item.name}</h3>
            {item.price !== undefined && item.price > 0 && (
              <div className="text-sm font-semibold text-slate-900 flex-shrink-0">
                ${item.price.toFixed(2)}
              </div>
            )}
          </div>

          {/* Quantity and status */}
          <div className="flex items-center gap-2 mb-2">
            <div className={cn(
              "px-2 py-1 rounded-md text-xs font-medium",
              lowStock ? "bg-red-100 text-red-700" : "bg-green-100 text-green-700"
            )}>
              {item.quantity} {item.quantity === 1 ? 'item' : 'items'}
            </div>
            {lowStock && (
              <span className="text-xs text-red-600 font-medium">Low Stock</span>
            )}
          </div>

          {/* Bottom row - collection and links */}
          <div className="flex items-center justify-between">
            {/* Collection tag */}
            <div className="flex-1">
              {item.collection && (
                <span className="text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded">
                  {item.collection}
                </span>
              )}
            </div>

            {/* External links - larger touch targets */}
            <div className="flex items-center gap-3">
              {item.amazonUrl && (
                <a
                  href={item.amazonUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 hover:bg-orange-50 rounded-lg transition-colors"
                  onClick={(e) => e.stopPropagation()}
                >
                  <img
                    src="/amazon-icon.svg"
                    alt="Amazon"
                    className="h-5 w-auto"
                  />
                </a>
              )}
              
              {item.walmartUrl && (
                <a
                  href={item.walmartUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 hover:bg-blue-50 rounded-lg transition-colors"
                  onClick={(e) => e.stopPropagation()}
                >
                  <img
                    src="/walmart-icon.svg"
                    alt="Walmart"
                    className="h-5 w-auto"
                  />
                </a>
              )}
            </div>
          </div>
        </div>

        {/* Delete button - mobile optimized */}
        {showDelete && (
          <Button
            size="icon"
            variant="destructive"
            className="absolute top-2 right-2 h-8 w-8 rounded-full opacity-90"
            onClick={handleDeleteClick}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </GlassCard>
    );
  }

  // Desktop layout: keep original card design but with improvements
  return (
    <GlassCard
      onClick={onClick}
      className={cn(
        "relative transition-all transform cursor-pointer hover:shadow-md group",
        selected && "ring-2 ring-primary",
        "h-full flex flex-col"
      )}
    >
      {/* Image section */}
      <div className="relative w-full aspect-square surface-secondary rounded-t-lg overflow-hidden">
        <InventoryImage
          imageUrl={item.imageUrl}
          amazonUrl={item.amazonUrl}
          itemName={item.name}
          className="w-full h-full object-contain p-2 group-hover:scale-105 transition-transform"
          size="lg"
        />

        {/* Low stock indicator */}
        {lowStock && (
          <div className="absolute bottom-2 right-2 status-error p-1.5 rounded-full shadow-sm">
            <AlertTriangle className="h-4 w-4" />
          </div>
        )}

        {/* Delete button */}
        {showDelete && (
          <Button
            size="icon"
            variant="destructive"
            className="absolute top-2 right-2 h-9 w-9 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={handleDeleteClick}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Content section */}
      <div className="p-4 flex-grow flex flex-col">
        <h3 className="font-medium text-base line-clamp-2 mb-3">{item.name}</h3>

        <div className="mt-auto space-y-3">
          {/* Quantity and price row */}
          <div className="flex items-center justify-between">
            <div className={cn(
              "px-3 py-1.5 rounded-lg text-sm font-medium",
              lowStock ? "status-error" : "status-success"
            )}>
              {item.quantity} {item.quantity === 1 ? 'item' : 'items'}
            </div>

            {item.price !== undefined && item.price > 0 && (
              <div className="text-base font-semibold text-primary">
                ${item.price.toFixed(2)}
              </div>
            )}
          </div>

          {/* External links */}
          <div className="flex items-center gap-3">
            {item.amazonUrl && (
              <a
                href={item.amazonUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 hover:bg-orange-50 rounded-lg transition-colors"
                onClick={(e) => e.stopPropagation()}
              >
                <img
                  src="/amazon-icon.svg"
                  alt="Amazon"
                  className="h-5 w-auto"
                />
              </a>
            )}

            {item.walmartUrl && (
              <a
                href={item.walmartUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 hover:bg-blue-50 rounded-lg transition-colors"
                onClick={(e) => e.stopPropagation()}
              >
                <img
                  src="/walmart-icon.svg"
                  alt="Walmart"
                  className="h-5 w-auto"
                />
              </a>
            )}
          </div>

          {/* Collection tag */}
          {item.collection && (
            <div className="pt-3 border-t border-slate-100">
              <span className="text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded">
                {item.collection}
              </span>
            </div>
          )}
        </div>
      </div>
    </GlassCard>
  );
};

export default InventoryCard;
