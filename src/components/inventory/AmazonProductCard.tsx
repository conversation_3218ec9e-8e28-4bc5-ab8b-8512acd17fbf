import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Package, Star, ShoppingCart } from 'lucide-react';
import { AmazonProduct } from './types';

interface AmazonProductCardProps {
  product: AmazonProduct;
  onSelect: (product: AmazonProduct) => void;
  selected?: boolean;
}

const AmazonProductCard: React.FC<AmazonProductCardProps> = ({
  product,
  onSelect,
  selected = false,
}) => {
  return (
    <Card className={`relative overflow-hidden transition-all ${
      selected ? 'ring-2 ring-primary' : ''
    }`}>
      <div className="relative aspect-square overflow-hidden bg-muted">
        <img
          src={product.img}
          alt={product.title}
          className="object-contain w-full h-full p-4"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder.svg';
          }}
        />
        {product.isPrime && (
          <div className="absolute top-2 right-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
            Prime
          </div>
        )}
      </div>

      <div className="p-4">
        <h3 className="font-medium text-sm line-clamp-2 mb-2" title={product.title}>
          {product.title}
        </h3>

        <div className="flex items-center justify-between mb-2">
          <div className="font-medium">
            {product.price || 'Price not available'}
          </div>
          <div className="flex items-center text-xs text-muted-foreground">
            <Star className="h-3 w-3 fill-yellow-400 stroke-yellow-400 mr-1" />
            {product.rating.toFixed(1)} ({product.reviewCount})
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant={selected ? "secondary" : "outline"}
            size="sm"
            className="flex-1"
            onClick={() => onSelect(product)}
          >
            {selected ? 'Selected' : 'Select'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(`https://www.amazon.com/dp/${product.asin}`, '_blank')}
          >
            <ShoppingCart className="h-4 w-4" />
          </Button>
        </div>

        <div className="mt-2 text-xs text-muted-foreground">
          ASIN: {product.asin}
        </div>
      </div>
    </Card>
  );
};

export default AmazonProductCard;