import React, { useMemo } from 'react';
import { MaintenanceTask, MaintenanceSeverity } from './types';

interface PrintableMaintenanceListProps {
  tasks: MaintenanceTask[];
  filters: {
    status?: string;
    severity?: string;
    provider?: string;
    property?: string;
    showCompleted: boolean;
    sortField: string;
    sortDirection: string;
  };
  providers: Array<{ id: string; name: string }>;
  properties: Array<{ id: string; name: string }>;
}

const PrintableMaintenanceList: React.FC<PrintableMaintenanceListProps> = ({
  tasks,
  filters,
  providers,
  properties
}) => {
  // Apply the same filters as in the main list
  const filteredTasks = useMemo(() => {
    // Start with all tasks and deduplicate by ID
    const uniqueTasksMap = new Map();

    // First, sort tasks by ID to ensure consistent order
    const sortedTasks = [...tasks].sort((a, b) => {
      // If IDs are the same, use another property to determine order
      if (a.id === b.id) {
        // Use creation date or any other property that makes sense
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      }
      return a.id.localeCompare(b.id);
    });

    // Then add to map to deduplicate
    sortedTasks.forEach(task => {
      // Only add the task if it's not already in the map
      if (!uniqueTasksMap.has(task.id)) {
        uniqueTasksMap.set(task.id, task);
      }
    });

    // Convert the map back to an array
    let filtered = Array.from(uniqueTasksMap.values());

    // Filter by status if a status filter is selected
    if (filters.status) {
      filtered = filtered.filter(task => task.status === filters.status);
    }

    // Filter by severity if a severity filter is selected
    if (filters.severity) {
      filtered = filtered.filter(task => task.severity === filters.severity);
    }

    // Filter by provider if a provider filter is selected
    if (filters.provider) {
      filtered = filtered.filter(task => task.providerId === filters.provider);
    }

    // Filter by property if a property filter is selected
    if (filters.property) {
      filtered = filtered.filter(task => task.propertyId === filters.property);
    }

    // Hide completed tasks unless explicitly showing them or filtering for them
    if (!filters.showCompleted && filters.status !== 'completed') {
      filtered = filtered.filter(task =>
        task.status !== 'completed' &&
        task.status !== 'cancelled'
      );
    }

    // Sort the tasks
    return filtered.sort((a, b) => {
      const { sortField, sortDirection } = filters;
      const multiplier = sortDirection === 'asc' ? 1 : -1;

      switch (sortField) {
        case 'dueDate':
          // Handle 'No due date' special case
          if (a.dueDate === 'No due date' && b.dueDate !== 'No due date') return multiplier;
          if (a.dueDate !== 'No due date' && b.dueDate === 'No due date') return -multiplier;
          if (a.dueDate === 'No due date' && b.dueDate === 'No due date') return 0;
          return multiplier * (new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());

        case 'severity':
          // Map severity to numeric values for sorting
          const severityMap: Record<MaintenanceSeverity, number> = {
            critical: 4,
            high: 3,
            medium: 2,
            low: 1
          };
          return multiplier * (severityMap[a.severity as MaintenanceSeverity] - severityMap[b.severity as MaintenanceSeverity]);

        case 'createdAt':
          return multiplier * (new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

        case 'status':
          // Map status to numeric values for sorting
          const statusMap: Record<string, number> = {
            new: 1,
            assigned: 2,
            accepted: 3,
            in_progress: 4,
            completed: 5,
            cancelled: 6
          };
          return multiplier * ((statusMap[a.status] || 0) - (statusMap[b.status] || 0));

        default:
          return 0;
      }
    });
  }, [tasks, filters]);
  // Get the current date for the report header
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Get filter descriptions for the report header
  const getFilterDescription = () => {
    const filterParts = [];

    if (filters.status) {
      filterParts.push(`Status: ${filters.status}`);
    }

    if (filters.severity) {
      filterParts.push(`Priority: ${filters.severity}`);
    }

    if (filters.provider) {
      const providerName = providers.find(p => p.id === filters.provider)?.name || 'Unknown';
      filterParts.push(`Provider: ${providerName}`);
    }

    if (filters.property) {
      const propertyName = properties.find(p => p.id === filters.property)?.name || 'Unknown';
      filterParts.push(`Property: ${propertyName}`);
    }

    if (!filters.showCompleted) {
      filterParts.push('Excluding completed/cancelled tasks');
    }

    return filterParts.join(' • ');
  };

  // Get severity class for styling (print-friendly colors)
  const getSeverityClass = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-800 font-bold';
      case 'high': return 'text-orange-800 font-semibold';
      case 'medium': return 'text-amber-800';
      case 'low': return 'text-green-800';
      default: return 'text-gray-800';
    }
  };

  // Get status class for styling (print-friendly colors)
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'new': return 'text-blue-800';
      case 'assigned': return 'text-amber-800';
      case 'in_progress': return 'text-purple-800';
      case 'completed': return 'text-green-800';
      case 'cancelled': return 'text-gray-800';
      case 'accepted': return 'text-emerald-800';
      case 'rejected': return 'text-red-800';
      default: return 'text-gray-800';
    }
  };

  return (
    <div className="print-container p-8 max-w-full">
      {/* Report Header */}
      <div className="mb-8 text-center">
        <h1 className="text-2xl font-bold mb-3">Maintenance Tasks Report</h1>
        <p className="text-gray-600 mb-2">Generated on {currentDate}</p>
        {getFilterDescription() && (
          <p className="text-sm text-gray-500 mb-2">{getFilterDescription()}</p>
        )}
        <p className="text-xs text-gray-400 mt-2 no-print">This report shows the {filteredTasks.length} maintenance tasks matching your current filters</p>
      </div>

      {/* Tasks Table */}
      <table className="w-full border-collapse" style={{ pageBreakInside: 'auto' }} data-html2pdf-pagebreak="avoid-all">
        <thead style={{ display: 'table-header-group' }}>
          <tr className="bg-gray-100">
            <th className="border px-4 py-3 text-left">Task</th>
            <th className="border px-4 py-3 text-left">Property</th>
            <th className="border px-4 py-3 text-left">Priority</th>
            <th className="border px-4 py-3 text-left">Status</th>
            <th className="border px-4 py-3 text-left">Due Date</th>
          </tr>
        </thead>
        <tbody>
          {filteredTasks.length === 0 ? (
            <tr>
              <td colSpan={5} className="border px-4 py-4 text-center text-gray-500">
                No tasks found matching the current filters
              </td>
            </tr>
          ) : (
            filteredTasks.map((task) => (
              <tr
                key={task.id}
                className="border-b task-row"
                data-html2pdf-pagebreak="avoid"
                style={{
                  pageBreakInside: 'avoid !important',
                  pageBreakAfter: 'auto',
                  breakInside: 'avoid !important',
                  display: 'table-row'
                }}
              >
                <td className="border px-4 py-3" style={{ verticalAlign: 'top' }}>
                  <div className="font-medium mb-1">{task.title}</div>
                  <div className="text-sm text-gray-600">{task.description}</div>
                </td>
                <td className="border px-4 py-3" style={{ verticalAlign: 'top' }}>{task.propertyName || 'N/A'}</td>
                <td className={`border px-4 py-3 ${getSeverityClass(task.severity)}`} style={{ verticalAlign: 'top' }}>
                  {task.severity}
                </td>
                <td className={`border px-4 py-3 ${getStatusClass(task.status)}`} style={{ verticalAlign: 'top' }}>
                  {task.status}
                </td>
                <td className="border px-4 py-3" style={{ verticalAlign: 'top' }}>
                  {task.dueDate && task.dueDate !== 'No due date' ? task.dueDate : 'No due date'}
                </td>
              </tr>
            ))
          )}
        </tbody>
        <tfoot style={{ display: 'table-footer-group' }}>
          <tr>
            <td colSpan={5} className="border px-4 py-3 text-right text-sm text-gray-600">
              Total tasks: {filteredTasks.length}
            </td>
          </tr>
        </tfoot>
      </table>
    </div>
  );
};

export default PrintableMaintenanceList;
