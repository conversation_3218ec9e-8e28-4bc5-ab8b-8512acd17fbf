import React from 'react';
import { Shopping<PERSON>art, ListTodo, AlertTriangle, Calendar } from 'lucide-react';

interface CalendarLegendProps {
  className?: string;
}

const CalendarLegend: React.FC<CalendarLegendProps> = ({ className = '' }) => {
  return (
    <div className={`space-y-2 ${className}`}>
      <h4 className="text-sm font-medium text-muted-foreground mb-3">Calendar Legend</h4>
      
      <div className="space-y-2 text-xs">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded bg-primary/20 border border-primary/30 flex items-center justify-center">
            <ShoppingCart className="h-2 w-2 text-primary" />
          </div>
          <span className="text-primary font-medium">Bookings</span>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded status-info border flex items-center justify-center">
            <ListTodo className="h-2 w-2" />
          </div>
          <span className="font-medium">Maintenance</span>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded status-warning border flex items-center justify-center">
            <AlertTriangle className="h-2 w-2" />
          </div>
          <span className="font-medium">Damage Reports</span>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded status-error border-2 flex items-center justify-center">
            <Calendar className="h-2 w-2" />
          </div>
          <span className="font-medium">Multiple Events</span>
        </div>
      </div>
      
      <div className="mt-3 pt-2 border-t text-xs text-muted-foreground">
        <p>Click on any date to view detailed events</p>
      </div>
    </div>
  );
};

export default CalendarLegend;
