import React from 'react';
import { Property } from '@/hooks/useProperties';
import { MapPin, Bed, Bath, DollarSign, Calendar, Building } from 'lucide-react';

interface PropertySummaryProps {
  property: Property;
}

const PropertySummary: React.FC<PropertySummaryProps> = ({ property }) => {
  // For debugging
  console.log("PropertySummary rendering with data:", {
    is_occupied: property.is_occupied,
    current_checkout: property.current_checkout,
    next_checkin_formatted: property.next_checkin_formatted,
    next_checkin_date: property.next_checkin_date,
    next_booking: property.next_booking
  });

  // Function to extract check-in date from next_booking (format: "Month Day, Year - Month Day, Year")
  const parseNextCheckIn = () => {
    if (property.next_checkin_formatted) {
      return property.next_checkin_formatted;
    }
    
    if (property.next_booking) {
      // Try to extract the first date from "Month Day, Year - Month Day, Year" format
      const match = property.next_booking.match(/^([A-Za-z]+ \d+, \d{4})/);
      if (match && match[1]) {
        return match[1];
      }
      
      // If we can't extract it, return the whole booking string
      return property.next_booking;
    }
    
    return null;
  };

  const nextCheckIn = parseNextCheckIn();

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
      <div className="md:col-span-1">
        <div className="rounded-xl overflow-hidden">
          {property.image_url ? (
            <img 
              src={property.image_url} 
              alt={property.name} 
              className="w-full aspect-square object-cover rounded-xl" 
            />
          ) : (
            <div className="w-full aspect-square bg-muted flex items-center justify-center rounded-xl">
              <Building className="h-20 w-20 text-muted-foreground/50" />
            </div>
          )}
        </div>
      </div>
      
      <div className="md:col-span-3">
        <h1 className="text-3xl font-bold mb-2">{property.name}</h1>
        <div className="flex items-center text-muted-foreground mb-4">
          <MapPin className="h-4 w-4 mr-2" />
          <span>
            {property.address}, {property.city}, {property.state} {property.zip}
          </span>
        </div>
        
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="glass rounded-xl p-4 flex flex-col items-center justify-center">
            <Bed className="h-5 w-5 mb-1 text-primary" />
            <p className="text-lg font-semibold">{property.bedrooms}</p>
            <p className="text-xs text-muted-foreground">Bedrooms</p>
          </div>
          
          <div className="glass rounded-xl p-4 flex flex-col items-center justify-center">
            <Bath className="h-5 w-5 mb-1 text-primary" />
            <p className="text-lg font-semibold">{property.bathrooms}</p>
            <p className="text-xs text-muted-foreground">Bathrooms</p>
          </div>
          
          <div className="glass rounded-xl p-4 flex flex-col items-center justify-center">
            <DollarSign className="h-5 w-5 mb-1 text-primary" />
            <p className="text-lg font-semibold">${property.budget.toLocaleString()}</p>
            <p className="text-xs text-muted-foreground">Budget</p>
          </div>
        </div>
        
        <div className="space-y-3">
          {/* Show current booking if property is occupied */}
          {property.is_occupied && (
            <div className="glass-surface rounded-xl p-4 status-error border">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                <h3 className="font-medium">
                  Currently Occupied
                  {property.current_checkout && ` until ${property.current_checkout}`}
                </h3>
              </div>
            </div>
          )}
          
          {/* Always show next check-in or next booking */}
          <div className="glass rounded-xl p-4">
            <div className="flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-primary" />
              {nextCheckIn ? (
                <h3 className="font-medium">Next Check-in: {nextCheckIn}</h3>
              ) : (
                <h3 className="font-medium">No upcoming bookings</h3>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertySummary;
