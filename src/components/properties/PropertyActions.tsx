
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Pencil, Trash2 } from 'lucide-react';
import { usePermissions } from '@/hooks/usePermissions';
import { PermissionType } from '@/types/auth';

interface PropertyActionsProps {
  propertyId: string;
  onEdit: () => void;
}

const PropertyActions: React.FC<PropertyActionsProps> = ({ propertyId, onEdit }) => {
  const navigate = useNavigate();
  const { hasPermission } = usePermissions();

  const handleDelete = async () => {
    if (!hasPermission(PermissionType.MANAGE_PROPERTIES)) {
      setTimeout(() => {
        toast.error('You do not have permission to delete properties');
      }, 0);
      return;
    }

    const confirmed = window.confirm('Are you sure you want to delete this property? This action cannot be undone. All associated inventory items, maintenance tasks, and damage reports will also be deleted.');
    if (!confirmed) return;

    try {
      setTimeout(() => {
        toast.loading('Deleting property and associated data...', { id: 'delete-property' });
      }, 0);

      // Use the RPC function to delete the property and all related data
      const { data, error } = await supabase.rpc('delete_property_cascade', {
        property_id_param: propertyId
      });

      if (error) {
        console.error('Error in delete_property_cascade function:', error);
        setTimeout(() => {
          toast.error(`Failed to delete property: ${error.message}`, { id: 'delete-property' });
        }, 0);

        // Fallback to simple deletion
        console.log('Falling back to simple deletion...');

        try {
          // Try simple property deletion
          const { error: simpleError } = await supabase
            .from('properties')
            .delete()
            .eq('id', propertyId);

          if (simpleError) {
            console.error('Error with simple deletion:', simpleError);
            setTimeout(() => {
              toast.error(`Failed to delete property: ${simpleError.message}`, { id: 'delete-property' });
            }, 0);
            return;
          }

          setTimeout(() => {
            toast.success('Property deleted successfully', { id: 'delete-property' });
          }, 0);
          navigate('/properties');
        } catch (fallbackError: any) {
          console.error('Error in fallback deletion:', fallbackError);
          setTimeout(() => {
            toast.error(`Failed to delete property: ${fallbackError.message || 'Unknown error'}`, { id: 'delete-property' });
          }, 0);
        }
        return;
      }

      setTimeout(() => {
        toast.success('Property and all associated data deleted successfully', { id: 'delete-property' });
      }, 0);
      navigate('/properties');
    } catch (error: any) {
      console.error('Error deleting property:', error);
      setTimeout(() => {
        toast.error(`Failed to delete property: ${error.message || 'Unknown error'}`, { id: 'delete-property' });
      }, 0);
    }
  };

  // Log permission status for debugging
  const hasManagePermission = hasPermission(PermissionType.MANAGE_PROPERTIES);
  console.log('User has MANAGE_PROPERTIES permission:', hasManagePermission);

  return (
    <div className="flex space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={onEdit}
        disabled={!hasManagePermission}
      >
        <Pencil className="h-4 w-4 mr-1" />
        Edit
      </Button>
      <Button
        variant="destructive"
        size="sm"
        onClick={handleDelete}
        disabled={!hasManagePermission}
      >
        <Trash2 className="h-4 w-4 mr-1" />
        Delete
      </Button>
    </div>
  );
};

export default PropertyActions;
