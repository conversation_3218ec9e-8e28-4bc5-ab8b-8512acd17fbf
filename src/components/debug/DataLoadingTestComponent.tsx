import React, { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DataLoadingTester, DataLoadingTestResult } from '@/utils/dataLoadingTest';
import { RefreshCw, Play, CheckCircle, XCircle, AlertCircle, ExternalLink } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

/**
 * A component for testing data loading functionality
 * This helps verify that the fixes for background/foreground data loading work correctly
 */
const DataLoadingTestComponent: React.FC = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [testResults, setTestResults] = useState<DataLoadingTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [lastRunTime, setLastRunTime] = useState<Date | null>(null);

  const runTests = async () => {
    setIsRunning(true);
    try {
      const tester = new DataLoadingTester(queryClient);
      const results = await tester.runAllTests();
      setTestResults(results);
      setLastRunTime(new Date());
      
      // Also log to console for debugging
      tester.logResults();
    } catch (error) {
      console.error('Error running data loading tests:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const simulateWindowFocus = () => {
    console.log('🔄 Simulating window focus event...');
    window.dispatchEvent(new Event('focus'));
  };

  const simulateVisibilityChange = () => {
    console.log('🔄 Simulating visibility change event...');
    Object.defineProperty(document, 'visibilityState', {
      writable: true,
      value: 'visible'
    });
    document.dispatchEvent(new Event('visibilitychange'));
  };

  const invalidateAllQueries = async () => {
    console.log('🔄 Invalidating all queries...');
    await queryClient.invalidateQueries();
  };

  const fixStuckQueries = async () => {
    console.log('🔧 Fixing stuck queries...');
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();

    // Find and fix stuck pending queries
    const stuckQueries = queries.filter(q =>
      q.state.status === 'pending' &&
      q.state.dataUpdatedAt === 0
    );

    console.log(`Found ${stuckQueries.length} potentially stuck queries`);

    for (const query of stuckQueries) {
      console.log('Aggressively fixing stuck query:', query.queryKey);

      // More aggressive approach
      try {
        // Cancel any ongoing requests
        await queryClient.cancelQueries({ queryKey: query.queryKey });

        // Remove the query completely
        queryClient.removeQueries({ queryKey: query.queryKey });

        // Invalidate to trigger fresh fetch
        await queryClient.invalidateQueries({ queryKey: query.queryKey });

        console.log('Successfully fixed query:', query.queryKey);
      } catch (error) {
        console.error('Error fixing query:', query.queryKey, error);
      }
    }
  };

  const cleanupDuplicateQueries = async () => {
    console.log('🧹 Cleaning up duplicate queries...');
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();

    // Group queries by their first key element
    const queryGroups = queries.reduce((acc, query) => {
      const key = query.queryKey[0];
      if (!acc[key]) acc[key] = [];
      acc[key].push(query);
      return acc;
    }, {} as Record<string, any[]>);

    // Find and remove duplicate queries (keep the most recent successful one)
    let removedCount = 0;
    for (const [key, groupQueries] of Object.entries(queryGroups)) {
      if (groupQueries.length > 1) {
        console.log(`Found ${groupQueries.length} instances of query key: ${key}`);

        // Sort by success status and last updated time
        const sortedQueries = groupQueries.sort((a, b) => {
          if (a.state.status === 'success' && b.state.status !== 'success') return -1;
          if (b.state.status === 'success' && a.state.status !== 'success') return 1;
          return b.state.dataUpdatedAt - a.state.dataUpdatedAt;
        });

        // Remove all but the best query
        for (let i = 1; i < sortedQueries.length; i++) {
          queryClient.removeQueries({ queryKey: sortedQueries[i].queryKey });
          removedCount++;
        }
      }
    }

    console.log(`Removed ${removedCount} duplicate queries`);
  };

  const navigateToDataPage = (page: string) => {
    console.log(`🔄 Navigating to ${page} to load data...`);
    navigate(page);
  };

  const fixOperationsQueries = async () => {
    console.log('🔧 Specifically fixing operations queries...');

    const operationsQueries = ['operationsData', 'operationsProperties'];

    for (const queryKey of operationsQueries) {
      try {
        console.log(`Fixing operations query: ${queryKey}`);

        // Cancel any ongoing requests
        await queryClient.cancelQueries({ queryKey: [queryKey] });

        // Remove the stuck query
        queryClient.removeQueries({ queryKey: [queryKey] });

        // Wait a moment
        await new Promise(resolve => setTimeout(resolve, 500));

        // Invalidate to trigger fresh fetch
        await queryClient.invalidateQueries({
          queryKey: [queryKey],
          exact: false
        });

        console.log(`Fixed operations query: ${queryKey}`);
      } catch (error) {
        console.error(`Error fixing operations query ${queryKey}:`, error);
      }
    }

    console.log('Operations queries fixed - try navigating to Operations page');
  };

  const resetAllQueries = async () => {
    console.log('🔄 NUCLEAR OPTION: Resetting ALL queries...');

    try {
      // Cancel all ongoing queries
      await queryClient.cancelQueries();

      // Remove all queries from cache
      queryClient.removeQueries();

      // Clear the entire cache
      queryClient.clear();

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Force garbage collection if available
      if (window.gc) {
        window.gc();
      }

      console.log('All queries cleared - navigate to any page to reload data');

      // Automatically run the test after reset
      setTimeout(() => {
        runTests();
      }, 2000);
    } catch (error) {
      console.error('Error resetting all queries:', error);
    }
  };

  const { authState } = useAuth(); // Move hook to component level
  const userId = authState.user?.id;

  const debugQueryState = () => {
    console.log('🔍 DEBUGGING QUERY STATE...');

    console.log('Auth State:', {
      isAuthenticated: authState.isAuthenticated,
      isLoading: authState.isLoading,
      userId: userId,
      userIdType: typeof userId,
      userIdTruthy: !!userId,
      hasUser: !!authState.user,
      hasProfile: !!authState.profile
    });

    // Check specific query states
    const propertiesV2State = queryClient.getQueryState(['propertiesV2']);
    const propertiesState = queryClient.getQueryState(['properties']);

    console.log('PropertiesV2 Query State:', propertiesV2State);
    console.log('Properties Query State:', propertiesState);

    // Check all queries in cache
    const allQueries = queryClient.getQueryCache().getAll();
    console.log('All Queries in Cache:', allQueries.map(q => ({
      queryKey: q.queryKey,
      status: q.state.status,
      fetchStatus: q.state.fetchStatus,
      dataUpdatedAt: q.state.dataUpdatedAt,
      isStale: q.state.isStale
    })));
  };

  const testDirectSupabaseCall = async () => {
    console.log('🧪 TESTING DIRECT SUPABASE CALL...');

    if (!userId) {
      console.error('No userId available for direct test');
      return;
    }

    try {
      console.log(`Testing direct RPC call with userId: ${userId}`);

      const { data, error } = await supabase.rpc(
        'get_user_role_properties',
        { p_user_id: userId }
      );

      if (error) {
        console.error('Direct RPC call failed:', error);
      } else {
        console.log(`Direct RPC call succeeded! Got ${data?.length || 0} properties:`, data);
      }

      // Also test a simple table query
      const { data: tableData, error: tableError } = await supabase
        .from('properties')
        .select('id, name')
        .eq('user_id', userId)
        .limit(5);

      if (tableError) {
        console.error('Direct table query failed:', tableError);
      } else {
        console.log(`Direct table query succeeded! Got ${tableData?.length || 0} properties:`, tableData);
      }

    } catch (error) {
      console.error('Exception in direct Supabase test:', error);
    }
  };

  const testSimpleReactQuery = () => {
    console.log('🧪 TESTING SIMPLE REACT QUERY...');

    // Create a simple test query
    queryClient.fetchQuery({
      queryKey: ['test-query'],
      queryFn: async () => {
        console.log('🔥 TEST QUERY EXECUTING!');
        return { test: 'data', timestamp: Date.now() };
      }
    }).then(result => {
      console.log('✅ Test query succeeded:', result);
    }).catch(error => {
      console.error('❌ Test query failed:', error);
    });
  };

  const testComponentMounting = () => {
    console.log('🧪 TESTING COMPONENT MOUNTING...');
    console.log('📋 Expected component mount logs to look for:');
    console.log('  🏠 Properties component mounting...');
    console.log('  📊 Dashboard component mounting...');
    console.log('  👥 TeamDashboard component mounting...');
    console.log('  🔧 Maintenance component mounting...');
    console.log('');
    console.log('📋 Navigate to each page and check if you see these logs:');
    console.log('  1. Go to /properties');
    console.log('  2. Go to /dashboard');
    console.log('  3. Go to /teams');
    console.log('  4. Go to /maintenance');
    console.log('');
    console.log('❌ If you DON\'T see these logs, the components are not mounting!');
    console.log('✅ If you DO see these logs, the components are mounting but hooks aren\'t working');
  };

  const testNetworkConnectivity = async () => {
    console.log('🌐 TESTING NETWORK CONNECTIVITY...');

    try {
      // Test 1: Basic fetch to Supabase
      console.log('🧪 Testing basic fetch to Supabase...');
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const supabaseKey = import.meta.env.VITE_SUPABASE_PUBLISHABLE_KEY;
      
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Missing Supabase environment variables');
      }
      
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'GET',
        headers: {
          'apikey': supabaseKey
        }
      });

      if (response.ok) {
        console.log('✅ Basic Supabase connectivity works!');
      } else {
        console.error('❌ Supabase returned error:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('❌ Network connectivity test failed:', error);
      console.error('🚨 This is likely the root cause of all data loading issues!');
      console.error('💡 Possible solutions:');
      console.error('  1. Check if you\'re behind a firewall blocking Supabase');
      console.error('  2. Check your internet connection');
      console.error('  3. Try accessing https://pwaeknalhosfwuxkpaet.supabase.co directly');
    }
  };

  const forceInitializeQueries = async () => {
    console.log('🚀 Force initializing all page queries...');

    // Define all possible queries that should be initialized
    const allQueries = [
      'propertiesV2', 'properties',
      'maintenanceTasksV2', 'maintenanceTasks',
      'damageReportsV2', 'damageReports',
      'inventoryV2', 'inventory',
      'teamsV2', 'teams',
      'purchaseOrders',
      'dashboardProperties', 'dashboardMaintenanceTasks',
      'dashboardInventoryItems', 'dashboardDamages', 'dashboardPurchaseOrders',
      'operationsData', 'operationsProperties'
    ];

    console.log('Force initializing all queries...');

    for (const queryKey of allQueries) {
      try {
        // More aggressive approach for stuck queries
        console.log(`Force initializing query: ${queryKey}`);

        // Cancel any ongoing requests
        await queryClient.cancelQueries({ queryKey: [queryKey] });

        // Remove existing query
        queryClient.removeQueries({ queryKey: [queryKey] });

        // Invalidate to trigger fresh fetch
        await queryClient.invalidateQueries({
          queryKey: [queryKey],
          exact: false
        });

        console.log(`Successfully initialized query: ${queryKey}`);
      } catch (error) {
        console.error(`Error initializing query ${queryKey}:`, error);
      }
    }

    console.log('All queries force initialized - they should now work properly');
  };

  const getTestIcon = (result: DataLoadingTestResult) => {
    if (result.success) {
      return <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />;
    } else {
      return <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />;
    }
  };

  const getTestBadge = (result: DataLoadingTestResult) => {
    if (result.success) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Passed</Badge>;
    } else {
      return <Badge variant="destructive">Failed</Badge>;
    }
  };

  const summary = testResults.length > 0 ? {
    passed: testResults.filter(r => r.success).length,
    failed: testResults.filter(r => !r.success).length,
    total: testResults.length
  } : null;

  // Auto-run tests on component mount
  useEffect(() => {
    runTests();
  }, []);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-foreground" />
            Data Loading Test Suite
          </CardTitle>
          <CardDescription>
            Test and verify that data loading works correctly when the app returns from background.
            This helps diagnose the persistent data loading issues.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={runTests}
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              {isRunning ? (
                <RefreshCw className="h-4 w-4 animate-spin text-foreground" />
              ) : (
                <Play className="h-4 w-4 text-foreground" />
              )}
              {isRunning ? 'Running Tests...' : 'Run All Tests'}
            </Button>

            <Button
              variant="outline"
              onClick={simulateWindowFocus}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4 text-foreground" />
              Simulate Focus
            </Button>

            <Button
              variant="outline"
              onClick={simulateVisibilityChange}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4 text-foreground" />
              Simulate Visibility
            </Button>

            <Button
              variant="outline"
              onClick={invalidateAllQueries}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4 text-foreground" />
              Invalidate Queries
            </Button>

            <Button
              variant="outline"
              onClick={fixStuckQueries}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4 text-foreground" />
              Fix Stuck Queries
            </Button>

            <Button
              variant="outline"
              onClick={cleanupDuplicateQueries}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4 text-foreground" />
              Clean Duplicates
            </Button>

            <Button
              variant="outline"
              onClick={forceInitializeQueries}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4 text-foreground" />
              Force Initialize
            </Button>

            <Button
              variant="outline"
              onClick={fixOperationsQueries}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4 text-foreground" />
              Fix Operations
            </Button>

            <Button
              variant="destructive"
              onClick={resetAllQueries}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4 text-destructive-foreground" />
              RESET ALL
            </Button>

            <Button
              variant="secondary"
              onClick={debugQueryState}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              DEBUG STATE
            </Button>

            <Button
              variant="secondary"
              onClick={testDirectSupabaseCall}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              TEST DIRECT
            </Button>

            <Button
              variant="secondary"
              onClick={testSimpleReactQuery}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              TEST RQ
            </Button>

            <Button
              variant="secondary"
              onClick={testComponentMounting}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              TEST MOUNT
            </Button>

            <Button
              variant="destructive"
              onClick={testNetworkConnectivity}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              TEST NETWORK
            </Button>
          </div>

          <div className="border-t pt-4">
            <p className="text-sm text-muted-foreground mb-2">
              <strong>Load Data First:</strong> If Test 2 fails, navigate to a data page first:
            </p>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateToDataPage('/properties')}
                className="flex items-center gap-2"
              >
                <ExternalLink className="h-3 w-3" />
                Properties
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateToDataPage('/dashboard')}
                className="flex items-center gap-2"
              >
                <ExternalLink className="h-3 w-3" />
                Dashboard
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateToDataPage('/teams')}
                className="flex items-center gap-2"
              >
                <ExternalLink className="h-3 w-3" />
                Teams
              </Button>
            </div>
          </div>

          {lastRunTime && (
            <p className="text-sm text-muted-foreground">
              Last run: {lastRunTime.toLocaleTimeString()}
            </p>
          )}

          {summary && (
            <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
              <div className="text-sm">
                <strong>Test Summary:</strong> {summary.passed}/{summary.total} passed
              </div>
              {summary.failed > 0 && (
                <Badge variant="destructive">{summary.failed} failed</Badge>
              )}
              {summary.failed === 0 && summary.total > 0 && (
                <Badge variant="default" className="bg-green-100 text-green-800">All passed</Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
            <CardDescription>
              Detailed results from the data loading tests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div 
                  key={index}
                  className="flex items-start gap-3 p-3 border rounded-lg"
                >
                  <div className="mt-0.5">
                    {getTestIcon(result)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">Test {index + 1}</span>
                      {getTestBadge(result)}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {result.message}
                    </p>
                    {result.details && (
                      <details className="text-xs">
                        <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                          View Details
                        </summary>
                        <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                          {JSON.stringify(result.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Manual Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm space-y-2">
            <p><strong>To manually test data loading:</strong></p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>Navigate to a page with data (Properties, Maintenance, Teams, etc.)</li>
              <li>Switch to another tab or minimize the browser</li>
              <li>Wait 10-30 seconds</li>
              <li>Return to the StayFu tab</li>
              <li>Verify that data loads correctly and doesn't disappear</li>
            </ol>
            <p className="mt-3">
              <strong>Expected behavior:</strong> Data should refresh automatically when the window regains focus,
              and should not disappear or show loading states indefinitely.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DataLoadingTestComponent;
