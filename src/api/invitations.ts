import { supabase } from '@/integrations/supabase/client';

/**
 * Accept an invitation directly using the Supabase RPC function
 * This is a fallback method when the Edge Function fails
 */
export const acceptInvitationDirect = async (
  token: string,
  userId: string
): Promise<{ success: boolean; error?: string; team_id?: string; team_name?: string }> => {
  try {
    console.log('Accepting invitation directly via RPC function');
    console.log('Token:', token);
    console.log('User ID:', userId);

    // Validate inputs
    if (!token) {
      return { success: false, error: 'Invitation token is required' };
    }

    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    // First, try to get the invitation details to verify it exists
    try {
      const { data: invitationData, error: invitationError } = await supabase
        .from('team_invitations')
        .select('*')
        .eq('token', token)
        .single();

      if (invitationError || !invitationData) {
        console.error('Error fetching invitation:', invitationError);
        return {
          success: false,
          error: `Invalid invitation token: ${invitationError?.message || 'No invitation found'}`
        };
      }

      console.log('Found invitation:', invitationData);
    } catch (fetchError: any) {
      console.error('Exception fetching invitation:', fetchError);
      // Continue anyway, as the RPC function will also check the token
    }

    // Call the RPC function to accept the invitation
    const { data, error } = await supabase.rpc('accept_invitation_and_add_member', {
      p_token: token,
      p_user_id: userId
    });

    if (error) {
      console.error('Error accepting invitation via RPC:', error);

      // Try to get more detailed error information
      if (error.message.includes('Invitation not found')) {
        return { success: false, error: 'The invitation link is invalid or has expired' };
      }

      return { success: false, error: error.message };
    }

    if (!data || !data.success) {
      console.error('Invitation acceptance failed:', data);
      return {
        success: false,
        error: data?.error || 'Failed to accept invitation'
      };
    }

    console.log('Invitation accepted successfully via RPC:', data);
    return {
      success: true,
      team_id: data.team_id,
      team_name: data.team_name
    };
  } catch (error: any) {
    console.error('Exception accepting invitation via RPC:', error);
    return {
      success: false,
      error: error.message || 'An unexpected error occurred'
    };
  }
};

/**
 * Accept an invitation directly with user registration in one step
 * This uses the new accept_invitation_direct RPC function
 */
export const acceptInvitationWithRegistration = async (
  token: string,
  email: string,
  password: string,
  firstName: string,
  lastName: string,
  role: string = 'service_provider'
): Promise<{ success: boolean; error?: string; user_id?: string; team_id?: string; user_exists?: boolean }> => {
  try {
    console.log('Accepting invitation with registration via Edge Function');
    console.log('Token:', token);
    console.log('Email:', email);
    console.log('Role:', role);

    // Validate inputs
    if (!token) {
      return { success: false, error: 'Invitation token is required' };
    }

    if (!email) {
      return { success: false, error: 'Email is required' };
    }

    if (!password) {
      return { success: false, error: 'Password is required' };
    }

    if (!firstName || !lastName) {
      return { success: false, error: 'First name and last name are required' };
    }

    // Validate the role is one of the allowed values
    const validRoles = ['admin', 'property_manager', 'service_provider', 'staff', 'super_admin'];
    const effectiveRole = validRoles.includes(role) ? role : 'service_provider';

    console.log('Using effective role:', effectiveRole);

    // Try the Edge Function first
    try {
      console.log('Attempting to use Edge Function for invitation acceptance');
      console.log('Supabase client URL:', supabase.supabaseUrl);
      console.log('Function payload:', {
        token: token.substring(0, 8) + '...',
        email,
        first_name: firstName,
        last_name: lastName,
        role: effectiveRole
      });

      const { data, error } = await supabase.functions.invoke('accept-invitation-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token,
          email,
          password,
          first_name: firstName,
          last_name: lastName,
          role: effectiveRole
        })
      });

      console.log('Edge Function response:', { data, error });

      if (error) {
        console.error('Error from Edge Function:', error);
        throw error;
      }

      if (!data || !data.success) {
        console.error('Edge Function returned unsuccessful response:', data);
        throw new Error(data?.error || 'Failed to accept invitation');
      }

      console.log('Invitation accepted successfully with Edge Function:', data);
      return {
        success: true,
        user_id: data.user_id,
        team_id: data.team_id
      };
    } catch (edgeFunctionError: any) {
      console.error('Edge Function failed, trying fallback approach:', edgeFunctionError);

      // Fallback: Try to register the user directly and then accept the invitation
      console.log('Attempting fallback: Direct registration and invitation acceptance');

      // First, check if the user already exists
      const { data: existingUser } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (existingUser?.user) {
        console.log('User already exists, returning user_exists flag');
        return {
          success: false,
          error: 'User already exists',
          user_exists: true,
          user_id: existingUser.user.id
        };
      }

      // Step 1: Register the user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
            role: effectiveRole
          },
          emailRedirectTo: `${window.location.origin}/#/auth?invitation=${token}`
        }
      });

      if (authError) {
        console.error('Fallback registration failed:', authError);

        // Check if the error is related to the user already existing
        if (authError.message.includes('duplicate key value violates unique constraint') ||
            authError.message.includes('already exists') ||
            authError.message.includes('User already registered')) {

          // Try to check if the user exists without password verification
          try {
            const { data: userLookup } = await supabase
              .from('profiles')
              .select('id')
              .eq('email', email)
              .single();

            if (userLookup?.id) {
              console.log('Found user in profiles table:', userLookup.id);
              return {
                success: false,
                error: 'User already exists but password may be incorrect',
                user_exists: true,
                user_id: userLookup.id
              };
            }
          } catch (lookupError) {
            console.error('Error looking up user in profiles:', lookupError);
          }

          return {
            success: false,
            error: 'User already exists. Please try signing in instead.',
            user_exists: true
          };
        }

        return { success: false, error: authError.message };
      }

      if (!authData.user) {
        console.error('Fallback registration did not return user data');
        return { success: false, error: 'Failed to create user account' };
      }

      console.log('User registered successfully in fallback flow:', authData.user.id);

      // Step 2: Create profile if needed
      try {
        const { error: profileError } = await supabase
          .from('profiles')
          .upsert({
            id: authData.user.id,
            email,
            first_name: firstName,
            last_name: lastName,
            role: effectiveRole,
            is_super_admin: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, { onConflict: 'id' });

        if (profileError) {
          console.error('Error creating profile in fallback flow:', profileError);
        }
      } catch (profileError) {
        console.error('Exception creating profile in fallback flow:', profileError);
        // Continue anyway, as the profile might be created by a trigger
      }

      // Step 3: Accept the invitation
      try {
        console.log('Accepting invitation via RPC in fallback flow');
        const { data: rpcData, error: rpcError } = await supabase.rpc(
          'accept_invitation_and_add_member',
          {
            p_token: token,
            p_user_id: authData.user.id
          }
        );

        if (rpcError) {
          console.error('Error accepting invitation via RPC in fallback flow:', rpcError);
          return { success: false, error: rpcError.message };
        }

        if (!rpcData || !rpcData.success) {
          console.error('RPC returned unsuccessful response in fallback flow:', rpcData);
          return {
            success: false,
            error: rpcData?.error || 'Failed to accept invitation'
          };
        }

        console.log('Invitation accepted successfully via fallback flow:', rpcData);
        return {
          success: true,
          user_id: authData.user.id,
          team_id: rpcData.team_id
        };
      } catch (rpcError: any) {
        console.error('Exception accepting invitation via RPC in fallback flow:', rpcError);
        return { success: false, error: rpcError.message };
      }
    }
  } catch (error: any) {
    console.error('Exception in acceptInvitationWithRegistration:', error);
    return {
      success: false,
      error: error.message || 'An unexpected error occurred'
    };
  }
};

/**
 * Process invitation acceptance after user registration
 * This function is called after a user has been registered or logged in
 */
export const processInvitationAcceptance = async (
  token: string,
  userId: string
): Promise<{
  success: boolean;
  error?: string;
  team_id?: string;
  team_name?: string;
  role?: string;
}> => {
  try {
    console.log('Processing invitation acceptance for user:', userId);

    // Call the new RPC function to process the invitation
    const { data, error } = await supabase.rpc(
      'process_invitation_acceptance',
      {
        p_token: token,
        p_user_id: userId
      }
    );

    if (error) {
      console.error('Error processing invitation:', error);
      return { success: false, error: error.message };
    }

    console.log('Invitation processed successfully:', data);
    return {
      success: data.success,
      error: data.error,
      team_id: data.team_id,
      team_name: data.team_name,
      role: data.role
    };
  } catch (error: any) {
    console.error('Exception in processInvitationAcceptance:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};

/**
 * Register user for invitation with auto email confirmation
 */
export const registerUserForInvitation = async (
  token: string,
  email: string,
  password: string,
  firstName: string,
  lastName: string,
  role: string = 'service_provider'
): Promise<{
  success: boolean;
  error?: string;
  user_id?: string;
  team_id?: string;
  session?: any;
}> => {
  try {
    console.log('Registering user for invitation with auto email confirmation:', email);

    // Use the edge function that auto-confirms email for invitation users
    const { data, error } = await supabase.functions.invoke('accept-invitation-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token,
        email,
        password,
        first_name: firstName,
        last_name: lastName,
        role: role
      })
    });

    if (error) {
      console.error('Invitation registration failed:', error);
      return { success: false, error: error.message };
    }

    if (!data || !data.success) {
      console.error('Edge function returned unsuccessful response:', data);
      return { success: false, error: data?.error || 'Failed to process invitation' };
    }

    console.log('User registered and invitation processed successfully:', data);

    // Handle different scenarios
    if (data.already_member) {
      return {
        success: true,
        user_id: data.user_id,
        team_id: data.team_id,
        existing_user: true,
        already_member: true,
        message: 'You are already a member of this team!'
      };
    }

    // If user already existed, they might already be signed in or need to sign in
    if (data.existing_user) {
      // Try to sign in the existing user
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (signInError) {
        console.error('Sign-in failed for existing user:', signInError);
        return { 
          success: true, 
          error: 'You have been added to the team, but sign-in failed. Please login with your existing password.',
          user_id: data.user_id,
          team_id: data.team_id,
          existing_user: true
        };
      }

      return {
        success: true,
        user_id: data.user_id,
        team_id: data.team_id,
        existing_user: true,
        session: signInData.session,
        message: 'Welcome back! You have been added to the team.'
      };
    }

    // New user - sign them in since they're auto-confirmed
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (signInError) {
      console.error('Auto sign-in failed for new user:', signInError);
      return { 
        success: true, 
        error: 'Account created but auto sign-in failed. Please login manually.',
        user_id: data.user_id,
        team_id: data.team_id
      };
    }

    return {
      success: true,
      user_id: data.user_id,
      team_id: data.team_id,
      session: signInData.session,
      message: 'Welcome! Your account has been created and you have been added to the team.'
    };
  } catch (error: any) {
    console.error('Exception in registerUserForInvitation:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};

/**
 * Simple function to register a new user (non-invitation)
 */
export const registerUser = async (
  email: string,
  password: string,
  firstName: string,
  lastName: string,
  role: string = 'service_provider'
): Promise<{
  success: boolean;
  error?: string;
  user?: any;
  session?: any;
}> => {
  try {
    console.log('Registering new user:', email);

    // Register the user normally (requires email confirmation)
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName,
          role: role
        },
        emailRedirectTo: `${window.location.origin}/#/auth`
      }
    });

    if (error) {
      console.error('Registration failed:', error);
      return { success: false, error: error.message };
    }

    if (!data?.user) {
      return { success: false, error: 'Failed to create user account' };
    }

    console.log('User registered successfully:', data.user.id);

    return {
      success: true,
      user: data.user,
      session: data.session
    };
  } catch (error: any) {
    console.error('Exception in registerUser:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};

/**
 * Simple function to sign in a user
 */
export const signInUser = async (
  email: string,
  password: string
): Promise<{
  success: boolean;
  error?: string;
  user?: any;
  session?: any;
}> => {
  try {
    console.log('Signing in user:', email);

    // Sign in the user
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error('Sign in failed:', error);
      return { success: false, error: error.message };
    }

    if (!data?.user) {
      return { success: false, error: 'Failed to sign in' };
    }

    console.log('User signed in successfully:', data.user.id);
    return {
      success: true,
      user: data.user,
      session: data.session
    };
  } catch (error: any) {
    console.error('Exception in signInUser:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};