
import { useLocation, Link, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import { motion } from "framer-motion";
import { Home, ArrowLeft, HelpCircle } from "lucide-react";

const NotFound = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  const handleGoBack = () => {
    navigate(-1); // Go back to the previous page in history
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <motion.div
        className="text-center max-w-md glass-card p-8 rounded-xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="w-20 h-20 bg-muted rounded-full mx-auto flex items-center justify-center mb-6">
          <HelpCircle size={32} className="text-muted-foreground" />
        </div>

        <h1 className="text-5xl font-bold mb-4">404</h1>
        <p className="text-xl mb-2">Page not found</p>
        <p className="text-muted-foreground mb-8">
          Sorry, we couldn't find the page you're looking for:<br />
          <span className="font-mono text-sm bg-muted px-2 py-1 rounded mt-2 inline-block">
            {location.pathname}
          </span>
        </p>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Link
            to="/#/"
            className="flex items-center justify-center gap-2 bg-primary text-white px-4 py-2 rounded-lg font-medium transition-colors hover:bg-primary/90"
          >
            <Home size={18} />
            Go to Dashboard
          </Link>
          <button
            onClick={handleGoBack}
            className="flex items-center justify-center gap-2 glass px-4 py-2 rounded-lg font-medium transition-colors hover:bg-background/80"
          >
            <ArrowLeft size={18} />
            Go Back
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default NotFound;
