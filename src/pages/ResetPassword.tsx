
import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { toast } from 'sonner';
import { supabase } from '../integrations/supabase/client';

const ResetPassword: React.FC = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(true);
  const [isValidResetLink, setIsValidResetLink] = useState(false);
  const [hasProcessedToken, setHasProcessedToken] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Process the recovery token on component mount
  useEffect(() => {
    const processRecoveryToken = async () => {
      try {
        // Parse URL parameters
        const params = new URLSearchParams(location.search);
        const accessToken = params.get('access_token');
        const refreshToken = params.get('refresh_token');
        const type = params.get('type');

        console.log('Reset password parameters:', {
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          type
        });

        // If we don't have the necessary parameters, show an error
        if (!accessToken || type !== 'recovery') {
          console.error('Missing or invalid recovery parameters');
          setIsValidResetLink(false);
          setIsProcessing(false);
          setHasProcessedToken(true);
          return;
        }

        // First, sign out any existing session
        try {
          // Check if we have a valid session before attempting to sign out
          const { data: sessionData } = await supabase.auth.getSession();

          if (sessionData && sessionData.session) {
            // We have a valid session, proceed with sign out
            await supabase.auth.signOut();
          } else {
            console.log('No active session found, skipping sign out');
          }
        } catch (signOutError) {
          console.warn('Error during sign out (continuing anyway):', signOutError);
          // Continue with the password reset flow regardless
        }

        // Now exchange the recovery token for a session
        const { error } = await supabase.auth.verifyOtp({
          token_hash: accessToken,
          type: 'recovery'
        });

        if (error) {
          console.error('Error verifying recovery token:', error);
          toast.error('Invalid or expired password reset link. Please request a new one.');
          setIsValidResetLink(false);
          setIsProcessing(false);
          setHasProcessedToken(true);
          return;
        }

        // Check if we have a valid session now
        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
          console.error('Failed to establish session with recovery token');
          toast.error('Unable to process password reset. Please request a new link.');
          setIsValidResetLink(false);
          setIsProcessing(false);
          setHasProcessedToken(true);
          return;
        }

        console.log('Recovery token processed successfully, user can now reset password');
        setIsValidResetLink(true);
        setIsProcessing(false);
        setHasProcessedToken(true);

      } catch (error) {
        console.error('Exception processing recovery token:', error);
        toast.error('An error occurred. Please try again or request a new password reset link.');
        setIsValidResetLink(false);
        setIsProcessing(false);
        setHasProcessedToken(true);
      }
    };

    if (!hasProcessedToken) {
      processRecoveryToken();
    }
  }, [location, hasProcessedToken]);

  // If the user is already logged in and not through the recovery flow, redirect to dashboard
  useEffect(() => {
    const checkAuthState = async () => {
      if (!hasProcessedToken) return; // Wait until we've processed the token

      const { data: { session } } = await supabase.auth.getSession();

      // If we have a session but the reset link was invalid, the user is logged in normally
      // so redirect them to the dashboard
      if (session && !isValidResetLink) {
        console.log('User is already logged in normally, redirecting to dashboard');
        navigate('/#/dashboard');
      }
    };

    checkAuthState();
  }, [hasProcessedToken, isValidResetLink, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (password.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    setIsLoading(true);

    try {
      // Check if we have a valid session
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.error('No valid session found for password reset');
        toast.error('Your session has expired. Please request a new password reset link.');
        setIsValidResetLink(false);
        setIsLoading(false);
        return;
      }

      // Update the password
      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        console.error('Error updating password:', error);
        throw error;
      }

      // Get the user for logging purposes
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Password updated successfully for user:', user?.email);

      // Sign out to ensure the user has to log in with their new password
      try {
        // We should have a valid session here since we just updated the password
        await supabase.auth.signOut();
      } catch (signOutError) {
        console.warn('Error during sign out after password update (continuing anyway):', signOutError);
        // Continue with the flow regardless
      }

      toast.success('Your password has been updated successfully. Please log in with your new password.');

      // Redirect to login page after a short delay
      setTimeout(() => {
        navigate('/#/login');
      }, 2000);
    } catch (error) {
      console.error('Password update error:', error);
      toast.error('Failed to update password. Please try again or request a new reset link.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen">
      <Card className="w-full max-w-md glass-card">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Set New Password</CardTitle>
          <CardDescription className="text-center">Enter and confirm your new password</CardDescription>
        </CardHeader>
        <CardContent>
          {isProcessing ? (
            <div className="text-center space-y-4 py-4">
              <p className="text-gray-600">
                Processing your password reset request...
              </p>
              <div className="flex justify-center mt-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            </div>
          ) : !isValidResetLink ? (
            <div className="text-center space-y-4">
              <p className="text-red-600">
                Invalid or expired password reset link. Please request a new one.
              </p>
              <Button
                onClick={() => navigate('/#/forgot-password')}
                className="mt-4"
              >
                Request New Reset Link
              </Button>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="p-3 bg-blue-50 text-blue-800 rounded-md mb-4">
                <p className="text-sm">
                  Please set a new password for your account. Your password must be at least 8 characters long.
                </p>
              </div>
              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">New Password</label>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  minLength={8}
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="confirmPassword" className="text-sm font-medium">Confirm Password</label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="••••••••"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  minLength={8}
                />
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Updating password...' : 'Update password'}
              </Button>
            </form>
          )}
        </CardContent>
        <CardFooter className="flex-col space-y-2">
          <div className="text-sm text-center">
            <Link to="/#/login" className="text-blue-600 hover:underline">
              Back to login
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ResetPassword;
