import React, { useState, useEffect, use<PERSON><PERSON>back, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowUpRight, ArrowDownRight, RefreshCcw, Loader2, AlertCircle } from 'lucide-react';
import PageTransition from '@/components/layout/PageTransition';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useOperationsDataQuery, DateRangeOption, DepartmentOption } from '@/hooks/useOperationsDataQuery';
import { useQueryClient } from '@tanstack/react-query';


import { handleAuthError } from '@/utils/authErrorHandler';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON><PERSON><PERSON>, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

const Operations: React.FC = () => {
  const [dateRange, setDateRange] = useState<DateRangeOption>('This month');
  const [department, setDepartment] = useState<DepartmentOption>('All departments');
  const [property, setProperty] = useState<string>('All properties');
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const queryClient = useQueryClient();

  // Use the operations data hook with React Query
  const {
    metrics,
    tasksCompletedData,
    tasksByDayData,
    reportedIssuesData,
    timeReadyData,
    properties,
    isLoading,
    error,
    isError,
    refreshData
  } = useOperationsDataQuery(dateRange, department, property);

  // Create a simpler, more reliable refresh function
  const handleRefresh = useCallback(async () => {
    console.log('[Operations] Refreshing data manually');
    if (isRefreshing || isLoading) {
      console.log('[Operations] Already refreshing or loading, skipping refresh');
      return;
    }

    setIsRefreshing(true);
    try {
      // Show loading toast
      toast.loading('Refreshing operations data...', {
        id: 'operations-refresh-toast'
      });

      // Call the hook's refresh function directly
      await refreshData();

      // No need to manually invalidate queries - the refreshData function should handle this

      console.log('[Operations] Data refreshed successfully');
      toast.success('Operations data refreshed successfully', {
        id: 'operations-refresh-toast'
      });
    } catch (err) {
      console.error('[Operations] Error refreshing data:', err);

      // Check if this is an authentication error
      if (err instanceof Error &&
          (err.message?.includes('Auth') ||
           err.message?.includes('auth') ||
           err.message?.includes('401'))) {
        handleAuthError(err, {
          showToast: true,
          redirectToLogin: true,
          logError: true
        });
      } else {
        toast.error('Failed to refresh operations data', {
          id: 'operations-refresh-toast'
        });
      }
    } finally {
      setIsRefreshing(false);
    }
  }, [refreshData, isRefreshing, isLoading]);

  // REMOVED: Visibility change tracking
  // This was causing data to disappear when the app comes back into focus

  // REMOVED: Session management hook
  // This hook doesn't exist in the codebase

  // Set up event listeners for refresh triggers
  useEffect(() => {
    // Listen for the single stayfu-data-refreshed event
    const handleDataRefreshed = (event: CustomEvent) => {
      // Only react if we're on the operations page and not already refreshing
      if (!isRefreshing && !isLoading) {
        // Get the current route path
        const currentPath = window.location.pathname;

        // Check if this event is for us (either no route specified or includes '/operations')
        const refreshedRoute = event.detail?.route || '';
        if (!refreshedRoute || refreshedRoute === '/operations' || currentPath.includes('/operations')) {
          console.log('[Operations] Received data refresh event, refreshing data');
          refreshData(); // Use the hook's refresh function directly - no toast needed
        }
      }
    };

    // Listen for our single, unified data refresh event
    window.addEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);

    // Return cleanup function
    return () => {
      window.removeEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);
    };

  }, [refreshData, isRefreshing, isLoading]);

  return (
    <PageTransition>
      <div className="space-y-6">
        <div className="glass-card glass-card-hover rounded-xl p-6 transition-all duration-300">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold">Operations Insights</h1>
              <p className="text-muted-foreground mt-1">Track performance metrics and operational data</p>
            </div>

            <div className="flex flex-wrap gap-2">
              <Select
                value={dateRange}
                onValueChange={(value: string) => setDateRange(value as DateRangeOption)}>
                <SelectTrigger className="glass-nav w-[140px]">
                  <SelectValue placeholder="Date range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="This month">This month</SelectItem>
                  <SelectItem value="Last month">Last month</SelectItem>
                  <SelectItem value="Last 3 months">Last 3 months</SelectItem>
                  <SelectItem value="This year">This year</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={department}
                onValueChange={(value: string) => setDepartment(value as DepartmentOption)}>
                <SelectTrigger className="glass-nav w-[180px]">
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All departments">All departments</SelectItem>
                  <SelectItem value="Cleaning">Cleaning</SelectItem>
                  <SelectItem value="Maintenance">Maintenance</SelectItem>
                  <SelectItem value="Inspection">Inspection</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={property}
                onValueChange={(value: string) => setProperty(value)}>
                <SelectTrigger className="glass-nav w-[160px]">
                  <SelectValue placeholder="Property" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All properties">All properties</SelectItem>
                  {properties.map((prop) => (
                    <SelectItem key={prop.id} value={prop.id}>{prop.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex gap-2 ml-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleRefresh}
                  disabled={isLoading || isRefreshing}
                  className="glass-interactive"
                >
                  {isRefreshing ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <RefreshCcw className="h-4 w-4 mr-1" />
                  )}
                  <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
                </Button>


              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="glass-card rounded-xl p-4 mb-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error instanceof Error ? error.message : String(error)}</AlertDescription>
            </Alert>
          </div>
        )}

        {/* Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Metric Cards */}
          <Card className="glass-card glass-card-hover transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">Avg. time to complete</span>
                <span className="text-2xl font-semibold">{isLoading ? '-' : metrics.avgTimeToComplete.toFixed(1)} hrs</span>
                <div className={`flex items-center mt-2 ${metrics.avgTimeComparison > 0 ? 'text-red-500 dark:text-red-400' : 'text-green-500 dark:text-green-400'} text-sm`}>
                  {metrics.avgTimeComparison > 0 ? (
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-4 w-4 mr-1" />
                  )}
                  <span>{Math.abs(metrics.avgTimeComparison).toFixed(2)} hrs vs last period</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card glass-card-hover transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">Completed on due date</span>
                <span className="text-2xl font-semibold">{isLoading ? '-' : Math.round(metrics.completedOnDueDate)}%</span>
                <div className={`flex items-center mt-2 ${metrics.completedOnDueDateComparison < 0 ? 'text-red-500 dark:text-red-400' : 'text-green-500 dark:text-green-400'} text-sm`}>
                  {metrics.completedOnDueDateComparison < 0 ? (
                    <ArrowDownRight className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                  )}
                  <span>{Math.abs(metrics.completedOnDueDateComparison)}% vs last period</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card glass-card-hover transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">Properties ready for next booking</span>
                <span className="text-2xl font-semibold">{isLoading ? '-' : Math.round(metrics.propertiesReadyBeforeCheckIn)}%</span>
                <div className={`flex items-center mt-2 ${metrics.propertiesReadyComparison < 0 ? 'text-red-500 dark:text-red-400' : 'text-green-500 dark:text-green-400'} text-sm`}>
                  {metrics.propertiesReadyComparison < 0 ? (
                    <ArrowDownRight className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                  )}
                  <span>{Math.abs(metrics.propertiesReadyComparison)}% vs last period</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="glass-card glass-card-hover transition-all duration-300">
            <CardHeader>
              <CardTitle>Tasks Completed</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-[300px] flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <div className="h-[300px]">
                  <ChartContainer
                    config={{
                      total: { color: '#0088fe' },
                      reported: { color: '#82ca9d' },
                      benchmark: { color: '#ff8042' }
                    }}
                  >
                    <BarChart data={tasksCompletedData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <ChartTooltip />
                      <Legend />
                      <Bar dataKey="total" name="Total Tasks" fill="#0088fe" />
                      <Bar dataKey="reported" name="Reported Issues" fill="#82ca9d" />
                    </BarChart>
                  </ChartContainer>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="glass-card glass-card-hover transition-all duration-300">
            <CardHeader>
              <CardTitle>Tasks by Day</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-[300px] flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <div className="h-[300px]">
                  <ChartContainer
                    config={{
                      cleaning: { color: '#0088fe' },
                      inspection: { color: '#82ca9d' },
                      maintenance: { color: '#ff8042' },
                      benchmark: { color: '#8884d8' }
                    }}
                  >
                    <BarChart data={tasksByDayData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <ChartTooltip />
                      <Legend />
                      <Bar dataKey="cleaning" name="Cleaning" stackId="a" fill="#0088fe" />
                      <Bar dataKey="inspection" name="Inspection" stackId="a" fill="#82ca9d" />
                      <Bar dataKey="maintenance" name="Maintenance" stackId="a" fill="#ff8042" />
                    </BarChart>
                  </ChartContainer>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Additional Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          <Card className="glass-card glass-card-hover transition-all duration-300">
            <CardHeader>
              <CardTitle>Reported Issues by Source</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-[300px] flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={reportedIssuesData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {reportedIssuesData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="glass-card glass-card-hover transition-all duration-300">
            <CardHeader>
              <CardTitle>Average Task Completion Time (Hours)</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-[300px] flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <div className="h-[300px]">
                  <ChartContainer
                    config={{
                      hours: { color: '#0088fe' }
                    }}
                  >
                    <LineChart data={timeReadyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <ChartTooltip />
                      <Legend />
                      <Line type="monotone" dataKey="hours" name="Hours" stroke="#0088fe" strokeWidth={2} />
                    </LineChart>
                  </ChartContainer>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </PageTransition>
  );
};

export default Operations;
