import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

const Index = () => {
  const { authState } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // If user is authenticated, redirect to dashboard
    // Add null check for authState before accessing its properties
    if (authState?.user && !authState?.isLoading) {
      navigate('/dashboard');
    }
  }, [authState?.user, authState?.isLoading, navigate]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center">
      <div className="text-center max-w-md p-8 glass-card rounded-xl">
        <h1 className="text-4xl font-bold mb-4">Welcome to StayFu</h1>
        <p className="text-xl text-muted-foreground mb-8">Property management made simple</p>

        <div className="flex flex-col space-y-4">
          <a
            href="/#/login"
            className="bg-primary text-white py-3 px-6 rounded-lg hover:bg-primary/90 transition-colors"
          >
            Login
          </a>
          <a
            href="/#/register"
            className="bg-secondary/10 text-secondary py-3 px-6 rounded-lg hover:bg-secondary/20 transition-colors"
          >
            Register
          </a>
        </div>
      </div>
    </div>
  );
};

export default Index;
