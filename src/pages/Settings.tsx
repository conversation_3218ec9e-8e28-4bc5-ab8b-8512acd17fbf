import React, { useState, useEffect, useRef } from 'react';
import PageTransition from '../components/layout/PageTransition';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useParams, useNavigate } from 'react-router-dom';
import { useNavigationRefresh } from '@/hooks/useNavigationRefresh';
import {
  fetchUserSettings,
  updateUserSetting,
  saveAllUserSettings,
  applyDarkMode,
  applyCompactMode
} from '@/services/settingsService';
import { UserSettings as UserSettingsType, UserSettingKey } from '@/types/settings';

// Import components
import SettingsSidebar, { SettingsSections } from '@/components/settings/SettingsSidebar';
import ToggleOption, { ToggleSetting } from '@/components/settings/ToggleOption';
import AppearanceSettings from '@/components/settings/AppearanceSettings';
import NotificationSettings from '@/components/settings/NotificationSettings';
import AccountSettings from '@/components/settings/AccountSettings';
import ProfileSettings from '@/components/settings/ProfileSettings';
import SecuritySettings from '@/components/settings/SecuritySettings';
import BackupSettings from '@/components/settings/BackupSettings';
import HelpSettings from '@/components/settings/HelpSettings';
import DefaultContent from '@/components/settings/DefaultContent';
import SettingsHeader from '@/components/settings/SettingsHeader';
import ApiTokenManager from '@/components/settings/ApiTokenManager';
import { ApiIntegrations } from '@/components/settings/ApiIntegrations';

const Settings = () => {
  const { section } = useParams<{ section: string }>();
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState<string>(section || 'appearance');
  const [hasChanges, setHasChanges] = useState(false);
  const { authState } = useAuth();
  const { resetTutorials } = useOnboarding();
  const [loading, setLoading] = useState(true);
  const { refreshRouteData } = useNavigationRefresh();
  const initialLoadDone = useRef(false);

  const [settings, setSettings] = useState<{
    notifications: ToggleSetting[];
    appearance: ToggleSetting[];
  }>({
    notifications: [
      {
        id: 'email_notifications',
        label: 'Email Notifications',
        description: 'Receive notifications about maintenance tasks and inventory updates via email',
        enabled: true,
      },
      {
        id: 'push_notifications',
        label: 'Push Notifications',
        description: 'Receive notifications on your device when tasks are assigned or completed',
        enabled: false,
      },
      {
        id: 'weekly_summary',
        label: 'Weekly Summary',
        description: 'Receive a weekly email summary of all property activities',
        enabled: true,
      },
      {
        id: 'inventory_alerts',
        label: 'Inventory Alerts',
        description: 'Get notified when inventory items are running low',
        enabled: true,
      },
    ],
    appearance: [
      {
        id: 'dark_mode',
        label: 'Dark Mode',
        description: 'Use dark mode for the application interface',
        enabled: false,
      },
      {
        id: 'compact_mode',
        label: 'Compact Mode',
        description: 'Show more information with less spacing',
        enabled: false,
      },
      {
        id: 'animations',
        label: 'Animations',
        description: 'Enable animations throughout the application',
        enabled: true,
      },
    ],
  });

  // Update URL when active section changes
  useEffect(() => {
    if (section !== activeSection && activeSection) {
      navigate(`/settings/${activeSection}`, { replace: true });
    }
  }, [activeSection, navigate, section]);

  useEffect(() => {
    // Update active section when URL parameter changes
    if (section && section !== activeSection) {
      setActiveSection(section);
    }
  }, [section]);

  useEffect(() => {
    const loadSettings = async () => {
      if (!authState.user?.id) {
        setLoading(false);
        return;
      }

      try {
        console.log('[Settings] Loading settings for user:', authState.user.id);
        const userSettings = await fetchUserSettings(authState.user.id);

        if (userSettings) {
          console.log('[Settings] Loaded settings from Supabase:', userSettings);

          setSettings({
            notifications: [
              {
                id: 'email_notifications',
                label: 'Email Notifications',
                description: 'Receive notifications about maintenance tasks and inventory updates via email',
                enabled: userSettings.email_notifications,
              },
              {
                id: 'push_notifications',
                label: 'Push Notifications',
                description: 'Receive notifications on your device when tasks are assigned or completed',
                enabled: userSettings.push_notifications,
              },
              {
                id: 'weekly_summary',
                label: 'Weekly Summary',
                description: 'Receive a weekly email summary of all property activities',
                enabled: userSettings.weekly_summary,
              },
              {
                id: 'inventory_alerts',
                label: 'Inventory Alerts',
                description: 'Get notified when inventory items are running low',
                enabled: userSettings.inventory_alerts,
              },
            ],
            appearance: [
              {
                id: 'dark_mode',
                label: 'Dark Mode',
                description: 'Use dark mode for the application interface',
                enabled: userSettings.dark_mode,
              },
              {
                id: 'compact_mode',
                label: 'Compact Mode',
                description: 'Show more information with less spacing',
                enabled: userSettings.compact_mode,
              },
              {
                id: 'animations',
                label: 'Animations',
                description: 'Enable animations throughout the application',
                enabled: userSettings.animations,
              },
            ],
          });

          // Immediately apply theme settings
          applyDarkMode(userSettings.dark_mode);
          applyCompactMode(userSettings.compact_mode);
        } else {
          console.log('[Settings] No settings found, using defaults');

          // Set defaults based on system preference if available
          const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
          applyDarkMode(prefersDarkMode);
          applyCompactMode(false);
        }

        // Mark initial load as done
        initialLoadDone.current = true;
      } catch (error) {
        console.error('[Settings] Failed to load settings:', error);
        toast.error('Failed to load settings');
      } finally {
        setLoading(false);
      }
    };

    loadSettings();

    // Refresh the route data to ensure all settings are loaded
    const currentPath = `/settings/${activeSection}`;
    console.log(`[Settings] Refreshing route data for ${currentPath}`);
    refreshRouteData(currentPath);
  }, [authState.user?.id, activeSection, refreshRouteData]);

  // Set up a periodic refresh timer
  useEffect(() => {
    if (!authState.user?.id) return;

    console.log('[Settings] Setting up periodic refresh timer');

    // Function to refresh settings data
    const refreshSettingsData = async () => {
      try {
        console.log('[Settings] Periodic refresh triggered');

        // Only refresh if initial load is done
        if (!initialLoadDone.current) {
          console.log('[Settings] Initial load not done yet, skipping refresh');
          return;
        }

        // Refresh the route data
        const currentPath = `/settings/${activeSection}`;
        console.log(`[Settings] Refreshing route data for ${currentPath}`);
        refreshRouteData(currentPath);

        // Also directly fetch settings
        console.log('[Settings] Directly fetching settings');
        const userSettings = await fetchUserSettings(authState.user.id);

        if (userSettings) {
          console.log('[Settings] Refreshed settings from Supabase:', userSettings);

          // Update settings state
          setSettings({
            notifications: [
              {
                id: 'email_notifications',
                label: 'Email Notifications',
                description: 'Receive notifications about maintenance tasks and inventory updates via email',
                enabled: userSettings.email_notifications,
              },
              {
                id: 'push_notifications',
                label: 'Push Notifications',
                description: 'Receive notifications on your device when tasks are assigned or completed',
                enabled: userSettings.push_notifications,
              },
              {
                id: 'weekly_summary',
                label: 'Weekly Summary',
                description: 'Receive a weekly email summary of all property activities',
                enabled: userSettings.weekly_summary,
              },
              {
                id: 'inventory_alerts',
                label: 'Inventory Alerts',
                description: 'Get notified when inventory items are running low',
                enabled: userSettings.inventory_alerts,
              },
            ],
            appearance: [
              {
                id: 'dark_mode',
                label: 'Dark Mode',
                description: 'Use dark mode for the application interface',
                enabled: userSettings.dark_mode,
              },
              {
                id: 'compact_mode',
                label: 'Compact Mode',
                description: 'Show more information with less spacing',
                enabled: userSettings.compact_mode,
              },
              {
                id: 'animations',
                label: 'Animations',
                description: 'Enable animations throughout the application',
                enabled: userSettings.animations,
              },
            ],
          });
        }
      } catch (error) {
        console.error('[Settings] Error in periodic refresh:', error);
      }
    };

    // Initial refresh after a short delay
    const initialRefreshTimeout = setTimeout(() => {
      refreshSettingsData();
    }, 2000);

    // Set up periodic refresh every 30 seconds
    const refreshInterval = setInterval(() => {
      // Only refresh if the document is visible
      if (document.visibilityState === 'visible') {
        refreshSettingsData();
      } else {
        console.log('[Settings] Document not visible, skipping periodic refresh');
      }
    }, 300000); // 5 minutes (300000 ms)

    // Clean up
    return () => {
      clearTimeout(initialRefreshTimeout);
      clearInterval(refreshInterval);
    };
  }, [authState.user?.id, activeSection, refreshRouteData]);

  const handleToggleSetting = async (sectionId: 'notifications' | 'appearance', settingId: UserSettingKey, checked: boolean) => {
    if (!authState.user?.id) {
      toast.error('You must be logged in to change settings');
      return;
    }

    console.log(`Settings: Toggle setting: ${settingId} = ${checked}`);

    // Update the local state immediately for responsive UI
    setSettings(prev => {
      const updatedSection = [...prev[sectionId]];
      const settingIndex = updatedSection.findIndex(setting => setting.id === settingId);

      if (settingIndex !== -1) {
        updatedSection[settingIndex] = {
          ...updatedSection[settingIndex],
          enabled: checked,
        };
      }

      return {
        ...prev,
        [sectionId]: updatedSection,
      };
    });

    // Apply visual changes immediately for appearance settings
    if (sectionId === 'appearance') {
      if (settingId === 'dark_mode') {
        applyDarkMode(checked);
        // Also save to localStorage for immediate persistence
        localStorage.setItem('theme-mode', checked ? 'dark' : 'light');
      } else if (settingId === 'compact_mode') {
        applyCompactMode(checked);
      }
    }

    try {
      // Save the setting to database with a small delay to prevent rapid toggles
      setTimeout(async () => {
        const success = await updateUserSetting(authState.user!.id, settingId, checked);
        if (!success) {
          // If save failed, revert the UI
          toast.error(`Failed to save ${settingId.replace('_', ' ')} setting`);
          setSettings(prev => {
            const updatedSection = [...prev[sectionId]];
            const settingIndex = updatedSection.findIndex(setting => setting.id === settingId);

            if (settingIndex !== -1) {
              updatedSection[settingIndex] = {
                ...updatedSection[settingIndex],
                enabled: !checked,
              };
            }

            return {
              ...prev,
              [sectionId]: updatedSection,
            };
          });

          // Also revert the applied theme if needed
          if (sectionId === 'appearance') {
            if (settingId === 'dark_mode') {
              applyDarkMode(!checked);
              localStorage.setItem('theme-mode', !checked ? 'dark' : 'light');
            } else if (settingId === 'compact_mode') {
              applyCompactMode(!checked);
            }
          }
        }
      }, 100);
    } catch (error) {
      console.error('Error saving setting:', error);
      toast.error(`Failed to save ${settingId.replace('_', ' ')} setting`);
    }
  };

  const saveSettings = async () => {
    if (!authState.user?.id) {
      toast.error('You must be logged in to save settings');
      return;
    }

    setLoading(true);

    try {
      const settingsToSave: Partial<UserSettingsType> = {
        dark_mode: settings.appearance.find(s => s.id === 'dark_mode')?.enabled || false,
        compact_mode: settings.appearance.find(s => s.id === 'compact_mode')?.enabled || false,
        animations: settings.appearance.find(s => s.id === 'animations')?.enabled || true,
        email_notifications: settings.notifications.find(s => s.id === 'email_notifications')?.enabled || true,
        push_notifications: settings.notifications.find(s => s.id === 'push_notifications')?.enabled || false,
        weekly_summary: settings.notifications.find(s => s.id === 'weekly_summary')?.enabled || true,
        inventory_alerts: settings.notifications.find(s => s.id === 'inventory_alerts')?.enabled || true,
      };

      const success = await saveAllUserSettings(authState.user.id, settingsToSave);

      if (success) {
        toast.success('Settings saved successfully');
        setHasChanges(false);
      } else {
        toast.error('Failed to save settings');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const handleResetTutorials = () => {
    resetTutorials();
    toast.success('Tutorials have been reset. You will see them again on the relevant pages.');
  };

  const renderSectionContent = (sectionId: string) => {
    switch (sectionId) {
      case 'notifications':
        return (
          <NotificationSettings
            settings={settings.notifications}
            handleToggleSetting={(id, checked) => handleToggleSetting('notifications', id, checked)}
          />
        );
      case 'appearance':
        return (
          <AppearanceSettings
            settings={settings.appearance}
            handleToggleSetting={(id, checked) => handleToggleSetting('appearance', id, checked)}
          />
        );

      case 'account':
        return <AccountSettings profile={authState?.profile} />;
      case 'security':
        return <SecuritySettings />;
      case 'backup':
        return <BackupSettings />;
      case 'help':
        return <HelpSettings resetTutorials={handleResetTutorials} />;
      case 'api':
        return <ApiIntegrations />;
      case 'style-guide':
        // Redirect to the style guide page
        navigate('/style-guide');
        return <DefaultContent />;
      default:
        return <DefaultContent />;
    }
  };

  return (
    <PageTransition>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-32 overflow-x-hidden">
        <div className="glass-card glass-card-hover rounded-xl p-6 mb-8 transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold tracking-tight mb-2">Settings</h1>
              <p className="text-muted-foreground">
                Customize your StayFu experience
              </p>
            </div>
            {hasChanges && (
              <Button onClick={saveSettings} className="glass-interactive flex items-center gap-2" disabled={loading}>
                <Save size={18} />
                {loading ? 'Saving...' : 'Save Changes'}
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="md:col-span-1">
            <div className="glass-card glass-card-hover rounded-xl p-4 transition-all duration-300">
              <SettingsSidebar
                activeSection={activeSection}
                setActiveSection={setActiveSection}
              />
            </div>
          </div>

          <div className="md:col-span-3">
            <motion.div
              key={activeSection}
              className="glass-card glass-card-hover rounded-xl p-6 transition-all duration-300"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2 }}
            >
              {SettingsSections.find(section => section.id === activeSection) && (
                <SettingsHeader
                  activeSection={activeSection}
                  hasChanges={hasChanges}
                  loading={loading}
                  saveSettings={saveSettings}
                />
              )}

              {loading ? (
                <div className="flex items-center justify-center h-60">
                  <div className="text-center">
                    <div className="mx-auto w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mb-4"></div>
                    <p className="text-muted-foreground">
                      Loading settings...
                    </p>
                  </div>
                </div>
              ) : (
                renderSectionContent(activeSection)
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </PageTransition>
  );
};

export default Settings;
