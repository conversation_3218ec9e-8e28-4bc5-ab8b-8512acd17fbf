import React from 'react';

const DiagnosticPage: React.FC = () => {
  // Get environment variables directly
  const envVars = {
    mode: import.meta.env?.MODE || 'unknown',
    dev: import.meta.env?.DEV || false,
    prod: import.meta.env?.PROD || false,
    supabaseUrl: import.meta.env?.VITE_SUPABASE_URL || 'NOT_SET',
    supabaseKeySet: !!import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY,
    supabaseKeyPrefix: import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY?.substring(0, 20) || 'NOT_SET',
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    location: window.location.href,
  };

  const testSupabase = async () => {
    try {
      // Dynamic import to avoid build issues
      const { supabase } = await import('@/integrations/supabase/client');
      console.log('Supabase client loaded:', supabase);

      const { data, error } = await supabase.from('profiles').select('id').limit(1);
      console.log('Supabase test result:', { data, error });

      alert(`Supabase test: ${error ? 'FAILED - ' + error.message : 'SUCCESS'}`);
    } catch (err) {
      console.error('Supabase test error:', err);
      alert(`Supabase test FAILED: ${(err as Error).message}`);
    }
  };

  const testEdgeFunction = async () => {
    try {
      const { supabase } = await import('@/integrations/supabase/client');

      const { data, error } = await supabase.functions.invoke('accept-invitation-direct', {
        body: JSON.stringify({
          token: 'test-token',
          email: '<EMAIL>',
          password: 'testpass',
          first_name: 'Test',
          last_name: 'User'
        })
      });

      console.log('Edge Function test result:', { data, error });
      alert(`Edge Function test: ${error ? 'FAILED - ' + error.message : 'SUCCESS'}`);
    } catch (err) {
      console.error('Edge Function test error:', err);
      alert(`Edge Function test FAILED: ${(err as Error).message}`);
    }
  };

  return (
    <div style={{ padding: '32px', maxWidth: '800px', margin: '0 auto', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '24px' }}>🔍 Diagnostic Page</h1>

      <div style={{ marginBottom: '24px', backgroundColor: '#f5f5f5', padding: '16px', borderRadius: '8px' }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '12px' }}>Environment Variables</h2>
        <pre style={{ fontSize: '12px', backgroundColor: 'white', padding: '8px', borderRadius: '4px', border: '1px solid #ddd', overflow: 'auto' }}>
          {JSON.stringify(envVars, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '24px', backgroundColor: '#e3f2fd', padding: '16px', borderRadius: '8px' }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '12px' }}>Manual Tests</h2>
        <button
          type="button"
          onClick={testSupabase}
          style={{
            backgroundColor: '#2196f3',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '4px',
            border: 'none',
            marginRight: '8px',
            cursor: 'pointer'
          }}
        >
          Test Supabase Connection
        </button>
        <button
          type="button"
          onClick={testEdgeFunction}
          style={{
            backgroundColor: '#4caf50',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '4px',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          Test Edge Function
        </button>
      </div>

      <div style={{ backgroundColor: '#fff3e0', padding: '16px', borderRadius: '8px' }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '12px' }}>Instructions</h2>
        <p style={{ fontSize: '14px', marginBottom: '8px' }}>1. Check the environment variables above</p>
        <p style={{ fontSize: '14px', marginBottom: '8px' }}>2. Click the test buttons to verify functionality</p>
        <p style={{ fontSize: '14px', marginBottom: '8px' }}>3. Check the browser console for detailed logs</p>
        <p style={{ fontSize: '14px' }}>4. Results will appear as alerts and in the console</p>
      </div>
    </div>
  );
};

export default DiagnosticPage;
