import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Alert, AlertDescription } from '../components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'sonner';
import { supabase } from '../integrations/supabase/client';

const Login: React.FC = () => {
  const location = useLocation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const { signIn, authState } = useAuth();
  const navigate = useNavigate();

  // Initialize email from location state if available
  useEffect(() => {
    const { state } = location;
    if (state?.email) {
      setEmail(state.email);
    }

    // Show toast message if redirected due to session expiration
    if (state?.reason === 'session_expired') {
      toast.error('Your session has expired. Please log in again.');
    } else if (state?.reason === 'idle_timeout') {
      toast.info('You have been logged out due to inactivity.');
    }

    // Check if we're already authenticated and redirect to dashboard if so
    const checkAuth = async () => {
      try {
        const { data } = await supabase.auth.getSession();
        if (data?.session?.user) {
          console.log('User already authenticated, redirecting to dashboard');
          navigate('/dashboard');
        }
      } catch (error) {
        console.error('Error checking authentication status:', error);
      }
    };

    checkAuth();
  }, [location, navigate]);

  // Check for pending invitation in localStorage
  useEffect(() => {
    const pendingInvitation = localStorage.getItem('pendingInvitation');
    const pendingEmail = localStorage.getItem('pendingInvitationEmail');

    if (pendingEmail && !email) {
      console.log('Found pending invitation email:', pendingEmail);
      setEmail(pendingEmail);
    }
  }, [email]);

  // Redirect to dashboard if user becomes authenticated
  useEffect(() => {
    if (authState?.isAuthenticated && !isLoading) {
      console.log('User is authenticated, redirecting to dashboard');
      navigate('/dashboard');
    }
  }, [authState?.isAuthenticated, navigate, isLoading]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setLoginError(null); // Clear any previous errors

    try {
      console.log('Attempting to sign in user:', email);

      // Set a timeout to prevent hanging indefinitely
      const loginTimeout = setTimeout(() => {
        if (isLoading) {
          console.log('Login request is taking too long, showing timeout message');
          setLoginError('Login request is taking too long. Please try again.');
          toast.error('Login request is taking too long. Please try again.');
          setIsLoading(false);
        }
      }, 15000); // 15 seconds timeout

      try {
        // Call Supabase auth directly
        console.log('Calling Supabase auth directly...');
        const startTime = new Date().getTime();

        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        // Clear the timeout since we got a response
        clearTimeout(loginTimeout);

        const endTime = new Date().getTime();
        console.log(`Login response received after ${endTime - startTime}ms`);

        if (data?.user) {
          console.log('Login successful');
          toast.success('Successfully logged in');

          // Check if there's a pending invitation
          const pendingInvitation = localStorage.getItem('pendingInvitation');
          const pendingEmail = localStorage.getItem('pendingInvitationEmail');

          // If there's a pending invitation and the email matches, redirect to the invitation page
          if (pendingInvitation && pendingEmail === email) {
            console.log('Redirecting to pending invitation:', pendingInvitation);
            // Get team_id from URL if available
            const urlParams = new URLSearchParams(window.location.search);
            const teamId = urlParams.get('team_id');

            // Use setTimeout to ensure auth state is fully updated before navigation
            setTimeout(() => {
              // This will be handled by HashRouter as /#/invite
              navigate(`/invite?token=${pendingInvitation}${teamId ? `&team_id=${teamId}` : ''}`);
              // Clear localStorage after redirecting
              localStorage.removeItem('pendingInvitation');
              localStorage.removeItem('pendingInvitationEmail');
            }, 500);
          }
          // If there's a return URL in the state, navigate there
          else if (location.state?.returnUrl) {
            // Fix for URLs that might be full URLs
            const returnUrl = location.state.returnUrl;
            console.log('Return URL:', returnUrl);

            // Use setTimeout to ensure auth state is fully updated before navigation
            setTimeout(() => {
              // Check if it's a full URL or just a path
              if (returnUrl.startsWith('http')) {
                try {
                  // Extract the path from the URL
                  const url = new URL(returnUrl);
                  const path = url.pathname + url.search + url.hash;
                  console.log('Extracted path from URL:', path);
                  navigate(path);
                } catch (error) {
                  console.error('Error parsing return URL:', error);
                  navigate('/dashboard');
                }
              } else {
                // It's already a path, just navigate to it
                navigate(returnUrl);
              }
            }, 500);
          } else {
            // Check if there's a redirectAfterLogin in sessionStorage
            const redirectPath = sessionStorage.getItem('redirectAfterLogin');
            if (redirectPath) {
              console.log('Redirecting to stored path:', redirectPath);
              sessionStorage.removeItem('redirectAfterLogin'); // Clear it after use

              // Use setTimeout to ensure auth state is fully updated before navigation
              setTimeout(() => {
                navigate(redirectPath);
              }, 500);
            } else {
              // Default redirect to dashboard
              setTimeout(() => {
                // This will be handled by HashRouter as /#/dashboard
                navigate('/dashboard');
              }, 500);
            }
          }

          return;
        }

        // If we get here, there was an error with login
        if (error) {
          console.error('Sign in error:', error);

          // Set error message based on the error type
          let errorMessage = '';
          if (error.message?.includes('Invalid login credentials')) {
            errorMessage = 'Incorrect email or password. Please check your credentials and try again.';
          } else if (error.message?.includes('Email not confirmed')) {
            errorMessage = 'Please verify your email address before logging in. Check your inbox for a confirmation email.';
          } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
            errorMessage = 'Network error. Please check your internet connection and try again.';
          } else {
            errorMessage = 'Login failed. Please try again.';
          }

          // Set the error state and show toast
          setLoginError(errorMessage);
          toast.error(errorMessage);
          setIsLoading(false);
          return;
        }
      } catch (loginError) {
        // Clear the timeout since we got a response (even if it's an error)
        clearTimeout(loginTimeout);

        console.error('Error during login attempt:', loginError);
        setLoginError('An unexpected error occurred. Please try again.');
        toast.error('An unexpected error occurred. Please try again.');
        setIsLoading(false);
        return;
      }

      // We should never reach here since we return in all cases above
      console.error('Unexpected code path in login flow');
      setLoginError('An unexpected error occurred. Please try again.');
      toast.error('An unexpected error occurred. Please try again.');
      setIsLoading(false);
    } catch (error: any) {
      console.error('Login error:', error);

      // Set a generic error message
      const errorMessage = 'An unexpected error occurred. Please try again.';
      setLoginError(errorMessage);
      toast.error(errorMessage);

      // Reset loading state
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen">
      <Card className="w-full max-w-md glass-card">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Welcome Back</CardTitle>
          <CardDescription className="text-center">Enter your credentials to access your account</CardDescription>
        </CardHeader>
        <CardContent>
          {(location.state?.reason || loginError) && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {loginError ? loginError :
                  location.state.reason === 'session_expired'
                    ? 'Your session has expired. Please log in again.'
                    : location.state.reason === 'idle_timeout'
                      ? 'You have been logged out due to inactivity.'
                      : 'Please log in to continue.'}
              </AlertDescription>
            </Alert>
          )}
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">Email</label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (loginError) setLoginError(null); // Clear error when user types
                }}
                required
                readOnly={!!location.state?.email}
                className={loginError && loginError.includes('email') ? 'border-red-500' : ''}
              />
              {location.state?.email && (
                <p className="text-xs text-muted-foreground mt-1">
                  Email is pre-filled from the maintenance request
                </p>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label htmlFor="password" className="text-sm font-medium">Password</label>
                <Link to="/forgot-password" className="text-sm text-blue-600 hover:underline">
                  Forgot Password?
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  if (loginError) setLoginError(null); // Clear error when user types
                }}
                required
                className={loginError && (loginError.includes('password') || loginError.includes('credentials')) ? 'border-red-500' : ''}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Logging in...' : 'Log in'}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex-col space-y-2">
          <div className="text-sm text-center">
            Don't have an account?{' '}
            <Link to="/register" className="text-blue-600 hover:underline">
              Create an account
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Login;
