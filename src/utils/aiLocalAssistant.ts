// Local AI Assistant for helpful feedback and command validation

export interface LocalAIResponse {
  shouldProceed: boolean;
  response: {
    success: boolean;
    message: string;
    helpfulFeedback?: string;
    missingInfo?: string[];
    suggestions?: string[];
  };
}

export const processCommandLocally = (command: string): LocalAIResponse => {
  const lowerCommand = command.toLowerCase().trim();
  
  // Property-related commands
  if (lowerCommand.includes('add') && lowerCommand.includes('property')) {
    return validatePropertyCommand(command);
  }
  
  // Damage report commands (check before maintenance since they can overlap)
  if (lowerCommand.includes('damage') || lowerCommand.includes('damage report')) {
    return validateDamageReportCommand(command);
  }
  
  // Maintenance-related commands
  if (lowerCommand.includes('maintenance') || lowerCommand.includes('fix') || lowerCommand.includes('repair')) {
    return validateMaintenanceCommand(command);
  }
  
  // Inventory-related commands
  if (lowerCommand.includes('inventory') || lowerCommand.includes('stock') || lowerCommand.includes('down to')) {
    return validateInventoryCommand(command);
  }
  
  // Purchase order commands
  if (lowerCommand.includes('purchase') || lowerCommand.includes('order') || lowerCommand.includes('buy')) {
    return validatePurchaseOrderCommand(command);
  }
  
  // Default: proceed to backend
  return {
    shouldProceed: true,
    response: {
      success: true,
      message: "Processing your request..."
    }
  };
};

const validatePropertyCommand = (command: string): LocalAIResponse => {
  const lowerCommand = command.toLowerCase();
  const missing: string[] = [];
  
  // Check for property name
  const hasName = lowerCommand.includes('named') || lowerCommand.includes('called');
  if (!hasName) {
    missing.push('property name');
  }
  
  // Check for address
  const hasAddress = lowerCommand.includes('at ') || lowerCommand.includes('address') || 
                    /\d+\s+\w+\s+(street|road|ave|avenue|blvd|boulevard|drive|dr|lane|ln|way|ct|court)/i.test(command);
  if (!hasAddress) {
    missing.push('address');
  }
  
  // Check for bedrooms
  const hasBedrooms = lowerCommand.includes('bedroom') || lowerCommand.includes('bed');
  if (!hasBedrooms) {
    missing.push('number of bedrooms');
  }
  
  if (missing.length > 0) {
    return {
      shouldProceed: false,
      response: {
        success: false,
        message: "I'd love to help you add that property! I just need a bit more information:",
        missingInfo: missing,
        helpfulFeedback: `To add a property, I need: ${missing.join(', ')}`,
        suggestions: [
          "Add a property named [Name] at [Full Address] with [X] bedrooms",
          "Example: Add a property named Ocean View at 123 Beach Road, Miami, FL with 3 bedrooms"
        ]
      }
    };
  }
  
  return {
    shouldProceed: true,
    response: {
      success: true,
      message: "Creating your property..."
    }
  };
};

const validateDamageReportCommand = (command: string): LocalAIResponse => {
  const lowerCommand = command.toLowerCase();
  const missing: string[] = [];
  
  // Check for property/location
  const hasLocation = lowerCommand.includes('at ') || lowerCommand.includes('property') ||
                     lowerCommand.includes('house') || lowerCommand.includes('apartment') ||
                     lowerCommand.includes('cabin') || lowerCommand.includes('unit') ||
                     /\b[A-Z][a-z]+\b/.test(command); // Property names are often capitalized
  if (!hasLocation) {
    missing.push('which property');
  }
  
  // Check for damage description
  const hasDamageDescription = lowerCommand.includes('stain') || lowerCommand.includes('broken') ||
                              lowerCommand.includes('cracked') || lowerCommand.includes('damaged') ||
                              lowerCommand.includes('hole') || lowerCommand.includes('torn') ||
                              lowerCommand.includes('scratched') || lowerCommand.includes('dent') ||
                              lowerCommand.includes('burn') || lowerCommand.includes('water damage') ||
                              lowerCommand.includes('for a ') || lowerCommand.includes('of a ');
  if (!hasDamageDescription) {
    missing.push('damage description');
  }
  
  // Check for location within property (room, area)
  const hasRoomLocation = lowerCommand.includes('carpet') || lowerCommand.includes('wall') ||
                         lowerCommand.includes('kitchen') || lowerCommand.includes('bathroom') ||
                         lowerCommand.includes('bedroom') || lowerCommand.includes('living room') ||
                         lowerCommand.includes('floor') || lowerCommand.includes('ceiling') ||
                         lowerCommand.includes('window') || lowerCommand.includes('door') ||
                         lowerCommand.includes('on the') || lowerCommand.includes('in the');
  if (!hasRoomLocation) {
    missing.push('where in the property (room/area)');
  }
  
  if (missing.length > 0) {
    return {
      shouldProceed: false,
      response: {
        success: false,
        message: "I can help you create that damage report! I just need to know:",
        missingInfo: missing,
        helpfulFeedback: `For damage reports, please specify: ${missing.join(', ')}`,
        suggestions: [
          "Create a damage report at [property] for [damage description] in/on [location]",
          "Example: Create a damage report at Ocean View for a wine stain on the living room carpet"
        ]
      }
    };
  }
  
  return {
    shouldProceed: true,
    response: {
      success: true,
      message: "Creating your damage report..."
    }
  };
};

const validateMaintenanceCommand = (command: string): LocalAIResponse => {
  const lowerCommand = command.toLowerCase();
  const missing: string[] = [];
  
  // Check for what needs to be done
  const hasTask = lowerCommand.includes('fix') || lowerCommand.includes('repair') || 
                  lowerCommand.includes('clean') || lowerCommand.includes('check') ||
                  lowerCommand.includes('replace') || lowerCommand.includes('maintenance');
  if (!hasTask) {
    missing.push('what needs to be done');
  }
  
  // Check for property/location
  const hasLocation = lowerCommand.includes('at ') || lowerCommand.includes('property') ||
                     lowerCommand.includes('house') || lowerCommand.includes('apartment') ||
                     lowerCommand.includes('cabin') || lowerCommand.includes('unit');
  if (!hasLocation) {
    missing.push('which property');
  }
  
  if (missing.length > 0) {
    return {
      shouldProceed: false,
      response: {
        success: false,
        message: "I can help you create that maintenance task! I just need to know:",
        missingInfo: missing,
        helpfulFeedback: `For maintenance tasks, please specify: ${missing.join(' and ')}`,
        suggestions: [
          "Create a maintenance task to [action] at [property name]",
          "Example: Create a maintenance task to fix the broken sink at Mountain Cabin"
        ]
      }
    };
  }
  
  return {
    shouldProceed: true,
    response: {
      success: true,
      message: "Creating your maintenance task..."
    }
  };
};

const validateInventoryCommand = (command: string): LocalAIResponse => {
  const lowerCommand = command.toLowerCase();
  const missing: string[] = [];
  
  // Check for item name
  const hasItem = /\b(towel|glass|supply|soap|shampoo|toilet paper|sheets|pillow|blanket)\b/i.test(command);
  if (!hasItem) {
    missing.push('item name');
  }
  
  // Check for quantities
  const hasQuantity = /\d+/.test(command) || lowerCommand.includes('out of') || lowerCommand.includes('down to');
  if (!hasQuantity) {
    missing.push('current or target quantity');
  }
  
  if (missing.length > 0) {
    return {
      shouldProceed: false,
      response: {
        success: false,
        message: "I can help you manage that inventory! I just need to know:",
        missingInfo: missing,
        helpfulFeedback: `For inventory updates, please include: ${missing.join(' and ')}`,
        suggestions: [
          "We're down to [current number] [item], we need a minimum of [target]",
          "Example: We're down to only 2 bath towels, we need a minimum of 12"
        ]
      }
    };
  }
  
  return {
    shouldProceed: true,
    response: {
      success: true,
      message: "Updating your inventory..."
    }
  };
};

const validatePurchaseOrderCommand = (command: string): LocalAIResponse => {
  const lowerCommand = command.toLowerCase();
  
  // These are usually fine as-is, but provide helpful context
  if (lowerCommand.includes('all') || lowerCommand.includes('low stock') || lowerCommand.includes('everything')) {
    return {
      shouldProceed: true,
      response: {
        success: true,
        message: "Creating purchase order for all low stock items..."
      }
    };
  }
  
  // Check if they specified items
  const hasItems = lowerCommand.includes('towel') || lowerCommand.includes('supplies') ||
                   lowerCommand.includes('cleaning') || lowerCommand.includes('bathroom') ||
                   lowerCommand.includes('kitchen');
  
  if (!hasItems && !lowerCommand.includes('all')) {
    return {
      shouldProceed: false,
      response: {
        success: false,
        message: "I can create that purchase order! What items do you need?",
        helpfulFeedback: "Please specify what you'd like to order",
        suggestions: [
          "Create a purchase order for all low stock items",
          "Order cleaning supplies",
          "Create a purchase order for bathroom supplies"
        ]
      }
    };
  }
  
  return {
    shouldProceed: true,
    response: {
      success: true,
      message: "Creating your purchase order..."
    }
  };
};