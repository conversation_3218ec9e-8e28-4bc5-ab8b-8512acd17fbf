// Speech Recognition Polyfill for better browser support
export interface SpeechRecognitionPolyfill {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onstart: ((event: Event) => void) | null;
  onresult: ((event: any) => void) | null;
  onerror: ((event: any) => void) | null;
  onend: ((event: Event) => void) | null;
  start(): void;
  stop(): void;
}

// Check for different browser implementations
const getSpeechRecognition = (): typeof SpeechRecognition | null => {
  if (typeof window === 'undefined') return null;
  
  // Check for different browser implementations
  const SpeechRecognition = 
    (window as any).SpeechRecognition ||
    (window as any).webkitSpeechRecognition ||
    (window as any).mozSpeechRecognition ||
    (window as any).msSpeechRecognition;
    
  return SpeechRecognition;
};

// Create a polyfill for unsupported browsers
class SpeechRecognitionFallback implements SpeechRecognitionPolyfill {
  continuous = false;
  interimResults = false;
  lang = 'en-US';
  onstart: ((event: Event) => void) | null = null;
  onresult: ((event: any) => void) | null = null;
  onerror: ((event: any) => void) | null = null;
  onend: ((event: Event) => void) | null = null;

  start() {
    // Fallback: prompt user for text input
    setTimeout(() => {
      if (this.onstart) {
        this.onstart(new Event('start'));
      }
      
      const userInput = prompt('Voice recognition not supported. Please type your command:');
      
      if (userInput && userInput.trim()) {
        // Create a mock SpeechRecognitionEvent
        const mockEvent = {
          results: [{
            0: { transcript: userInput },
            length: 1
          }],
          resultIndex: 0
        } as any;
        
        if (this.onresult) {
          this.onresult(mockEvent);
        }
      } else {
        const errorEvent = {
          error: 'no-speech'
        } as any;
        
        if (this.onerror) {
          this.onerror(errorEvent);
        }
      }
      
      if (this.onend) {
        this.onend(new Event('end'));
      }
    }, 100);
  }

  stop() {
    // No-op for fallback
  }
}

export const createSpeechRecognition = (): SpeechRecognitionPolyfill | null => {
  const SpeechRecognition = getSpeechRecognition();
  
  if (SpeechRecognition) {
    return new SpeechRecognition();
  }
  
  // Return fallback for unsupported browsers
  return new SpeechRecognitionFallback();
};

export const isSpeechRecognitionSupported = (): boolean => {
  return getSpeechRecognition() !== null;
};

export const getBrowserSpeechSupport = (): {
  supported: boolean;
  browser: string;
  implementation: string;
} => {
  if (typeof window === 'undefined') {
    return { supported: false, browser: 'unknown', implementation: 'none' };
  }
  
  const userAgent = navigator.userAgent.toLowerCase();
  let browser = 'unknown';
  let implementation = 'none';
  
  if (userAgent.includes('chrome')) {
    browser = 'chrome';
    implementation = 'webkitSpeechRecognition';
  } else if (userAgent.includes('firefox')) {
    browser = 'firefox';
    implementation = 'experimental';
  } else if (userAgent.includes('safari')) {
    browser = 'safari';
    implementation = 'webkitSpeechRecognition';
  } else if (userAgent.includes('edge')) {
    browser = 'edge';
    implementation = 'webkitSpeechRecognition';
  }
  
  const SpeechRecognition = getSpeechRecognition();
  
  return {
    supported: SpeechRecognition !== null,
    browser,
    implementation: SpeechRecognition ? implementation : 'fallback'
  };
};