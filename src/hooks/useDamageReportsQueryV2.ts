import { useState, useCallback, useEffect } from 'react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useImpersonation } from '@/contexts/ImpersonationContext';

export interface DamageReport {
  id: string;
  title: string;
  description: string;
  status: string;
  property_id: string;
  property_name?: string;
  created_at: string;
  updated_at: string;
  provider_id?: string;
  provider_name?: string;
  platform?: string;
}

export interface DamageReportsData {
  damageReports: DamageReport[];
  loading: boolean;
  error: string | null;
  isError: boolean;
  fetchDamageReports: () => Promise<void>;
  addDamageReport: (report: Omit<DamageReport, 'id' | 'created_at' | 'updated_at'>) => Promise<boolean>;
  updateDamageReport: (id: string, report: Partial<DamageReport>) => Promise<boolean>;
  deleteDamageReport: (id: string) => Promise<boolean>;
}

/**
 * A standardized hook for fetching damage reports using React Query
 * This follows the same pattern as useOperationsDataQuery for consistency
 */
export const useDamageReportsQueryV2 = (): DamageReportsData => {
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);
  const { isImpersonating } = useImpersonation();

  // Function to retry data fetching manually
  const retryFetch = useCallback(async () => {
    console.log('[useDamageReportsQueryV2] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['damageReportsV2'] });
    await queryClient.refetchQueries({ queryKey: ['damageReportsV2'] });
  }, [queryClient]);

  // Fetch damage reports
  const {
    data: damageReports = [],
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['damageReportsV2'],
    queryFn: async () => {
      try {
        if (!userId) {
          throw new Error('User not authenticated');
        }

        console.log(`[useDamageReportsQueryV2] Fetching damage reports (attempt ${retryCount + 1})`);

        // Use our RPC function to get damage reports
        const { data: reports, error: reportsError } = await supabase.rpc(
          'get_user_damage_reports_simple',
          { p_user_id: userId }
        );

        if (reportsError) {
          console.error('[useDamageReportsQueryV2] RPC function error:', reportsError);
          throw reportsError;
        }

        if (!reports || reports.length === 0) {
          console.log('[useDamageReportsQueryV2] No damage reports found');
          return [];
        }

        console.log(`[useDamageReportsQueryV2] Successfully loaded ${reports.length} damage reports`);

        // Apply impersonation filter if needed
        let filteredReports = reports;
        if (isImpersonating) {
          console.log('[useDamageReportsQueryV2] Impersonating user, filtering damage reports');
          filteredReports = reports.filter(report => report.user_id === userId);
        }

        return filteredReports;
      } catch (err: any) {
        console.error('[useDamageReportsQueryV2] Error fetching damage reports:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId,
    networkMode: 'always'
  });

  // Add damage report mutation
  const addReportMutation = useMutation({
    mutationFn: async (report: Omit<DamageReport, 'id' | 'created_at' | 'updated_at'>) => {
      console.log('[useDamageReportsQueryV2] Adding damage report:', report);

      // Get the current user directly from Supabase auth
      const { data: userData, error: userError } = await supabase.auth.getUser();

      if (userError) {
        console.error('[useDamageReportsQueryV2] Error getting user:', userError);
        throw userError;
      }

      if (!userData.user) {
        throw new Error('User not authenticated');
      }

      const currentUserId = userData.user.id;
      console.log('[useDamageReportsQueryV2] Current user ID from auth:', currentUserId);

      const { data, error } = await supabase
        .from('damage_reports')
        .insert([{
          ...report,
          user_id: currentUserId
        }])
        .select('*');

      if (error) {
        console.error('[useDamageReportsQueryV2] Error adding damage report:', error);
        throw error;
      }

      return data?.[0] || null;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['damageReportsV2'] });
    }
  });

  // Update damage report mutation
  const updateReportMutation = useMutation({
    mutationFn: async ({ id, report }: { id: string, report: Partial<DamageReport> }) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useDamageReportsQueryV2] Updating damage report ${id}:`, report);

      const { data, error } = await supabase
        .from('damage_reports')
        .update({
          ...report,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select();

      if (error) {
        console.error('[useDamageReportsQueryV2] Error updating damage report:', error);
        throw error;
      }

      return data?.[0] || null;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['damageReportsV2'] });
    }
  });

  // Delete damage report mutation
  const deleteReportMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useDamageReportsQueryV2] Deleting damage report ${id}`);

      const { error } = await supabase
        .from('damage_reports')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('[useDamageReportsQueryV2] Error deleting damage report:', error);
        throw error;
      }

      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['damageReportsV2'] });
    }
  });

  // Wrapper functions for mutations
  const addDamageReport = async (report: Omit<DamageReport, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> => {
    try {
      await addReportMutation.mutateAsync(report);
      return true;
    } catch (error) {
      return false;
    }
  };

  const updateDamageReport = async (id: string, report: Partial<DamageReport>): Promise<boolean> => {
    try {
      await updateReportMutation.mutateAsync({ id, report });
      return true;
    } catch (error) {
      return false;
    }
  };

  const deleteDamageReport = async (id: string): Promise<boolean> => {
    try {
      await deleteReportMutation.mutateAsync(id);
      return true;
    } catch (error) {
      return false;
    }
  };

  // Add error retry effect similar to usePurchaseOrders
  useEffect(() => {
    // Only retry on actual errors, not on empty data
    if (isError) {
      const timer = setTimeout(() => {
        if (retryCount < 3) {
          console.log('[useDamageReportsQueryV2] Auto-retrying data fetch due to error');
          retryFetch();
        } else {
          console.error('[useDamageReportsQueryV2] Failed to load damage reports after multiple attempts');
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isError, retryCount, retryFetch]);

  return {
    damageReports,
    loading: isLoading,
    error: error ? String(error) : null,
    isError,
    fetchDamageReports: retryFetch,
    addDamageReport,
    updateDamageReport,
    deleteDamageReport
  };
};
