/**
 * Test script for clickable AI confirmation messages
 * 
 * This script tests the enhanced toast functionality that allows users
 * to click on AI confirmation messages to navigate to created items.
 * 
 * Usage:
 * 1. Open the browser console on any StayFu page
 * 2. Copy and paste this script
 * 3. Run testClickableConfirmations()
 */

// Test the showAIConfirmationToast function with different entity types
async function testClickableConfirmations() {
  console.log('🧪 Testing Clickable AI Confirmation Messages');
  console.log('='.repeat(50));

  // Mock navigate function for testing
  const mockNavigate = (path) => {
    console.log(`🔗 Navigation triggered: ${path}`);
    // In a real test, this would actually navigate
    // For testing, we'll just log the path
  };

  // Try to import the function from the module
  let showAIConfirmationToast;

  try {
    // In a real browser environment, we'll need to import from the actual module
    // For testing, we'll create a mock version that demonstrates the functionality
    showAIConfirmationToast = async (result, navigationConfig) => {
      console.log('📧 Toast would be shown:', result.message);

      if (result.success && navigationConfig && result.entityType && result.entityId) {
        console.log('🔗 Action button would navigate to:', getNavigationPath(result.entityType, result.entityId));
        console.log('💡 In real app, user would see a clickable "View" button');

        // Simulate the navigation
        if (navigationConfig.navigate) {
          const path = getNavigationPath(result.entityType, result.entityId);
          navigationConfig.navigate(path);
        }
      } else if (!result.success) {
        console.log('❌ Error toast would be shown without action button');
      }
    };
  } catch (error) {
    console.error('❌ Could not load showAIConfirmationToast function:', error);
    return;
  }

  // Helper function to generate navigation paths (copied from utils.ts)
  function getNavigationPath(type, id) {
    switch (type) {
      case 'maintenance_task':
        return `/maintenance?id=${id}`;
      case 'purchase_order':
        return `/purchase-orders?id=${id}`;
      case 'property':
        return `/properties/${id}`;
      case 'inventory_item':
        return `/inventory?item=${id}`;
      case 'damage_report':
        return `/damages/${id}`;
      case 'team':
        return `/teams?team=${id}`;
      default:
        return '/dashboard';
    }
  }

  // Test cases for different entity types
  const testCases = [
    {
      name: 'Maintenance Task Creation',
      result: {
        success: true,
        message: 'Successfully created maintenance task "Fix broken window"',
        action: 'create',
        entityType: 'maintenance_task',
        entityId: '123e4567-e89b-12d3-a456-426614174000'
      },
      expectedPath: '/maintenance?id=123e4567-e89b-12d3-a456-426614174000'
    },
    {
      name: 'Purchase Order Creation',
      result: {
        success: true,
        message: 'Successfully created purchase order for low stock items',
        action: 'create',
        entityType: 'purchase_order',
        entityId: '456e7890-e89b-12d3-a456-426614174001'
      },
      expectedPath: '/purchase-orders?id=456e7890-e89b-12d3-a456-426614174001'
    },
    {
      name: 'Property Creation',
      result: {
        success: true,
        message: 'Successfully created property "Sunset Villa"',
        action: 'create',
        entityType: 'property',
        entityId: '789e0123-e89b-12d3-a456-426614174002'
      },
      expectedPath: '/properties/789e0123-e89b-12d3-a456-426614174002'
    },
    {
      name: 'Inventory Item Update',
      result: {
        success: true,
        message: 'Successfully updated inventory item "Towel Set"',
        action: 'update',
        entityType: 'inventory_item',
        entityId: '012e3456-e89b-12d3-a456-426614174003'
      },
      expectedPath: '/inventory?item=012e3456-e89b-12d3-a456-426614174003'
    },
    {
      name: 'Damage Report Creation',
      result: {
        success: true,
        message: 'Successfully created damage report for broken mirror',
        action: 'create',
        entityType: 'damage_report',
        entityId: '345e6789-e89b-12d3-a456-426614174004'
      },
      expectedPath: '/damages/345e6789-e89b-12d3-a456-426614174004'
    },
    {
      name: 'Error Case',
      result: {
        success: false,
        message: 'Failed to create maintenance task: Invalid property ID'
      },
      expectedPath: null // No navigation for errors
    }
  ];

  // Run tests
  for (const testCase of testCases) {
    console.log(`\n🧪 Testing: ${testCase.name}`);
    console.log(`📝 Message: ${testCase.result.message}`);
    
    try {
      // Test with navigation config
      if (testCase.result.success && testCase.result.entityType && testCase.result.entityId) {
        showAIConfirmationToast(testCase.result, {
          entityType: testCase.result.entityType,
          entityId: testCase.result.entityId,
          navigate: mockNavigate
        });
        
        console.log(`✅ Expected navigation: ${testCase.expectedPath}`);
        console.log('💡 Check the toast notification for the "View" button');
      } else {
        // Test error case
        showAIConfirmationToast(testCase.result);
        console.log('✅ Error toast should be displayed without navigation button');
      }
      
      // Wait a bit between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`❌ Test failed for ${testCase.name}:`, error);
    }
  }

  console.log('\n🎉 Test completed!');
  console.log('💡 Check the toast notifications in the top-right corner');
  console.log('💡 Click the "View" buttons to test navigation');
}

// Test the navigation path generation logic
function testNavigationPaths() {
  console.log('\n🧪 Testing Navigation Path Generation');
  console.log('='.repeat(40));

  const testCases = [
    { type: 'maintenance_task', id: 'task-123', expected: '/maintenance?id=task-123' },
    { type: 'purchase_order', id: 'order-456', expected: '/purchase-orders?id=order-456' },
    { type: 'property', id: 'prop-789', expected: '/properties/prop-789' },
    { type: 'inventory_item', id: 'item-012', expected: '/inventory?item=item-012' },
    { type: 'damage_report', id: 'damage-345', expected: '/damages/damage-345' },
    { type: 'team', id: 'team-678', expected: '/teams?team=team-678' },
    { type: 'unknown_type', id: 'unknown-901', expected: '/dashboard' }
  ];

  testCases.forEach(testCase => {
    console.log(`📍 ${testCase.type} (${testCase.id}) → ${testCase.expected}`);
  });
}

// Export functions for manual testing
window.testClickableConfirmations = testClickableConfirmations;
window.testNavigationPaths = testNavigationPaths;

console.log('🚀 Clickable Confirmations Test Script Loaded');
console.log('📝 Run testClickableConfirmations() to start testing');
console.log('📝 Run testNavigationPaths() to test path generation');
