/**
 * Test script for dynamic sidebar improvements
 * Tests:
 * 1. Sidebar height adjustment without scrolling
 * 2. Logo switching between full logo and favicon when collapsed
 * 3. Proper flex layout behavior
 * 4. Dynamic spacing and text sizes based on available height
 * 5. Responsive behavior on window resize
 */

// Test sidebar height and scrolling behavior
function testSidebarHeight() {
  console.log('🧪 Testing sidebar height and scrolling...');
  
  const sidebar = document.querySelector('aside.sidebar');
  if (!sidebar) {
    console.error('❌ Sidebar not found');
    return false;
  }
  
  // Check if sidebar has proper height
  const sidebarHeight = sidebar.offsetHeight;
  const windowHeight = window.innerHeight;
  
  console.log(`📏 Sidebar height: ${sidebarHeight}px, Window height: ${windowHeight}px`);
  
  if (Math.abs(sidebarHeight - windowHeight) > 5) {
    console.error('❌ Sidebar height does not match window height');
    return false;
  }
  
  // Check if sidebar has overflow-y-auto removed from main container
  const sidebarStyles = window.getComputedStyle(sidebar);
  if (sidebarStyles.overflowY === 'auto') {
    console.error('❌ Sidebar still has overflow-y: auto on main container');
    return false;
  }
  
  // Check if navigation section has overflow-y-auto
  const nav = sidebar.querySelector('nav');
  if (!nav) {
    console.error('❌ Navigation section not found');
    return false;
  }
  
  const navStyles = window.getComputedStyle(nav);
  if (navStyles.overflowY !== 'auto') {
    console.error('❌ Navigation section should have overflow-y: auto');
    return false;
  }
  
  console.log('✅ Sidebar height and scrolling behavior correct');
  return true;
}

// Test logo switching behavior
function testLogoSwitching() {
  console.log('🧪 Testing logo switching...');
  
  const logoImg = document.querySelector('aside.sidebar img[alt="StayFu Logo"]');
  if (!logoImg) {
    console.error('❌ Logo image not found');
    return false;
  }
  
  // Check current logo source
  const currentSrc = logoImg.src;
  console.log(`🖼️ Current logo source: ${currentSrc}`);
  
  // Find collapse button
  const collapseButton = document.querySelector('aside.sidebar button[aria-label*="sidebar" i], aside.sidebar button svg');
  if (!collapseButton) {
    console.error('❌ Collapse button not found');
    return false;
  }
  
  // Get the actual button element (might be nested)
  const button = collapseButton.closest('button');
  if (!button) {
    console.error('❌ Could not find button element');
    return false;
  }
  
  console.log('🔄 Testing logo switching by toggling sidebar...');
  
  // Store initial state
  const initialSrc = logoImg.src;
  const sidebar = document.querySelector('aside.sidebar');
  const initialWidth = sidebar.offsetWidth;
  
  // Click to toggle
  button.click();
  
  // Wait for transition
  setTimeout(() => {
    const newSrc = logoImg.src;
    const newWidth = sidebar.offsetWidth;
    
    console.log(`📏 Width changed from ${initialWidth}px to ${newWidth}px`);
    console.log(`🖼️ Logo changed from ${initialSrc} to ${newSrc}`);
    
    // Check if logo source changed appropriately
    const isCollapsed = newWidth < 100; // Collapsed should be around 64px
    const expectedSrc = isCollapsed ? '/icons/icon-32x32.png' : '/icons/logo.png';
    
    if (!newSrc.includes(expectedSrc)) {
      console.error(`❌ Logo source incorrect. Expected to contain: ${expectedSrc}, got: ${newSrc}`);
      return false;
    }
    
    console.log('✅ Logo switching behavior correct');
    
    // Toggle back
    button.click();
    
    return true;
  }, 500);
}

// Test flex layout behavior
function testFlexLayout() {
  console.log('🧪 Testing flex layout...');
  
  const sidebar = document.querySelector('aside.sidebar');
  if (!sidebar) {
    console.error('❌ Sidebar not found');
    return false;
  }
  
  // Check if sidebar is flex column
  const sidebarStyles = window.getComputedStyle(sidebar);
  if (sidebarStyles.display !== 'flex' || sidebarStyles.flexDirection !== 'column') {
    console.error('❌ Sidebar should be flex column');
    return false;
  }
  
  // Check header section
  const header = sidebar.querySelector('div:first-child');
  if (!header) {
    console.error('❌ Header section not found');
    return false;
  }
  
  const headerStyles = window.getComputedStyle(header);
  if (!headerStyles.flexShrink || headerStyles.flexShrink === '1') {
    console.error('❌ Header should have flex-shrink-0');
    return false;
  }
  
  // Check navigation section
  const nav = sidebar.querySelector('nav');
  if (!nav) {
    console.error('❌ Navigation section not found');
    return false;
  }
  
  const navStyles = window.getComputedStyle(nav);
  if (navStyles.flexGrow !== '1') {
    console.error('❌ Navigation should have flex-grow');
    return false;
  }
  
  // Check footer section
  const footer = sidebar.querySelector('div:last-child');
  if (!footer) {
    console.error('❌ Footer section not found');
    return false;
  }
  
  const footerStyles = window.getComputedStyle(footer);
  if (!footerStyles.flexShrink || footerStyles.flexShrink === '1') {
    console.error('❌ Footer should have flex-shrink-0');
    return false;
  }
  
  console.log('✅ Flex layout behavior correct');
  return true;
}

// Test dynamic spacing and text size adaptation
function testDynamicSpacing() {
  console.log('🧪 Testing dynamic spacing and text size adaptation...');

  const sidebar = document.querySelector('aside.sidebar');
  if (!sidebar) {
    console.error('❌ Sidebar not found');
    return false;
  }

  // Get current window height and sidebar height
  const sidebarHeight = sidebar.offsetHeight;
  const windowHeight = window.innerHeight;

  console.log(`📏 Sidebar height: ${sidebarHeight}px, Window height: ${windowHeight}px`);

  // Check navigation item spacing
  const navItems = sidebar.querySelectorAll('nav ul li a');
  if (navItems.length === 0) {
    console.error('❌ Navigation items not found');
    return false;
  }

  const firstNavItem = navItems[0];
  const navStyles = window.getComputedStyle(firstNavItem);
  const paddingY = parseFloat(navStyles.paddingTop) + parseFloat(navStyles.paddingBottom);

  console.log(`📏 Current navigation item vertical padding: ${paddingY}px`);

  // Check text size
  const textSpan = sidebar.querySelector('nav span');
  let fontSize = 14; // default
  if (textSpan) {
    const textStyles = window.getComputedStyle(textSpan);
    fontSize = parseFloat(textStyles.fontSize);
    console.log(`📝 Current navigation text size: ${fontSize}px`);
  }

  // Check icon sizes
  let iconSize = 20; // default
  const icons = sidebar.querySelectorAll('nav svg');
  if (icons.length > 0) {
    iconSize = parseInt(icons[0].getAttribute('width') || icons[0].getAttribute('size') || '20');
    console.log(`🎯 Current icon size: ${iconSize}px`);
  }

  // Calculate expected sizes based on available space
  const totalNavItems = navItems.length + 3; // +3 for settings, sign out, user profile
  const headerHeight = 80;
  const footerBaseHeight = 120;
  const availableNavHeight = sidebarHeight - headerHeight - footerBaseHeight;
  const idealItemHeight = availableNavHeight / totalNavItems;

  console.log(`📊 Available nav height: ${availableNavHeight}px, Ideal item height: ${idealItemHeight}px`);

  // Verify that spacing is appropriate for the available space
  let expectedMaxPadding, expectedMaxFontSize, expectedMaxIconSize;

  if (idealItemHeight >= 44) {
    expectedMaxPadding = 24; // py-3 = 12px top + 12px bottom
    expectedMaxFontSize = 14; // text-sm
    expectedMaxIconSize = 20;
  } else if (idealItemHeight >= 36) {
    expectedMaxPadding = 16; // py-2 = 8px top + 8px bottom
    expectedMaxFontSize = 14; // text-sm
    expectedMaxIconSize = 20;
  } else if (idealItemHeight >= 30) {
    expectedMaxPadding = 12; // py-1.5 = 6px top + 6px bottom
    expectedMaxFontSize = 14; // text-sm
    expectedMaxIconSize = 18;
  } else {
    expectedMaxPadding = 8; // py-1 = 4px top + 4px bottom
    expectedMaxFontSize = 12; // text-xs
    expectedMaxIconSize = 16;
  }

  console.log(`🎯 Expected max padding: ${expectedMaxPadding}px, font size: ${expectedMaxFontSize}px, icon size: ${expectedMaxIconSize}px`);

  // Verify the values are within expected ranges
  if (paddingY > expectedMaxPadding + 2) { // +2px tolerance
    console.error(`❌ Padding too large for available space. Expected ≤${expectedMaxPadding}px, got ${paddingY}px`);
    return false;
  }

  if (fontSize > expectedMaxFontSize + 1) { // +1px tolerance
    console.error(`❌ Font size too large for available space. Expected ≤${expectedMaxFontSize}px, got ${fontSize}px`);
    return false;
  }

  if (iconSize > expectedMaxIconSize + 2) { // +2px tolerance
    console.error(`❌ Icon size too large for available space. Expected ≤${expectedMaxIconSize}px, got ${iconSize}px`);
    return false;
  }

  console.log('✅ Dynamic spacing and sizing appropriate for available space');
  return true;
}

// Test responsive behavior on window resize
function testResponsiveBehavior() {
  console.log('🧪 Testing responsive behavior on window resize...');

  const sidebar = document.querySelector('aside.sidebar');
  if (!sidebar) {
    console.error('❌ Sidebar not found');
    return false;
  }

  // Store initial values
  const initialHeight = window.innerHeight;
  const initialNavItem = sidebar.querySelector('nav ul li a');
  if (!initialNavItem) {
    console.error('❌ Navigation item not found');
    return false;
  }

  const initialPadding = parseFloat(window.getComputedStyle(initialNavItem).paddingTop) +
                        parseFloat(window.getComputedStyle(initialNavItem).paddingBottom);

  console.log(`📏 Initial window height: ${initialHeight}px, initial padding: ${initialPadding}px`);

  // Simulate window resize (we can't actually resize in a test, but we can check if the logic exists)
  // Check if resize event listener is attached
  const hasResizeListener = window.addEventListener.toString().includes('resize') ||
                           document.addEventListener.toString().includes('resize');

  if (!hasResizeListener) {
    console.log('⚠️ Cannot verify resize listener attachment in test environment');
  }

  console.log('✅ Responsive behavior test completed (limited in test environment)');
  return true;
}

// Run all tests
function runSidebarTests() {
  console.log('🚀 Starting sidebar improvement tests...');
  
  const tests = [
    testSidebarHeight,
    testFlexLayout,
    testLogoSwitching,
    testDynamicSpacing,
    testResponsiveBehavior
  ];
  
  let passed = 0;
  let total = tests.length;
  
  tests.forEach((test, index) => {
    try {
      if (test()) {
        passed++;
      }
    } catch (error) {
      console.error(`❌ Test ${index + 1} failed with error:`, error);
    }
  });
  
  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All sidebar improvement tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Check the console for details.');
  }
}

// Auto-run tests when script loads
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runSidebarTests);
  } else {
    runSidebarTests();
  }
}

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testSidebarHeight,
    testLogoSwitching,
    testFlexLayout,
    testDynamicSpacing,
    testResponsiveBehavior,
    runSidebarTests
  };
}
