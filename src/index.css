@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Core Shadcn Variables - Light Mode */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 47.4% 11.2%;
    --radius: 0.5rem;

    /* Enhanced Semantic Variables - Light Mode */
    /* Background Hierarchy */
    --bg-primary: 0 0% 100%;           /* Main page background */
    --bg-secondary: 0 0% 98%;          /* Card backgrounds */
    --bg-tertiary: 0 0% 96%;           /* Elevated surfaces */
    --bg-interactive: 210 40% 94%;     /* Hover states */

    /* Text Hierarchy */
    --text-primary: 222.2 84% 4.9%;   /* Main text */
    --text-secondary: 215.4 16.3% 20%; /* Secondary text - darker for better contrast */
    --text-muted: 215.4 16.3% 46.9%;  /* Muted text */
    --text-inverse: 210 40% 98%;       /* Text on colored backgrounds */

    /* Status Colors - Light Mode */
    --status-success-bg: 142 76% 95%;
    --status-success-text: 142 76% 25%;
    --status-success-border: 142 76% 85%;

    --status-warning-bg: 45 93% 95%;
    --status-warning-text: 45 93% 25%;
    --status-warning-border: 45 93% 85%;

    --status-error-bg: 0 84% 95%;
    --status-error-text: 0 84% 25%;
    --status-error-border: 0 84% 85%;

    --status-info-bg: 210 100% 95%;
    --status-info-text: 210 100% 25%;
    --status-info-border: 210 100% 85%;

    /* Glass Effects - Light Mode */
    --glass-bg: 255 255 255 / 0.2;
    --glass-border: 255 255 255 / 0.3;
    --glass-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);

    /* Sidebar Variables - Light Mode */
    --sidebar-background: 210 40% 96.1%;
    --sidebar-foreground: 222.2 47.4% 11.2%;
    --sidebar-primary: 222.2 47.4% 11.2%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 222.2 47.4% 11.2%;
  }

  .dark {
    /* Core Shadcn Variables - Dark Mode */
    --background: 220 15% 4%;          /* Very dark background */
    --foreground: 210 20% 98%;         /* Light text */
    --card: 220 15% 6%;                /* Dark card background */
    --card-foreground: 210 20% 98%;
    --popover: 220 15% 6%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 100% 50%;           /* Bright blue primary */
    --primary-foreground: 0 0% 100%;
    --secondary: 220 15% 8%;
    --secondary-foreground: 210 20% 98%;
    --muted: 220 15% 8%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 210 100% 50%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 15% 12%;
    --input: 220 15% 12%;
    --ring: 210 100% 50%;

    /* Enhanced Semantic Variables - Dark Mode */
    /* Background Hierarchy */
    --bg-primary: 220 15% 4%;          /* Main page background - very dark */
    --bg-secondary: 220 15% 6%;        /* Card backgrounds - slightly lighter */
    --bg-tertiary: 220 15% 8%;         /* Elevated surfaces - modals, dropdowns */
    --bg-interactive: 220 15% 12%;     /* Hover states and interactive elements */

    /* Text Hierarchy */
    --text-primary: 210 20% 98%;       /* Main text - high contrast */
    --text-secondary: 210 20% 85%;     /* Secondary text - improved contrast for dark mode */
    --text-muted: 215 20.2% 75%;       /* Muted text - improved contrast */
    --text-inverse: 220 15% 4%;        /* Text on colored backgrounds */

    /* Status Colors - Dark Mode */
    --status-success-bg: 142 76% 15%;
    --status-success-text: 142 76% 85%;
    --status-success-border: 142 76% 25%;

    --status-warning-bg: 45 93% 15%;
    --status-warning-text: 45 93% 85%;
    --status-warning-border: 45 93% 25%;

    --status-error-bg: 0 84% 15%;
    --status-error-text: 0 84% 85%;
    --status-error-border: 0 84% 25%;

    --status-info-bg: 210 100% 15%;
    --status-info-text: 210 100% 85%;
    --status-info-border: 210 100% 25%;

    /* Glass Effects - Dark Mode */
    --glass-bg: 0 0 0 / 0.6;
    --glass-border: 255 255 255 / 0.1;
    --glass-shadow: 0 4px 30px rgba(0, 0, 0, 0.3);

    /* Sidebar Variables - Dark Mode */
    --sidebar-background: 220 15% 6%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 210 20% 98%;
    --sidebar-primary-foreground: 220 15% 6%;
    --sidebar-accent: 220 15% 8%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 220 15% 12%;
    --sidebar-ring: 210 100% 50%;
  }
}

@layer components {
  /* Theme-Aware Surface Classes */
  .surface-primary {
    background-color: hsl(var(--bg-primary));
    color: hsl(var(--text-primary));
  }

  .surface-secondary {
    background-color: hsl(var(--bg-secondary));
    color: hsl(var(--text-primary));
  }

  .surface-tertiary {
    background-color: hsl(var(--bg-tertiary));
    color: hsl(var(--text-primary));
  }

  .surface-interactive {
    background-color: hsl(var(--bg-interactive));
    color: hsl(var(--text-primary));
  }

  /* Theme-Aware Text Classes */
  .text-primary {
    color: hsl(var(--text-primary));
  }

  .text-secondary {
    color: hsl(var(--text-secondary));
  }

  .text-muted {
    color: hsl(var(--text-muted));
  }

  .text-inverse {
    color: hsl(var(--text-inverse));
  }

  /* Global Icon Color Inheritance */
  svg {
    color: inherit;
  }

  /* Ensure Lucide icons inherit proper colors */
  [data-lucide] {
    color: inherit;
  }

  /* Global Text Color Inheritance - Ensure all text is visible in both modes */
  * {
    color: inherit;
  }

  /* Set default text color on body */
  body {
    color: hsl(var(--foreground));
  }

  /* Ensure headings inherit proper colors */
  h1, h2, h3, h4, h5, h6 {
    color: inherit;
  }

  /* Ensure paragraphs and spans inherit proper colors */
  p, span, div {
    color: inherit;
  }

  /* Ensure font-medium elements have proper text color */
  .font-medium {
    color: hsl(var(--text-primary));
  }

  /* Ensure font-semibold elements have proper text color */
  .font-semibold {
    color: hsl(var(--text-primary));
  }

  /* Ensure font-bold elements have proper text color */
  .font-bold {
    color: hsl(var(--text-primary));
  }

  /* Fix for text-secondary in dark mode */
  .dark .text-secondary {
    color: hsl(var(--text-secondary));
  }

  /* Theme-Aware Status Classes */
  .status-success {
    background-color: hsl(var(--status-success-bg));
    color: hsl(var(--status-success-text));
    border-color: hsl(var(--status-success-border));
  }

  .status-warning {
    background-color: hsl(var(--status-warning-bg));
    color: hsl(var(--status-warning-text));
    border-color: hsl(var(--status-warning-border));
  }

  .status-error {
    background-color: hsl(var(--status-error-bg));
    color: hsl(var(--status-error-text));
    border-color: hsl(var(--status-error-border));
  }

  .status-info {
    background-color: hsl(var(--status-info-bg));
    color: hsl(var(--status-info-text));
    border-color: hsl(var(--status-info-border));
  }

  /* Enhanced Status Colors - Comprehensive Theme-Aware System */
  .status-critical {
    background-color: hsl(0 84% 95% / 1);
    color: hsl(0 84% 25% / 1);
    border-color: hsl(0 84% 85% / 1);
  }

  .dark .status-critical {
    background-color: hsl(0 84% 15% / 1);
    color: hsl(0 84% 85% / 1);
    border-color: hsl(0 84% 25% / 1);
  }

  .status-high {
    background-color: hsl(25 95% 95% / 1);
    color: hsl(25 95% 25% / 1);
    border-color: hsl(25 95% 85% / 1);
  }

  .dark .status-high {
    background-color: hsl(25 95% 15% / 1);
    color: hsl(25 95% 85% / 1);
    border-color: hsl(25 95% 25% / 1);
  }

  .status-medium {
    background-color: hsl(45 93% 95% / 1);
    color: hsl(45 93% 25% / 1);
    border-color: hsl(45 93% 85% / 1);
  }

  .dark .status-medium {
    background-color: hsl(45 93% 15% / 1);
    color: hsl(45 93% 85% / 1);
    border-color: hsl(45 93% 25% / 1);
  }

  .status-low {
    background-color: hsl(60 90% 95% / 1);
    color: hsl(60 90% 25% / 1);
    border-color: hsl(60 90% 85% / 1);
  }

  .dark .status-low {
    background-color: hsl(60 90% 15% / 1);
    color: hsl(60 90% 85% / 1);
    border-color: hsl(60 90% 25% / 1);
  }

  /* Badge Status Colors */
  .badge-critical {
    background-color: hsl(0 84% 95% / 1);
    color: hsl(0 84% 25% / 1);
    border: 1px solid hsl(0 84% 85% / 1);
  }

  .dark .badge-critical {
    background-color: hsl(0 84% 15% / 1);
    color: hsl(0 84% 85% / 1);
    border: 1px solid hsl(0 84% 25% / 1);
  }

  .badge-high {
    background-color: hsl(25 95% 95% / 1);
    color: hsl(25 95% 25% / 1);
    border: 1px solid hsl(25 95% 85% / 1);
  }

  .dark .badge-high {
    background-color: hsl(25 95% 15% / 1);
    color: hsl(25 95% 85% / 1);
    border: 1px solid hsl(25 95% 25% / 1);
  }

  .badge-medium {
    background-color: hsl(45 93% 95% / 1);
    color: hsl(45 93% 25% / 1);
    border: 1px solid hsl(45 93% 85% / 1);
  }

  .dark .badge-medium {
    background-color: hsl(45 93% 15% / 1);
    color: hsl(45 93% 85% / 1);
    border: 1px solid hsl(45 93% 25% / 1);
  }

  .badge-low {
    background-color: hsl(60 90% 95% / 1);
    color: hsl(60 90% 25% / 1);
    border: 1px solid hsl(60 90% 85% / 1);
  }

  .dark .badge-low {
    background-color: hsl(60 90% 15% / 1);
    color: hsl(60 90% 85% / 1);
    border: 1px solid hsl(60 90% 25% / 1);
  }

  /* Maintenance Status Colors */
  .status-new {
    background-color: hsl(210 100% 95% / 1);
    color: hsl(210 100% 25% / 1);
    border-color: hsl(210 100% 85% / 1);
  }

  .dark .status-new {
    background-color: hsl(210 100% 15% / 1);
    color: hsl(210 100% 85% / 1);
    border-color: hsl(210 100% 25% / 1);
  }

  .status-assigned {
    background-color: hsl(270 95% 95% / 1);
    color: hsl(270 95% 25% / 1);
    border-color: hsl(270 95% 85% / 1);
  }

  .dark .status-assigned {
    background-color: hsl(270 95% 15% / 1);
    color: hsl(270 95% 85% / 1);
    border-color: hsl(270 95% 25% / 1);
  }

  .status-in-progress {
    background-color: hsl(45 93% 95% / 1);
    color: hsl(45 93% 25% / 1);
    border-color: hsl(45 93% 85% / 1);
  }

  .dark .status-in-progress {
    background-color: hsl(45 93% 15% / 1);
    color: hsl(45 93% 85% / 1);
    border-color: hsl(45 93% 25% / 1);
  }

  .status-completed {
    background-color: hsl(142 76% 95% / 1);
    color: hsl(142 76% 25% / 1);
    border-color: hsl(142 76% 85% / 1);
  }

  .dark .status-completed {
    background-color: hsl(142 76% 15% / 1);
    color: hsl(142 76% 85% / 1);
    border-color: hsl(142 76% 25% / 1);
  }

  /* Enhanced Glass System */
  .glass-surface {
    background: rgba(var(--glass-bg));
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(var(--glass-border));
    box-shadow: var(--glass-shadow);
  }

  .glass-card {
    background: rgba(var(--glass-bg));
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(var(--glass-border));
    box-shadow: var(--glass-shadow);
    border-radius: 12px;
  }

  .glass-card-hover {
    background: rgba(var(--glass-bg));
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(var(--glass-border));
    box-shadow: var(--glass-shadow);
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .glass-card-hover:hover {
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
  }

  /* Dashboard Background */
  .dashboard-background {
    background: linear-gradient(135deg,
      hsl(var(--bg-primary)) 0%,
      hsl(var(--bg-secondary)) 25%,
      hsl(var(--bg-primary)) 50%,
      hsl(var(--bg-tertiary)) 75%,
      hsl(var(--bg-primary)) 100%);
    min-height: 100vh;
  }

  /* Utility Classes */
  .chart-container {
    background-color: hsl(var(--bg-secondary));
    border: 1px solid hsl(var(--border));
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .filter-dropdown {
    background-color: hsl(var(--bg-secondary));
    border: 1px solid hsl(var(--border));
    border-radius: 6px;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    color: hsl(var(--text-primary));
  }

  /* Mobile-First Enhancements */
  .mobile-optimized {
    min-height: 44px;
    touch-action: manipulation;
  }

  .mobile-touch-friendly {
    min-height: 48px;
    min-width: 48px;
    touch-action: manipulation;
  }

  .mobile-text-readable {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
  }

  .mobile-padding {
    padding: 1rem;
  }

  .mobile-gap {
    gap: 0.75rem;
  }

  @media (max-width: 640px) {
    /* Improve touch targets on mobile */
    .btn, button, [role="button"] {
      min-height: 44px;
      touch-action: manipulation;
    }

    /* Better spacing on mobile */
    .card-content {
      padding: 1rem;
    }

    /* Larger text for readability */
    .mobile-text {
      font-size: 16px;
      line-height: 1.5;
    }

    /* Better button spacing */
    .button-group {
      gap: 0.75rem;
    }

    /* Improve form inputs */
    input, textarea, select {
      min-height: 44px;
      font-size: 16px;
    }
  }

  /* Compact Mode */
  .compact-mode .card {
    padding: 0.75rem;
  }

  .compact-mode .p-4 {
    padding: 0.5rem;
  }

  .compact-mode .p-6 {
    padding: 0.75rem;
  }

  /* Glass Badge Variants */
  .glass-badge-success {
    border: 1px solid hsl(var(--status-success-border));
    background: hsl(var(--status-success-bg));
    color: hsl(var(--status-success-text));
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }

  .glass-badge-warning {
    border: 1px solid hsl(var(--status-warning-border));
    background: hsl(var(--status-warning-bg));
    color: hsl(var(--status-warning-text));
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }

  .glass-badge-error {
    border: 1px solid hsl(var(--status-error-border));
    background: hsl(var(--status-error-bg));
    color: hsl(var(--status-error-text));
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }

  .glass-badge-info {
    border: 1px solid hsl(var(--status-info-border));
    background: hsl(var(--status-info-bg));
    color: hsl(var(--status-info-text));
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }
}