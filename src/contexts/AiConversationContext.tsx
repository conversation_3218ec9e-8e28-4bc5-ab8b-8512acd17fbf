import React, { createContext, useContext, useReducer, ReactNode } from 'react';

export interface ConversationMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  result?: any;
}

interface ConversationState {
  messages: ConversationMessage[];
  sessionId: string;
}

type ConversationAction = 
  | { type: 'ADD_MESSAGE'; payload: ConversationMessage }
  | { type: 'CLEAR_CONVERSATION' }
  | { type: 'SET_SESSION_ID'; payload: string };

const conversationReducer = (state: ConversationState, action: ConversationAction): ConversationState => {
  switch (action.type) {
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload]
      };
    case 'CLEAR_CONVERSATION':
      return {
        ...state,
        messages: [],
        sessionId: generateSessionId()
      };
    case 'SET_SESSION_ID':
      return {
        ...state,
        sessionId: action.payload
      };
    default:
      return state;
  }
};

const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

const initialState: ConversationState = {
  messages: [],
  sessionId: generateSessionId()
};

interface ConversationContextType {
  state: ConversationState;
  addMessage: (message: Omit<ConversationMessage, 'id'>) => void;
  clearConversation: () => void;
  getConversationContext: () => string;
}

const ConversationContext = createContext<ConversationContextType | undefined>(undefined);

export const AiConversationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(conversationReducer, initialState);

  const addMessage = (message: Omit<ConversationMessage, 'id'>) => {
    const messageWithId: ConversationMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    dispatch({ type: 'ADD_MESSAGE', payload: messageWithId });
  };

  const clearConversation = () => {
    dispatch({ type: 'CLEAR_CONVERSATION' });
  };

  const getConversationContext = (): string => {
    // Get last 5 messages for context, excluding system messages
    const recentMessages = state.messages.slice(-5);
    
    if (recentMessages.length === 0) return '';
    
    const contextMessages = recentMessages.map(msg => 
      `${msg.type === 'user' ? 'User' : 'AI'}: ${msg.content}`
    ).join('\n');
    
    return `Previous conversation context:\n${contextMessages}\n\nCurrent request:`;
  };

  return (
    <ConversationContext.Provider value={{
      state,
      addMessage,
      clearConversation,
      getConversationContext
    }}>
      {children}
    </ConversationContext.Provider>
  );
};

export const useAiConversation = (): ConversationContextType => {
  const context = useContext(ConversationContext);
  if (!context) {
    throw new Error('useAiConversation must be used within an AiConversationProvider');
  }
  return context;
};