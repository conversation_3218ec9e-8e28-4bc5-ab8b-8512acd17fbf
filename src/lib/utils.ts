import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { toast } from "sonner"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Enhanced toast utility for AI command confirmations with navigation
export interface AICommandResult {
  success: boolean;
  message: string;
  action?: string;
  entityType?: string;
  entityId?: string;
}

export interface NavigationConfig {
  entityType: string;
  entityId?: string;
  navigate: (path: string) => void;
}

export function showAIConfirmationToast(result: AICommandResult, navigationConfig?: NavigationConfig) {
  if (!result.success) {
    toast.error(result.message);
    return;
  }

  // If we have navigation config and entity info, show clickable toast
  if (navigationConfig && result.entityType && result.entityId) {
    const { entityType, entityId } = result;
    const { navigate } = navigationConfig;

    // Determine the navigation path based on entity type
    const getNavigationPath = (type: string, id: string): string => {
      switch (type) {
        case 'maintenance_task':
          // Use 'id' parameter as that's what the maintenance page expects
          return `/maintenance?id=${id}`;
        case 'purchase_order':
          return `/purchase-orders?id=${id}`;
        case 'property':
          // Navigate to specific property detail page
          return `/properties/${id}`;
        case 'inventory_item':
          return `/inventory?item=${id}`;
        case 'damage_report':
          // Navigate to specific damage detail page
          return `/damages/${id}`;
        case 'team':
          return `/teams?team=${id}`;
        default:
          return '/dashboard';
      }
    };

    const navigationPath = getNavigationPath(entityType, entityId);

    // Get user-friendly entity name
    const getEntityDisplayName = (type: string): string => {
      switch (type) {
        case 'maintenance_task':
          return 'maintenance task';
        case 'purchase_order':
          return 'purchase order';
        case 'property':
          return 'property';
        case 'inventory_item':
          return 'inventory item';
        case 'damage_report':
          return 'damage report';
        case 'team':
          return 'team';
        default:
          return 'item';
      }
    };

    const entityDisplayName = getEntityDisplayName(entityType);

    toast.success(result.message, {
      action: {
        label: `View ${entityDisplayName}`,
        onClick: () => {
          navigate(navigationPath);
        },
      },
      duration: 8000, // Longer duration for actionable toasts
    });
  } else {
    // Fallback to regular success toast
    toast.success(result.message);
  }
}
