name: Supabase DB Backup
'on':
  workflow_dispatch: null
  schedule:
  - cron: 0 4 * * *   # 4:00 AM UTC daily
  - cron: 0 17 * * *  # 5:00 PM UTC daily  
  - cron: 0 0 * * *   # Midnight UTC daily
jobs:
  backup_database:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4
    - name: Install PostgreSQL Client (for pg_dump)
      run: sudo apt-get update && sudo apt-get install -y postgresql-client
    - name: Perform Database Dump (using pg_dump)
      run: "#!/bin/bash\nset -e # Exit immediately if a command exits with a non-zero status.\n\n# Mask password for logging\n# Use standard sed delimiter '#'\nMASKED_DB_URL=$(echo \"$SUPABASE_DB_URL\" | sed 's#:[^@]*@#:********@#')\necho \"Using DB URL (Pooler): $MASKED_DB_URL\"\n\n# Create the backup directory if it doesn't exist\nmkdir -p \"supabase/backups\"\n\n# Create timestamp for unique backup files\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\necho \"Creating backup with timestamp: $TIMESTAMP\"\n\n# Define schemas to exclude (Supabase internal + standard PG internal)\n# Use multiple --exclude-schema flags as pg_dump doesn't always handle comma-separated lists well\n# Ensure correct bash array expansion syntax: \"${EXCLUDE_SCHEMAS[@]}\"\nEXCLUDE_SCHEMAS=(\n  --exclude-schema=auth\n  --exclude-schema=extensions\n  --exclude-schema=pg* # Exclude pg_catalog, pg_toast, etc.\n  --exclude-schema=storage\n  --exclude-schema=supabase_functions\n  --exclude-schema=_realtime\n  --exclude-schema=graphql # Often present\n  --exclude-schema=graphql_public # Often present\n  --exclude-schema=information_schema # Standard PG internal\n\
        )\n\n# Dump global objects (roles) using pg_dumpall (Uses --database flag correctly)\necho \"Dumping roles...\"\npg_dumpall --roles-only --no-role-passwords --database=\"$SUPABASE_DB_URL\" > \"supabase/backups/roles_${TIMESTAMP}.sql\"\n\n# Dump schema (excluding internal ones) using pg_dump (DB URL is last argument)\necho \"Dumping schema...\"\npg_dump --schema-only --no-owner --no-privileges \"${EXCLUDE_SCHEMAS[@]}\" \"$SUPABASE_DB_URL\" > \"supabase/backups/schema_${TIMESTAMP}.sql\"\n\n# Dump data (excluding internal ones) using pg_dump (DB URL is last argument)\necho \"Dumping data...\"\npg_dump --data-only --no-owner --no-privileges \"${EXCLUDE_SCHEMAS[@]}\" \"$SUPABASE_DB_URL\" > \"supabase/backups/data_${TIMESTAMP}.sql\"\n\necho \"Backup complete with timestamp: $TIMESTAMP\"\n"
      env:
        SUPABASE_DB_URL: ${{ secrets.SUPABASE_DB_URL }}
        PGPASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}
    - name: Cleanup Old Backups (Keep only 10 most recent)
      run: |
        cd supabase/backups
        # Keep only the 10 most recent backup sets (30 files total)
        if [ "$(ls -1 *_*.sql 2>/dev/null | wc -l)" -gt 30 ]; then
          ls -t *_*.sql | tail -n +31 | xargs -r rm -f
        fi
        echo "Cleanup completed. Remaining backup files:"
        ls -la *.sql 2>/dev/null | wc -l || echo "0"
    - name: Commit Backup Files to supaback branch
      uses: stefanzweifel/git-auto-commit-action@v5
      with:
        commit_message: 'chore: Automated Supabase DB backup - $(date +"%Y-%m-%d %H:%M UTC") [skip ci]'
        file_pattern: supabase/backups/*.sql
        repository: .
        branch: supaback
        commit_options: --no-verify
        add_options: --force
        push_options: --force
        skip_dirty_check: 'true'
        skip_fetch: 'true'
        create_branch: true
    - name: Commit Backup Files to main branch
      uses: stefanzweifel/git-auto-commit-action@v5
      with:
        commit_message: 'chore: Automated Supabase DB backup - $(date +"%Y-%m-%d %H:%M UTC") [skip ci]'
        file_pattern: supabase/backups/*.sql
        repository: .
        branch: main
        commit_options: --no-verify
        add_options: --force
        push_options: --force
        skip_dirty_check: 'true'
        skip_fetch: 'true'
        create_branch: true
    - name: Commit Backup Files to preview branch
      uses: stefanzweifel/git-auto-commit-action@v5
      with:
        commit_message: 'chore: Automated Supabase DB backup - $(date +"%Y-%m-%d %H:%M UTC") [skip ci]'
        file_pattern: supabase/backups/*.sql
        repository: .
        branch: preview
        commit_options: --no-verify
        add_options: --force
        push_options: --force
        skip_dirty_check: 'true'
        skip_fetch: 'true'
        create_branch: true
