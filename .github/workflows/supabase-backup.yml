name: Supabase Backup

on:
  schedule:
    # Run 4 times daily: 2 AM, 8 AM, 2 PM, 8 PM UTC
    - cron: '0 2,8,14,20 * * *'
  workflow_dispatch: # Allow manual trigger

env:
  SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
  SUPABASE_PROJECT_REF: ${{ secrets.SUPABASE_PROJECT_REF }}
  SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}

jobs:
  backup:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install Supabase CLI
        run: |
          wget -O supabase_linux_amd64.tar.gz https://github.com/supabase/cli/releases/latest/download/supabase_linux_amd64.tar.gz
          tar -xzf supabase_linux_amd64.tar.gz
          sudo cp supabase /usr/local/bin/supabase
          sudo chmod +x /usr/local/bin/supabase
          rm supabase_linux_amd64.tar.gz supabase

      - name: Verify Supabase CLI installation
        run: supabase --version

      - name: Create backup directory structure
        run: |
          BACKUP_DATE=$(date +%Y-%m-%d_%H-%M-%S)
          BACKUP_DIR="backups/${BACKUP_DATE}"
          mkdir -p "${BACKUP_DIR}"/{schema,data,functions,config}
          echo "BACKUP_DATE=${BACKUP_DATE}" >> $GITHUB_ENV
          echo "BACKUP_DIR=${BACKUP_DIR}" >> $GITHUB_ENV

      - name: Authenticate with Supabase
        run: |
          echo "$SUPABASE_ACCESS_TOKEN" | supabase login --token

      - name: Link to Supabase project
        run: |
          supabase link --project-ref $SUPABASE_PROJECT_REF --password $SUPABASE_DB_PASSWORD

      - name: Backup database schema
        run: |
          echo "🗄️ Backing up database schema..."
          
          # Backup public schema
          supabase db dump --schema=public --linked \
            -f "${BACKUP_DIR}/schema/public_schema.sql"
          
          # Backup auth schema customizations
          supabase db dump --schema=auth --linked \
            -f "${BACKUP_DIR}/schema/auth_schema.sql" || echo "No custom auth schema"
          
          # Backup storage schema
          supabase db dump --schema=storage --linked \
            -f "${BACKUP_DIR}/schema/storage_schema.sql" || echo "No custom storage schema"
          
          # Backup extensions
          supabase db dump --schema=extensions --linked \
            -f "${BACKUP_DIR}/schema/extensions_schema.sql" || echo "No custom extensions"

      - name: Backup database data
        run: |
          echo "📊 Backing up database data..."
          
          # Backup public schema data
          supabase db dump --schema=public --data-only --linked \
            -f "${BACKUP_DIR}/data/public_data.sql"
          
          # Backup auth users (if accessible)
          supabase db dump --schema=auth --data-only --linked \
            -f "${BACKUP_DIR}/data/auth_data.sql" || echo "Auth data not accessible"
          
          # Backup storage data
          supabase db dump --schema=storage --data-only --linked \
            -f "${BACKUP_DIR}/data/storage_data.sql" || echo "Storage data not accessible"

      - name: Backup Edge Functions
        run: |
          echo "⚡ Backing up Edge Functions..."
          
          # List all functions and save to file
          supabase functions list --project-ref $SUPABASE_PROJECT_REF --output json > "${BACKUP_DIR}/functions/functions_list.json"
          
          # Get function names from the list
          FUNCTION_NAMES=$(supabase functions list --project-ref $SUPABASE_PROJECT_REF | tail -n +2 | awk '{print $4}' | grep -v "NAME" | head -n -1)
          
          echo "Functions to backup: $FUNCTION_NAMES"
          
          # Create functions backup directory
          mkdir -p "${BACKUP_DIR}/functions/code"
          
          # Download each function
          for func in $FUNCTION_NAMES; do
            if [ ! -z "$func" ] && [ "$func" != "NAME" ]; then
              echo "Downloading function: $func"
              supabase functions download "$func" --project-ref $SUPABASE_PROJECT_REF || echo "Failed to download $func"
              
              # Copy the downloaded function to backup directory
              if [ -d "supabase/functions/$func" ]; then
                cp -r "supabase/functions/$func" "${BACKUP_DIR}/functions/code/"
                echo "✅ Backed up function: $func"
              fi
            fi
          done

      - name: Backup project configuration
        run: |
          echo "⚙️ Backing up project configuration..."
          
          # Copy local config (if it exists and is up to date)
          if [ -f "supabase/config.toml" ]; then
            cp supabase/config.toml "${BACKUP_DIR}/config/"
          fi
          
          # Create a comprehensive project info file
          cat > "${BACKUP_DIR}/config/project_info.json" << EOF
          {
            "project_ref": "$SUPABASE_PROJECT_REF",
            "backup_date": "$BACKUP_DATE",
            "backup_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "cli_version": "$(supabase --version)",
            "backup_type": "full",
            "retention_days": 30
          }
          EOF

      - name: Create backup manifest
        run: |
          echo "📋 Creating backup manifest..."
          
          # Create detailed manifest
          cat > "${BACKUP_DIR}/MANIFEST.md" << EOF
          # Supabase Backup Manifest
          
          **Backup Date:** $BACKUP_DATE  
          **Project Reference:** $SUPABASE_PROJECT_REF  
          **Backup Type:** Full Backup  
          **Created:** $(date -u +%Y-%m-%dT%H:%M:%SZ)  
          
          ## Contents
          
          ### Schema Backups
          - \`schema/public_schema.sql\` - Public schema structure
          - \`schema/auth_schema.sql\` - Auth schema customizations
          - \`schema/storage_schema.sql\` - Storage schema
          - \`schema/extensions_schema.sql\` - Extensions
          
          ### Data Backups
          - \`data/public_data.sql\` - Application data
          - \`data/auth_data.sql\` - Authentication data
          - \`data/storage_data.sql\` - Storage metadata
          
          ### Functions
          - \`functions/functions_list.json\` - Function inventory
          - \`functions/code/\` - Edge Function source code
          
          ### Configuration
          - \`config/config.toml\` - Supabase configuration
          - \`config/project_info.json\` - Project metadata
          
          ## Restoration
          
          To restore from this backup:
          
          1. **Schema:** \`supabase db reset\` then apply schema files
          2. **Data:** Use \`psql\` to restore data files
          3. **Functions:** Use \`supabase functions deploy\` for each function
          4. **Config:** Update \`supabase/config.toml\`
          
          ## File Sizes
          \`\`\`
          $(find ${BACKUP_DIR} -type f -exec ls -lh {} \; | awk '{print $5 " " $9}')
          \`\`\`
          EOF

      - name: Compress backup
        run: |
          echo "🗜️ Compressing backup..."
          cd backups
          tar -czf "${BACKUP_DATE}.tar.gz" "${BACKUP_DATE}/"
          rm -rf "${BACKUP_DATE}/"
          
          # Show backup size
          echo "📦 Backup size: $(du -h ${BACKUP_DATE}.tar.gz | cut -f1)"

      - name: Upload backup artifact
        uses: actions/upload-artifact@v4
        with:
          name: supabase-backup-${{ env.BACKUP_DATE }}
          path: backups/${{ env.BACKUP_DATE }}.tar.gz
          retention-days: 30
          compression-level: 9

      - name: Create backup summary
        run: |
          echo "## 📊 Supabase Backup Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Date:** $BACKUP_DATE" >> $GITHUB_STEP_SUMMARY
          echo "**Project:** $SUPABASE_PROJECT_REF" >> $GITHUB_STEP_SUMMARY
          echo "**Size:** $(du -h backups/${BACKUP_DATE}.tar.gz | cut -f1)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📁 Backup Contents" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Database Schema" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Application Data" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Edge Functions" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Project Configuration" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔄 Retention Policy" >> $GITHUB_STEP_SUMMARY
          echo "Backups are kept for **30 days** and automatically cleaned up." >> $GITHUB_STEP_SUMMARY

      - name: Cleanup old local files
        run: |
          echo "🧹 Cleaning up local files..."
          rm -rf supabase/functions/*
          rm -f backups/${BACKUP_DATE}.tar.gz

      - name: Backup completion notification
        run: |
          echo "✅ Backup completed successfully!"
          echo "📦 Backup artifact: supabase-backup-$BACKUP_DATE"
          echo "🕒 Next backup scheduled in ~6 hours"
