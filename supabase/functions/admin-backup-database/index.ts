import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type"
};
serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: corsHeaders
    });
  }
  try {
    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY is not set");
    }
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);
    // Verify that the user is a super admin
    const authHeader = req.headers.get("Authorization") || "";
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      throw new Error("Missing or invalid authorization header");
    }
    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      throw new Error("Authentication failed");
    }
    // Check if user is a super admin
    const { data: profile, error: profileError } = await supabaseAdmin.from("profiles").select("is_super_admin").eq("id", user.id).single();
    if (profileError || !profile || !profile.is_super_admin) {
      throw new Error("Only super admins can perform database backups");
    }
    // Parse request body
    const requestData = await req.json();
    const backupName = requestData.backupName || `backup-${new Date().toISOString().split('T')[0]}`;
    const format = requestData.format || 'json';
    // Get list of all tables using RPC function
    const { data: tableList, error: tableError } = await supabaseAdmin.rpc('get_all_tables');
    if (tableError || !tableList || tableList.length === 0) {
      throw new Error(`Failed to retrieve table list: ${tableError?.message || "No tables found"}`);
    }
    console.log(`Found ${tableList.length} tables`);
    if (format === 'json') {
      // Create a backup object (JSON format)
      const backup = {
        metadata: {
          created_at: new Date().toISOString(),
          version: "1.0",
          name: backupName
        },
        tables: {}
      };
      for (const tableInfo of tableList){
        const tableName = tableInfo.tablename;
        // Skip schema migrations
        if (tableName === 'schema_migrations') {
          continue;
        }
        try {
          console.log(`Processing table: ${tableName}`);
          // Get table data
          const { data: tableData, error: dataError } = await supabaseAdmin.from(tableName).select('*');
          if (dataError) {
            console.error(`Error fetching data from ${tableName}: ${dataError.message}`);
            continue;
          }
          console.log(`Retrieved ${tableData?.length || 0} rows from ${tableName}`);
          // Get table structure - using our SQL function
          const { data: tableColumns, error: columnError } = await supabaseAdmin.rpc('get_table_columns', {
            table_name: tableName
          });
          if (columnError) {
            console.error(`Error fetching columns for ${tableName}: ${columnError.message}`);
            // Fallback to just storing the data without structure
            backup.tables[tableName] = {
              data: tableData || []
            };
          } else {
            // Store both structure and data
            backup.tables[tableName] = {
              columns: tableColumns,
              data: tableData || []
            };
          }
        } catch (tableError) {
          console.error(`Error processing table ${tableName}: ${tableError.message}`);
          continue;
        }
      }
      return new Response(JSON.stringify(backup), {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        },
        status: 200
      });
    } else {
      // SQL Format
      let sqlContent = `-- Database backup generated on ${new Date().toISOString()}\n`;
      sqlContent += `-- Name: ${backupName}\n\n`;
      // Add SQL commands to create tables and import data
      for (const tableInfo of tableList){
        const tableName = tableInfo.tablename;
        // Skip schema migrations
        if (tableName === 'schema_migrations') {
          continue;
        }
        try {
          console.log(`Processing table for SQL: ${tableName}`);
          // Get table structure
          const { data: tableStructure, error: structureError } = await supabaseAdmin.rpc('get_table_structure', {
            table_name: tableName
          });
          if (structureError) {
            console.error(`Error fetching structure for ${tableName}: ${structureError.message}`);
            continue;
          }
          // Get table data
          const { data: tableData, error: dataError } = await supabaseAdmin.from(tableName).select('*');
          if (dataError) {
            console.error(`Error fetching data from ${tableName}: ${dataError.message}`);
            continue;
          }
          console.log(`Retrieved ${tableData?.length || 0} rows from ${tableName} for SQL export`);
          // Add table structure
          sqlContent += `-- Table: public.${tableName}\n\n`;
          // Create DROP statement with IF EXISTS
          sqlContent += `DROP TABLE IF EXISTS public.${tableName};\n\n`;
          // Create CREATE TABLE statement
          sqlContent += `CREATE TABLE public.${tableName} (\n`;
          if (tableStructure && tableStructure.length > 0) {
            for(let i = 0; i < tableStructure.length; i++){
              const col = tableStructure[i];
              sqlContent += `    ${col.column_name} ${col.data_type}`;
              if (!col.is_nullable) {
                sqlContent += ` NOT NULL`;
              }
              if (col.column_default) {
                sqlContent += ` DEFAULT ${col.column_default}`;
              }
              if (i < tableStructure.length - 1) {
                sqlContent += ',';
              }
              sqlContent += '\n';
            }
          }
          sqlContent += `);\n\n`;
          // Add ALTER TABLE to enable RLS
          sqlContent += `-- Enable row level security\n`;
          sqlContent += `ALTER TABLE public.${tableName} ENABLE ROW LEVEL SECURITY;\n\n`;
          // Add table data
          if (tableData && tableData.length > 0) {
            sqlContent += `-- Data for table public.${tableName}\n`;
            for (const row of tableData){
              const columns = Object.keys(row).filter((key)=>key !== 'password'); // Skip password columns
              const columnList = columns.join(', ');
              const values = columns.map((col)=>{
                const val = row[col];
                if (val === null) return 'NULL';
                if (typeof val === 'object') return `'${JSON.stringify(val).replace(/'/g, "''")}'`;
                if (typeof val === 'string') return `'${val.replace(/'/g, "''")}'`;
                return val;
              });
              sqlContent += `INSERT INTO public.${tableName} (${columnList}) VALUES (${values.join(', ')});\n`;
            }
            sqlContent += '\n';
          }
          sqlContent += '\n';
        } catch (tableError) {
          console.error(`Error processing SQL for table ${tableName}: ${tableError.message}`);
          continue;
        }
      }
      // For SQL format, return the SQL content directly
      return new Response(JSON.stringify({
        sql: sqlContent
      }), {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        },
        status: 200
      });
    }
  } catch (error) {
    console.error("Error in admin-backup-database function:", error);
    return new Response(JSON.stringify({
      error: error instanceof Error ? error.message : 'Unknown error',
      success: false
    }), {
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      },
      status: 500
    });
  }
});
