import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';
Deno.serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    // Create a Supabase client with the service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    // Verify that the request is authorized
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({
        error: 'Missing authorization header'
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Verify the user is authenticated and is a super admin
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    if (userError || !user) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        details: userError?.message
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Check if the user is a super admin
    const { data: isAdmin, error: isAdminError } = await supabase.rpc('is_super_admin', {
      uid: user.id
    });
    if (isAdminError) {
      return new Response(JSON.stringify({
        error: `Error checking admin status: ${isAdminError.message}`
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    if (!isAdmin) {
      return new Response(JSON.stringify({
        error: 'Admin access required'
      }), {
        status: 403,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Get the request body
    const { userId, updates, password } = await req.json();
    if (!userId) {
      return new Response(JSON.stringify({
        error: 'User ID is required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Update user metadata if provided
    if (updates) {
      const userMetadata = {};
      if (updates.first_name) userMetadata.first_name = updates.first_name;
      if (updates.last_name) userMetadata.last_name = updates.last_name;
      if (updates.role) userMetadata.role = updates.role;
      const userUpdateParams = {
        user_metadata: userMetadata
      };
      // Add password update if provided
      if (password) {
        userUpdateParams.password = password;
      }
      // Update user in Auth
      if (Object.keys(userMetadata).length > 0 || password) {
        const { error: updateAuthError } = await supabase.auth.admin.updateUserById(userId, userUpdateParams);
        if (updateAuthError) {
          return new Response(JSON.stringify({
            error: updateAuthError.message
          }), {
            status: 500,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          });
        }
      }
      // Update profile in database
      const profileUpdates = {};
      if (updates.first_name !== undefined) profileUpdates.first_name = updates.first_name;
      if (updates.last_name !== undefined) profileUpdates.last_name = updates.last_name;
      if (updates.role !== undefined) profileUpdates.role = updates.role;
      if (updates.is_super_admin !== undefined) profileUpdates.is_super_admin = updates.is_super_admin;
      if (Object.keys(profileUpdates).length > 0) {
        const { error: updateProfileError } = await supabase.from('profiles').update(profileUpdates).eq('id', userId);
        if (updateProfileError) {
          return new Response(JSON.stringify({
            error: updateProfileError.message
          }), {
            status: 500,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          });
        }
      }
    }
    // Fetch the updated profile
    const { data: profile, error: profileError } = await supabase.from('profiles').select('*').eq('id', userId).single();
    if (profileError) {
      return new Response(JSON.stringify({
        error: profileError.message
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    return new Response(JSON.stringify({
      success: true,
      user: profile
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Unexpected error:', error.message, error.stack);
    return new Response(JSON.stringify({
      error: 'Internal Server Error',
      details: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
