import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';
Deno.serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    // Create a Supabase client with the service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    // Verify that the request is authorized
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({
        error: 'Missing authorization header'
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Verify the user is authenticated and is a super admin
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    if (userError || !user) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        details: userError?.message
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Check if the user is a super admin
    const { data: isAdmin, error: isAdminError } = await supabase.rpc('is_super_admin', {
      uid: user.id
    });
    if (isAdminError) {
      return new Response(JSON.stringify({
        error: `Error checking admin status: ${isAdminError.message}`
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    if (!isAdmin) {
      return new Response(JSON.stringify({
        error: 'Admin access required'
      }), {
        status: 403,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Get the request body
    const { email, password, firstName, lastName, role, isSuperAdmin } = await req.json();
    // Validate required fields
    if (!email || !password) {
      return new Response(JSON.stringify({
        error: 'Email and password are required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Create the user with Supabase Auth
    const { data: userData, error: createError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
        role
      }
    });
    if (createError) {
      return new Response(JSON.stringify({
        error: createError.message
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    if (!userData.user) {
      return new Response(JSON.stringify({
        error: 'Failed to create user'
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Update the profile with the super admin status if needed
    if (isSuperAdmin) {
      const { error: updateError } = await supabase.from('profiles').update({
        is_super_admin: true
      }).eq('id', userData.user.id);
      if (updateError) {
        console.error('Error updating super admin status:', updateError);
      // We don't return an error here as the user was already created
      }
    }
    // Fetch the updated profile
    const { data: profile, error: profileError } = await supabase.from('profiles').select('*').eq('id', userData.user.id).single();
    if (profileError) {
      console.error('Error fetching profile:', profileError);
    }
    return new Response(JSON.stringify({
      success: true,
      user: profile || {
        id: userData.user.id,
        email: userData.user.email,
        first_name: firstName,
        last_name: lastName,
        role,
        is_super_admin: isSuperAdmin
      }
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Unexpected error:', error.message, error.stack);
    return new Response(JSON.stringify({
      error: 'Internal Server Error',
      details: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
