import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('=== Accept Invitation Direct Function Started ===');
    console.log('Request method:', req.method);
    console.log('Request headers:', Object.fromEntries(req.headers.entries()));

    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

    console.log('Supabase URL:', supabaseUrl ? 'Set' : 'Missing');
    console.log('Service Role Key:', supabaseKey ? 'Set' : 'Missing');

    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing Supabase URL or key');
      return new Response(JSON.stringify({
        success: false,
        error: 'Server configuration error'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      });
    }

    const supabaseAdmin = createClient(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('Supabase admin client created successfully');

    // Get the request body
    let requestBody;
    try {
      requestBody = await req.json();
      console.log('Request body:', JSON.stringify(requestBody));
    } catch (error) {
      console.error('Error parsing request body:', error);
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid JSON in request body'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400
      });
    }

    // Extract and validate required fields
    const { token, email, password, first_name, last_name, role = 'service_provider' } = requestBody;
    
    if (!token || !email || !password || !first_name || !last_name) {
      console.error('Missing required fields');
      return new Response(JSON.stringify({
        success: false,
        error: 'Missing required fields'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400
      });
    }

    // First, get the invitation details
    console.log(`Looking up invitation with token: ${token}`);
    const { data: invitationData, error: invitationError } = await supabaseAdmin
      .from('team_invitations')
      .select('id, team_id, email, role, status')
      .eq('token', token)
      .maybeSingle();

    if (invitationError) {
      console.error('Error fetching invitation:', invitationError);
      return new Response(JSON.stringify({
        success: false,
        error: 'Error fetching invitation: ' + invitationError.message
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400
      });
    }

    if (!invitationData) {
      console.error('Invitation not found');
      return new Response(JSON.stringify({
        success: false,
        error: 'Invitation not found'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404
      });
    }

    console.log('Found invitation:', invitationData);

    // Create the user
    console.log('Creating new user');
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        first_name,
        last_name,
        role: role
      }
    });

    if (userError) {
      console.error('Error creating user:', userError);
      return new Response(JSON.stringify({
        success: false,
        error: 'Could not create user: ' + userError.message
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400
      });
    }

    if (!userData?.user) {
      console.error('User creation response did not include user data');
      return new Response(JSON.stringify({
        success: false,
        error: 'User creation failed'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      });
    }

    const userId = userData.user.id;
    console.log(`Created user with ID: ${userId}`);

    // Use the create_profile_safely RPC function
    console.log(`Creating user profile safely for user ID: ${userId} with role: ${role}`);
    const { data: profileResult, error: profileError } = await supabaseAdmin.rpc('create_profile_safely', {
      p_user_id: userId,
      p_first_name: first_name,
      p_last_name: last_name,
      p_role: role,
      p_email: email
    });

    if (profileError) {
      console.error('Error creating user profile with RPC:', profileError);
      // Fallback to direct profile creation
      console.log('Attempting fallback profile creation');
      const { error: directProfileError } = await supabaseAdmin.from('profiles').upsert({
        id: userId,
        email,
        first_name,
        last_name,
        role: role,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
      
      if (directProfileError) {
        console.error('Error with fallback profile creation:', directProfileError);
      }
    } else {
      console.log('Profile created successfully with RPC:', profileResult);
    }

    // Create the service provider profile if needed
    if (role === 'service_provider') {
      console.log('Creating service provider profile for user:', userId);
      const { error: spProfileError } = await supabaseAdmin.from('service_providers').upsert({
        id: userId,
        email,
        first_name,
        last_name,
        status: 'active'
      });
      
      if (spProfileError) {
        console.error('Error creating service provider profile:', spProfileError);
        // Continue anyway - the profile might already exist
      }
    }

    // Add the user to the team
    console.log(`Adding user ${userId} to team ${invitationData.team_id}`);
    const { error: teamMemberError } = await supabaseAdmin.from('team_members').upsert({
      team_id: invitationData.team_id,
      user_id: userId,
      role: invitationData.role || role,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    if (teamMemberError) {
      console.error('Error adding user to team:', teamMemberError);
      // Continue anyway - the user might already be in the team
    }

    // Update the invitation status
    console.log(`Updating invitation status to accepted for invitation ID: ${invitationData.id}`);
    const { error: updateError } = await supabaseAdmin.from('team_invitations').update({
      status: 'accepted',
      accepted_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }).eq('id', invitationData.id);

    if (updateError) {
      console.error('Error updating invitation status:', updateError);
      // Continue anyway - the user is already added to the team
    }

    return new Response(JSON.stringify({
      success: true,
      user: userData.user,
      isNewUser: true,
      team_id: invitationData.team_id,
      message: 'Invitation accepted successfully'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (err) {
    console.error('Unexpected error:', err);
    return new Response(JSON.stringify({
      success: false,
      error: 'An unexpected error occurred: ' + (err.message || 'Unknown error')
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
