import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};
serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  try {
    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({
        error: 'Missing authorization header'
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    if (!supabaseUrl || !supabaseServiceKey) {
      return new Response(JSON.stringify({
        error: 'Server configuration error'
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    // Verify the user's token
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        details: authError?.message
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Check if the user is a super admin
    const { data: profile, error: profileError } = await supabase.from('profiles').select('is_super_admin, role').eq('id', user.id).single();
    if (profileError) {
      return new Response(JSON.stringify({
        error: 'Error fetching user profile',
        details: profileError.message
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    const isSuperAdmin = profile?.is_super_admin || profile?.role === 'admin';
    if (!isSuperAdmin) {
      return new Response(JSON.stringify({
        error: 'Forbidden: Only super admins can delete users'
      }), {
        status: 403,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Parse the request body
    const requestData = await req.json();
    const { userId } = requestData;
    if (!userId) {
      return new Response(JSON.stringify({
        error: 'Missing required parameter: userId'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    console.log(`Deleting user with ID: ${userId}`);
    try {
      // Step 1: Delete user_preferences
      console.log('Deleting user_preferences...');
      await supabase.from('user_preferences').delete().eq('user_id', userId);
      // Step 2: Delete user_permissions
      console.log('Deleting user_permissions...');
      await supabase.from('user_permissions').delete().eq('user_id', userId);
      // Step 3: Delete team_members
      console.log('Deleting team_members...');
      await supabase.from('team_members').delete().eq('user_id', userId);
      // Step 4: Delete team_invitations
      console.log('Deleting team_invitations...');
      await supabase.from('team_invitations').delete().eq('user_id', userId);
      // Step 5: Delete damage_notes
      console.log('Deleting damage_notes...');
      await supabase.from('damage_notes').delete().eq('user_id', userId);
      // Step 6: Delete damage_photos
      console.log('Deleting damage_photos...');
      await supabase.from('damage_photos').delete().eq('user_id', userId);
      // Step 7: Get all damage reports by this user
      console.log('Getting damage reports...');
      const { data: damageReports } = await supabase.from('damage_reports').select('id').eq('user_id', userId);
      if (damageReports && damageReports.length > 0) {
        const damageReportIds = damageReports.map((report)=>report.id);
        // Delete related records for each damage report
        console.log('Deleting related damage report records...');
        await supabase.from('damage_notes').delete().in('damage_report_id', damageReportIds);
        await supabase.from('damage_photos').delete().in('damage_report_id', damageReportIds);
      }
      // Step 8: Delete damage_reports
      console.log('Deleting damage_reports...');
      await supabase.from('damage_reports').delete().eq('user_id', userId);
      // Step 9: Get all purchase orders by this user
      console.log('Getting purchase orders...');
      const { data: purchaseOrders } = await supabase.from('purchase_orders').select('id').eq('user_id', userId);
      if (purchaseOrders && purchaseOrders.length > 0) {
        const purchaseOrderIds = purchaseOrders.map((order)=>order.id);
        // Delete related records for each purchase order
        console.log('Deleting purchase order items...');
        await supabase.from('purchase_order_items').delete().in('purchase_order_id', purchaseOrderIds);
      }
      // Step 10: Delete purchase_orders
      console.log('Deleting purchase_orders...');
      await supabase.from('purchase_orders').delete().eq('user_id', userId);
      // Step 11: Delete maintenance_providers
      console.log('Deleting maintenance_providers...');
      await supabase.from('maintenance_providers').delete().eq('user_id', userId);
      // Step 12: Delete inventory_items
      console.log('Deleting inventory_items...');
      await supabase.from('inventory_items').delete().eq('user_id', userId);
      // Step 13: Delete maintenance_tasks
      console.log('Deleting maintenance_tasks...');
      await supabase.from('maintenance_tasks').delete().eq('user_id', userId);
      // Step 14: Delete collections
      console.log('Deleting collections...');
      await supabase.from('collections').delete().eq('user_id', userId);
      // Step 15: Delete properties
      console.log('Deleting properties...');
      await supabase.from('properties').delete().eq('user_id', userId);
      // Step 16: Delete service_providers
      console.log('Deleting service_providers...');
      await supabase.from('service_providers').delete().eq('user_id', userId);
      // Step 17: Delete profiles
      console.log('Deleting profile...');
      await supabase.from('profiles').delete().eq('id', userId);
      // Step 18: Finally, delete the user from auth.users
      console.log('Deleting auth user...');
      const { error: deleteError } = await supabase.auth.admin.deleteUser(userId);
      if (deleteError) {
        throw deleteError;
      }
      return new Response(JSON.stringify({
        message: 'User deleted successfully'
      }), {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.error('Error in delete operation:', error);
      return new Response(JSON.stringify({
        error: `Database error deleting user: ${error.message}`
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: errorMessage
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
