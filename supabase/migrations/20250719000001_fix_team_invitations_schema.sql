-- Fix team_invitations table schema
-- Add missing accepted_at column and ensure proper constraints

-- Add accepted_at column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'team_invitations' 
        AND column_name = 'accepted_at'
    ) THEN
        ALTER TABLE team_invitations 
        ADD COLUMN accepted_at TIMESTAMPTZ;
    END IF;
END $$;

-- Ensure the table has all required columns
DO $$ 
BEGIN
    -- Add expires_at if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'team_invitations' 
        AND column_name = 'expires_at'
    ) THEN
        ALTER TABLE team_invitations 
        ADD COLUMN expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days');
    END IF;
    
    -- Add invited_by if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'team_invitations' 
        AND column_name = 'invited_by'
    ) THEN
        ALTER TABLE team_invitations 
        ADD COLUMN invited_by UUID REFERENCES auth.users(id);
    END IF;
END $$;

-- Update any existing accepted invitations to have accepted_at timestamp
UPDATE team_invitations 
SET accepted_at = updated_at 
WHERE status = 'accepted' AND accepted_at IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN team_invitations.accepted_at IS 'Timestamp when the invitation was accepted';
COMMENT ON COLUMN team_invitations.expires_at IS 'Timestamp when the invitation expires';
COMMENT ON COLUMN team_invitations.invited_by IS 'User ID of who sent the invitation';
