-- Fix the delete_property_cascade function with proper parameter naming
DROP FUNCTION IF EXISTS delete_property_cascade(UUID);

CREATE OR REPLACE FUNCTION delete_property_cascade(property_id_param UUID)
RETURNS void AS $$
BEGIN
    -- Delete maintenance tasks associated with the property
    DELETE FROM maintenance_tasks WHERE property_id = property_id_param;
    
    -- Delete inventory items associated with the property
    DELETE FROM inventory_items WHERE property_id = property_id_param;
    
    -- Delete team_properties associations
    DELETE FROM team_properties WHERE property_id = property_id_param;
    
    -- Delete damage photos and notes for damage reports of this property
    DELETE FROM damage_photos 
    WHERE damage_report_id IN (
        SELECT id FROM damage_reports WHERE property_id = property_id_param
    );
    
    DELETE FROM damage_notes 
    WHERE damage_report_id IN (
        SELECT id FROM damage_reports WHERE property_id = property_id_param
    );
    
    -- Delete damage reports
    DELETE FROM damage_reports WHERE property_id = property_id_param;
    
    -- Delete property files
    DELETE FROM property_files WHERE property_id = property_id_param;
    
    -- Finally, delete the property itself
    DELETE FROM properties WHERE id = property_id_param;
END;
$$ LANGUAGE plpgsql;
