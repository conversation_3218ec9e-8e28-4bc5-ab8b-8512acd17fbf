#!/bin/bash

# GitHub Secrets Setup Helper for Supabase Backup
# This script helps you identify the values needed for GitHub secrets

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Supabase Backup - GitHub Secrets Setup Helper${NC}"
echo "=================================================="
echo ""

echo -e "${YELLOW}This script will help you identify the values needed for GitHub secrets.${NC}"
echo -e "${YELLOW}You'll need to manually add these to your GitHub repository.${NC}"
echo ""

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   curl -sSL https://supabase.com/install.sh | sh"
    exit 1
fi

echo -e "${GREEN}✅ Supabase CLI found: $(supabase --version)${NC}"
echo ""

# Get project information
echo "🔍 Detecting project information..."
echo ""

# Check if already linked
if supabase projects list > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Already authenticated with Supabase${NC}"
    
    # Get current project reference
    PROJECT_REF=""
    if [ -f "supabase/config.toml" ]; then
        PROJECT_REF=$(grep -o 'project_id = "[^"]*"' supabase/config.toml | cut -d'"' -f2)
        if [ ! -z "$PROJECT_REF" ]; then
            echo -e "${GREEN}📁 Local project ID: $PROJECT_REF${NC}"
        fi
    fi
    
    # List available projects
    echo ""
    echo "📋 Available Supabase projects:"
    supabase projects list
    
else
    echo "❌ Not authenticated with Supabase. Please run:"
    echo "   supabase login"
    echo ""
    echo "Then run this script again."
    exit 1
fi

echo ""
echo "=================================================="
echo -e "${BLUE}📝 GitHub Secrets Configuration${NC}"
echo "=================================================="
echo ""

echo "Add these secrets to your GitHub repository:"
echo "Go to: Settings > Secrets and variables > Actions > New repository secret"
echo ""

echo -e "${YELLOW}1. SUPABASE_ACCESS_TOKEN${NC}"
echo "   - Go to: https://supabase.com/dashboard/account/tokens"
echo "   - Click 'Generate new token'"
echo "   - Copy the generated token value"
echo "   - This token should start with 'sbp_'"
echo ""

echo -e "${YELLOW}2. SUPABASE_PROJECT_REF${NC}"
if [ ! -z "$PROJECT_REF" ]; then
    echo -e "   - Value: ${GREEN}$PROJECT_REF${NC}"
else
    echo "   - Find your project reference ID"
    echo "   - It's in your project URL: https://supabase.com/dashboard/project/[PROJECT_REF]"
    echo "   - Or in the projects list above"
fi
echo ""

echo -e "${YELLOW}3. SUPABASE_DB_PASSWORD${NC}"
echo "   - Go to: Supabase Dashboard > Settings > Database"
echo "   - Find 'Database password' section"
echo "   - Use your database password (the one you set when creating the project)"
echo "   - If you forgot it, you can reset it in the dashboard"
echo ""

echo "=================================================="
echo -e "${BLUE}🧪 Testing Setup${NC}"
echo "=================================================="
echo ""

echo "After adding the secrets to GitHub:"
echo ""
echo "1. Go to your repository's 'Actions' tab"
echo "2. Find 'Supabase Backup' workflow"
echo "3. Click 'Run workflow' to test manually"
echo "4. Check the workflow run for any errors"
echo ""

echo "📋 The workflow will run automatically:"
echo "   - 2:00 AM UTC (Daily)"
echo "   - 8:00 AM UTC (Daily)" 
echo "   - 2:00 PM UTC (Daily)"
echo "   - 8:00 PM UTC (Daily)"
echo ""

echo "=================================================="
echo -e "${BLUE}📖 Additional Resources${NC}"
echo "=================================================="
echo ""
echo "📚 Documentation: docs/supabase-backup.md"
echo "🔧 Restoration script: scripts/restore-backup.sh"
echo "⚙️ Workflow file: .github/workflows/supabase-backup.yml"
echo ""

echo -e "${GREEN}✅ Setup helper completed!${NC}"
echo ""
echo "Next steps:"
echo "1. Add the three secrets to GitHub"
echo "2. Test the workflow manually"
echo "3. Check the first automatic backup run"
echo "4. Test restoration with a backup file"
