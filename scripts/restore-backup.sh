#!/bin/bash

# Supabase Backup Restoration Script
# Usage: ./restore-backup.sh <backup-file.tar.gz> [--dry-run]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_FILE="$1"
DRY_RUN="$2"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if backup file is provided
if [ -z "$BACKUP_FILE" ]; then
    log_error "Please provide a backup file"
    echo "Usage: $0 <backup-file.tar.gz> [--dry-run]"
    exit 1
fi

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    log_error "Backup file '$BACKUP_FILE' not found"
    exit 1
fi

log_info "Starting Supabase backup restoration process"
log_info "Backup file: $BACKUP_FILE"

if [ "$DRY_RUN" = "--dry-run" ]; then
    log_warning "DRY RUN MODE - No actual changes will be made"
fi

# Extract backup
TEMP_DIR=$(mktemp -d)
log_info "Extracting backup to temporary directory: $TEMP_DIR"

tar -xzf "$BACKUP_FILE" -C "$TEMP_DIR"
BACKUP_DIR=$(find "$TEMP_DIR" -maxdepth 1 -type d ! -path "$TEMP_DIR" | head -1)

if [ -z "$BACKUP_DIR" ]; then
    log_error "Could not find backup directory in archive"
    rm -rf "$TEMP_DIR"
    exit 1
fi

log_success "Backup extracted successfully"

# Read backup manifest
MANIFEST_FILE="$BACKUP_DIR/MANIFEST.md"
if [ -f "$MANIFEST_FILE" ]; then
    log_info "Reading backup manifest:"
    head -10 "$MANIFEST_FILE"
    echo ""
fi

# Read project info
PROJECT_INFO="$BACKUP_DIR/config/project_info.json"
if [ -f "$PROJECT_INFO" ]; then
    BACKUP_DATE=$(grep -o '"backup_date": "[^"]*"' "$PROJECT_INFO" | cut -d'"' -f4)
    PROJECT_REF=$(grep -o '"project_ref": "[^"]*"' "$PROJECT_INFO" | cut -d'"' -f4)
    
    log_info "Backup date: $BACKUP_DATE"
    log_info "Project reference: $PROJECT_REF"
fi

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    log_error "Supabase CLI is not installed. Please install it first."
    rm -rf "$TEMP_DIR"
    exit 1
fi

# Warning about destructive operation
if [ "$DRY_RUN" != "--dry-run" ]; then
    log_warning "This will overwrite your current Supabase project!"
    log_warning "Make sure you have a recent backup before proceeding."
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        log_info "Restoration cancelled"
        rm -rf "$TEMP_DIR"
        exit 0
    fi
fi

# Function to restore schema
restore_schema() {
    log_info "Restoring database schema..."
    
    if [ -f "$BACKUP_DIR/schema/public_schema.sql" ]; then
        if [ "$DRY_RUN" = "--dry-run" ]; then
            log_info "[DRY RUN] Would restore public schema"
        else
            log_info "Restoring public schema..."
            supabase db reset --linked
            cat "$BACKUP_DIR/schema/public_schema.sql" | supabase db push --linked
        fi
        log_success "Public schema restoration completed"
    else
        log_warning "Public schema backup not found"
    fi
}

# Function to restore data
restore_data() {
    log_info "Restoring database data..."
    
    if [ -f "$BACKUP_DIR/data/public_data.sql" ]; then
        if [ "$DRY_RUN" = "--dry-run" ]; then
            log_info "[DRY RUN] Would restore public data"
        else
            log_info "Restoring public data..."
            # Note: This requires direct psql access
            log_warning "Data restoration requires manual psql execution"
            log_info "Execute this command manually:"
            echo "psql -d \$DATABASE_URL -f \"$BACKUP_DIR/data/public_data.sql\""
        fi
        log_success "Data restoration prepared"
    else
        log_warning "Public data backup not found"
    fi
}

# Function to restore functions
restore_functions() {
    log_info "Restoring Edge Functions..."
    
    if [ -d "$BACKUP_DIR/functions/code" ]; then
        FUNCTION_COUNT=$(find "$BACKUP_DIR/functions/code" -maxdepth 1 -type d | wc -l)
        log_info "Found $((FUNCTION_COUNT - 1)) functions to restore"
        
        for func_dir in "$BACKUP_DIR/functions/code"/*; do
            if [ -d "$func_dir" ]; then
                func_name=$(basename "$func_dir")
                log_info "Restoring function: $func_name"
                
                if [ "$DRY_RUN" = "--dry-run" ]; then
                    log_info "[DRY RUN] Would deploy function: $func_name"
                else
                    # Copy function to local supabase directory
                    mkdir -p "supabase/functions"
                    cp -r "$func_dir" "supabase/functions/"
                    
                    # Deploy function
                    supabase functions deploy "$func_name" --project-ref "$PROJECT_REF"
                fi
                log_success "Function $func_name restored"
            fi
        done
    else
        log_warning "Functions backup not found"
    fi
}

# Function to restore configuration
restore_config() {
    log_info "Restoring project configuration..."
    
    if [ -f "$BACKUP_DIR/config/config.toml" ]; then
        if [ "$DRY_RUN" = "--dry-run" ]; then
            log_info "[DRY RUN] Would restore config.toml"
        else
            mkdir -p "supabase"
            cp "$BACKUP_DIR/config/config.toml" "supabase/"
            log_success "Configuration restored"
        fi
    else
        log_warning "Configuration backup not found"
    fi
}

# Main restoration process
main() {
    log_info "Starting restoration process..."
    
    # Create restoration log
    RESTORE_LOG="restore_$(date +%Y%m%d_%H%M%S).log"
    
    {
        echo "=== Supabase Backup Restoration Log ==="
        echo "Date: $(date)"
        echo "Backup file: $BACKUP_FILE"
        echo "Backup date: $BACKUP_DATE"
        echo "Project ref: $PROJECT_REF"
        echo "Dry run: $DRY_RUN"
        echo "======================================="
        echo ""
    } > "$RESTORE_LOG"
    
    # Perform restoration steps
    restore_config 2>&1 | tee -a "$RESTORE_LOG"
    restore_schema 2>&1 | tee -a "$RESTORE_LOG"
    restore_functions 2>&1 | tee -a "$RESTORE_LOG"
    restore_data 2>&1 | tee -a "$RESTORE_LOG"
    
    log_success "Restoration process completed!"
    log_info "Check the restoration log: $RESTORE_LOG"
    
    if [ "$DRY_RUN" != "--dry-run" ]; then
        log_info "Next steps:"
        echo "1. Verify your Supabase project is working correctly"
        echo "2. Test critical functionality"
        echo "3. Monitor for any issues"
        echo "4. Consider creating a new backup after verification"
    fi
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    rm -rf "$TEMP_DIR"
}

# Set up cleanup trap
trap cleanup EXIT

# Run main restoration process
main

log_success "Restoration script completed successfully!"
