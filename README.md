# StayFu - Property Management Platform

StayFuse is a property management platform built with Supabase, providing comprehensive tools for managing properties, teams, maintenance, and more.

## 🛡️ Backup & Disaster Recovery

This project includes an automated backup system that protects your Supabase data with 4x daily backups and 30-day retention.

### Quick Start
1. **Setup**: Run `./scripts/setup-github-secrets.sh` to configure GitHub secrets
2. **Test**: Run `./scripts/test-backup-local.sh` to verify backup functionality
3. **Monitor**: Check GitHub Actions for automatic backup status

### Features
- 🔄 **Automated Backups**: 4 times daily (2 AM, 8 AM, 2 PM, 8 PM UTC)
- 📦 **Complete Coverage**: Schema, data, functions, and configuration
- 🗂️ **30-Day Retention**: Automatic cleanup of old backups
- 🔧 **Easy Restoration**: One-command restoration from any backup

### Documentation
- 📚 [Full Backup Documentation](docs/supabase-backup.md)
- 🔧 [Restoration Guide](scripts/restore-backup.sh)
- ⚙️ [GitHub Action Workflow](.github/workflows/supabase-backup.yml)
