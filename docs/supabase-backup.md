# Supabase Backup & Restoration System

This repository includes a comprehensive backup and restoration system for your Supabase project, providing automated daily backups with 30-day retention.

## 🎯 Features

- **Automated Backups**: Runs 4 times daily (2 AM, 8 AM, 2 PM, 8 PM UTC)
- **Complete Coverage**: Schema, data, Edge Functions, and configuration
- **Long-term Retention**: 30 days of backup history
- **Easy Restoration**: Automated restoration scripts
- **GitHub Integration**: Stored as GitHub Action artifacts

## 📋 Setup Instructions

### 1. Configure GitHub Secrets

Add these secrets to your GitHub repository (`Settings > Secrets and variables > Actions`):

#### Required Secrets:

- **`SUPABASE_ACCESS_TOKEN`**
  - Get from: [Supabase Dashboard > Settings > Access Tokens](https://supabase.com/dashboard/account/tokens)
  - Click "Generate new token" and copy the value

- **`SUPABASE_PROJECT_REF`** 
  - Your project reference ID (e.g., `pwaeknalhosfwuxkpaet`)
  - Found in your Supabase project URL: `https://supabase.com/dashboard/project/[PROJECT_REF]`

- **`SUPABASE_DB_PASSWORD`**
  - Your database password
  - Found in: Supabase Dashboard > Settings > Database > Database password

### 2. Test the Backup System

#### Manual Trigger
1. Go to `Actions` tab in your GitHub repository
2. Select "Supabase Backup" workflow
3. Click "Run workflow" to test manually

#### Verify Scheduled Runs
The backup will automatically run at:
- 2:00 AM UTC
- 8:00 AM UTC  
- 2:00 PM UTC
- 8:00 PM UTC

## 📦 What Gets Backed Up

### Database Schema
- `public` schema (your application tables, functions, etc.)
- `auth` schema customizations
- `storage` schema modifications
- Custom extensions

### Application Data
- All data from public schema tables
- User authentication data (where accessible)
- Storage metadata

### Edge Functions
- Complete source code for all Edge Functions
- Function metadata and configurations
- Dependencies and shared modules

### Project Configuration
- `config.toml` settings
- Project metadata
- Backup manifest with restoration instructions

## 🔄 Restoration Process

### Quick Restoration

1. **Download Backup**
   ```bash
   # Download from GitHub Actions artifacts
   # Go to Actions > Supabase Backup > Select run > Download artifact
   ```

2. **Run Restoration Script**
   ```bash
   # Dry run (safe preview)
   ./scripts/restore-backup.sh backup-file.tar.gz --dry-run
   
   # Actual restoration (destructive!)
   ./scripts/restore-backup.sh backup-file.tar.gz
   ```

### Manual Restoration Steps

If you prefer manual restoration:

#### 1. Extract Backup
```bash
tar -xzf supabase-backup-YYYY-MM-DD_HH-MM-SS.tar.gz
cd YYYY-MM-DD_HH-MM-SS/
```

#### 2. Restore Schema
```bash
# Reset and restore schema
supabase db reset --linked
supabase db push --linked -f schema/public_schema.sql
```

#### 3. Restore Data
```bash
# Connect to your database and restore data
psql $DATABASE_URL -f data/public_data.sql
```

#### 4. Restore Functions
```bash
# Deploy each function
for func in functions/code/*/; do
  func_name=$(basename "$func")
  cp -r "$func" supabase/functions/
  supabase functions deploy "$func_name"
done
```

#### 5. Restore Configuration
```bash
# Copy configuration
cp config/config.toml supabase/
```

## 🗂️ Backup Structure

Each backup contains:

```
backup-YYYY-MM-DD_HH-MM-SS/
├── MANIFEST.md                 # Backup documentation
├── schema/
│   ├── public_schema.sql       # Main application schema
│   ├── auth_schema.sql         # Auth customizations
│   ├── storage_schema.sql      # Storage schema
│   └── extensions_schema.sql   # Extensions
├── data/
│   ├── public_data.sql         # Application data
│   ├── auth_data.sql           # User data
│   └── storage_data.sql        # Storage metadata
├── functions/
│   ├── functions_list.json     # Function inventory
│   └── code/                   # Function source code
│       ├── function-1/
│       ├── function-2/
│       └── ...
└── config/
    ├── config.toml             # Supabase configuration
    └── project_info.json       # Backup metadata
```

## 🛡️ Disaster Recovery Scenarios

### Complete Project Loss
1. Create new Supabase project
2. Update secrets with new project reference
3. Run restoration script with latest backup
4. Update application connection strings

### Data Corruption
1. Download recent backup
2. Restore only data files to clean database
3. Verify data integrity

### Function Issues
1. Download backup with working functions
2. Restore specific functions or all functions
3. Test functionality

### Configuration Rollback
1. Download backup from before configuration changes
2. Restore only configuration files
3. Restart services if needed

## 📊 Monitoring & Maintenance

### Backup Status
- Check GitHub Actions tab for backup status
- Review backup artifacts for completeness
- Monitor backup file sizes for anomalies

### Cleanup
- GitHub automatically deletes artifacts after 30 days
- No manual cleanup required
- Adjust retention in workflow file if needed

### Troubleshooting

#### Common Issues:

**Authentication Failed**
- Verify `SUPABASE_ACCESS_TOKEN` is valid
- Check token hasn't expired
- Ensure token has required permissions

**Permission Denied**
- Verify `SUPABASE_DB_PASSWORD` is correct
- Check database access permissions
- Ensure CLI has proper project access

**Function Download Failed**
- Some functions may have deployment restrictions
- Check function status in Supabase dashboard
- Verify function names in logs

## 🔧 Customization

### Adjust Backup Frequency
Edit `.github/workflows/supabase-backup.yml`:
```yaml
schedule:
  # Example: Every 6 hours
  - cron: '0 */6 * * *'
  
  # Example: Once daily at midnight
  - cron: '0 0 * * *'
```

### Change Retention Period
Edit the workflow file:
```yaml
retention-days: 60  # Keep for 60 days instead of 30
```

### Add Custom Backup Steps
Add additional steps in the workflow for:
- Environment variables backup
- Storage bucket content backup
- External integrations backup

## 📞 Support

- Check GitHub Actions logs for detailed error messages
- Review Supabase CLI documentation
- Verify all secrets are properly configured
- Test restoration process regularly

---

**⚠️ Important Notes:**
- Always test restoration in a non-production environment first
- Restoration is a destructive operation that will overwrite existing data
- Keep your access tokens secure and rotate them regularly
- Monitor backup success and investigate any failures promptly
