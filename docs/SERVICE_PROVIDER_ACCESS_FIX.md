# Service Provider Maintenance Task Access Fix

## Problem
Service providers were seeing maintenance tasks from properties they shouldn't have access to, even when no properties were assigned to their team.

## Root Cause
The `get_maintenance_tasks_for_user` RPC function had overly permissive logic for service providers. It was returning tasks based on:
1. `user_id = p_user_id` (tasks they created)
2. `provider_id = p_user_id` (tasks assigned to them as provider)
3. `assigned_to = p_user_id` (tasks specifically assigned to them)

**The problem:** It wasn't verifying that the service provider had team access to the properties associated with those tasks.

## Impact
- Service providers could see maintenance tasks from any property in the system if they were assigned to those tasks
- This violated the team-based access control model
- Privacy and security concern as users could see data they shouldn't access

## Solution Implemented

### 1. Database Migration (`20250708000003_fix_service_provider_maintenance_access.sql`)
- Updated the `get_maintenance_tasks_for_user` function to properly check team membership
- Modified RLS policy to enforce team-based access control
- Service providers now only see tasks where they have team access to the associated property

### 2. Client-Side Safety Filter
- Added additional filtering in `useMaintenanceTasksQueryV2.ts`
- Validates service provider team memberships before showing tasks
- Acts as a safety net until database migration is fully deployed

### 3. New Logic for Service Providers
Service providers can now only see tasks where:
1. They created the task themselves, OR
2. They are assigned to the task AND have team membership that gives them access to the property
3. They are the provider for the task AND have team membership that gives them access to the property

## Files Changed
1. `/supabase/migrations/20250708000003_fix_service_provider_maintenance_access.sql` - Database migration
2. `/src/hooks/useMaintenanceTasksQueryV2.ts` - Client-side filtering
3. `/SERVICE_PROVIDER_ACCESS_FIX.md` - This documentation

## Testing
To test the fix:
1. Create a service provider user
2. Add them to a team with specific properties
3. Create maintenance tasks for both team properties and non-team properties
4. Assign some tasks to the service provider
5. Verify they only see tasks for properties their team has access to

## Migration Deployment
Run the migration with:
```bash
npx supabase db push
```

## Verification
After deployment, service providers should only see:
- Tasks they created themselves
- Tasks assigned to them where they have team access to the property
- No tasks from properties outside their team's scope